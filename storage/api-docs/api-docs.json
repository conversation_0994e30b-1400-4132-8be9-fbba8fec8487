{"openapi": "3.0.0", "info": {"title": "GIPA - API", "contact": {"email": ""}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000/api", "description": "GIPA - API"}], "paths": {"/curricula": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "List of curricula", "description": "List of curricula", "operationId": "50a614606e937226a6562a96cf00a3e0", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_ka": {"description": "String", "type": "string"}, "name_en": {"description": "String", "type": "string"}}, "type": "object", "example": {"id": 1, "name_ka": "quisquam aliquam", "name_en": "id est", "program_id": 9, "is_active": 1, "created_at": "2022-07-06T21:06:00.000000Z", "updated_at": "2022-07-06T21:06:00.000000Z", "program": {"id": 9, "name_ka": "Chasity", "name_en": "Rice", "school_id": 12, "academic_degree_id": 13, "created_at": "2022-07-06 21:06", "updated_at": "2022-07-06 21:06", "deleted_at": null}}}}}}, "400": {"description": "Bad request"}}}}, "/permissions": {"get": {"tags": ["Permissions"], "summary": "List of permissions", "description": "List of permissions", "operationId": "4fa8c35c418a01b19b140677b9253490", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"title": {"description": "String", "type": "string"}}, "type": "object", "example": {"name": "test name", "program": "related object"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Permissions"], "summary": "Add Permission", "description": "Add Permission", "operationId": "acabc9909a6cfe85f5cb41bd943eee6c", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StorePermissionRequest", "required": ["title"], "properties": {"title": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Permission created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/permissions/{id}": {"get": {"tags": ["Permissions"], "summary": "Show permission", "description": "Show permission", "operationId": "e6b187791d15b61e4e6f20951d2f7b59", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"title": {"description": "String", "type": "string"}}, "type": "object", "example": {"title": "test title"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Permissions"], "summary": "Update Permission", "description": "Update Permission", "operationId": "74db7ab2a01b393475467548ec46efbf", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateFlowRequest", "required": ["title"], "properties": {"title": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Permission updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Permissions"], "summary": "Delete permission", "description": "Delete permission", "operationId": "a810ee150f62fb611c0aeead76d19a00", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete permission", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/administration-items": {"get": {"tags": ["Administration Items"], "summary": "List of administration items", "description": "List of administrator items", "operationId": "2a69a7d84cc322554e289973a292716c", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Administration Items"], "summary": "Add Administration Item", "description": "Add Administration Item", "operationId": "09881bdbbb12875c553f0470ce428f00", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreAdministrationItemRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Administration Item created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/administration-items/{id}": {"get": {"tags": ["Administration Items"], "summary": "Show administration item", "description": "Show administration item", "operationId": "710aa88fdb636bf6a576e6f5db6b8008", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Administration Items"], "summary": "Update administration item", "description": "Update administration item", "operationId": "15264a79e3f9414b3a6af1fbbb6ca761", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateAdministrationItemRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Administration item updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Administration Items"], "summary": "Delete administration item", "description": "Delete administration item", "operationId": "62e1aa44899a19b1f9462b35b1f9d8b0", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete administration item", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/administration-positions": {"get": {"tags": ["Administration Positions"], "summary": "List of administration positions", "description": "List of administrator positions", "operationId": "e95a50ed2000511aa4b22e7619ff62ab", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Administration Positions"], "summary": "Add Administration Position", "description": "Add Administration Position", "operationId": "b0f4b7f27ec3762a9b7610a588b3b69f", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreCampusRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Administration Position created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/administration-positions/{id}": {"get": {"tags": ["Administration Positions"], "summary": "Show administration position", "description": "Show administration position", "operationId": "bfd4899eb9cbc4dd8ef2e581a89ac5ab", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Administration Positions"], "summary": "Update administration position", "description": "Update administration position", "operationId": "7bcb8ae26033eb9ee48559ad7ae5ca50", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateCampusRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Administration position updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Administration Positions"], "summary": "Delete administration position", "description": "Delete administration position", "operationId": "9dfa5ce8d22a24ba21a4832f2d43c483", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete administration position", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/campuses": {"get": {"tags": ["Campus"], "summary": "List of campuses", "description": "List of campuses", "operationId": "73bf00b583b0a2e63c938b70572a2c8b", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "address_en": {"description": "String", "type": "string"}, "address_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "address_ka": "address ka", "address_en": "address en"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Campus"], "summary": "Add Campuse", "description": "Add Campuse", "operationId": "7c214174668e21af83e7ae5aa64ea75c", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreCampusRequest", "required": ["name_en", "name_ka", "address_ka", "address_en"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}, "address_ka": {"type": "string"}, "address_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Campus created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/campuses/{id}": {"get": {"tags": ["Campus"], "summary": "Show campus", "description": "Show campus", "operationId": "ae22c4e4838e0e1e43602c1c6688c87d", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "address_en": {"description": "String", "type": "string"}, "address_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "address_ka": "address ka", "address_en": "address en"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Campus"], "summary": "Update Campuse", "description": "Update Campuse", "operationId": "16b09320315c144324f0646810a63553", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateCampusRequest", "required": ["name_en", "name_ka", "address_en", "address_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}, "address_ka": {"type": "string"}, "address_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Campus updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Campus"], "summary": "Delete campus", "description": "Delete campus", "operationId": "6153e0205c08ef2d7cfe3ff073021ea5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete campus", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/directions": {"get": {"tags": ["Directions"], "summary": "List of directions", "description": "List of directions", "operationId": "a1c5ea2639d629f1c9dc5b01e457a0f5", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Directions"], "summary": "Add Direction", "description": "Add Direction", "operationId": "9b0fe7827266ec6867596951ba8bd8b5", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreDirectionRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Direction created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/directions/{id}": {"get": {"tags": ["Directions"], "summary": "Show direction", "description": "Show direction", "operationId": "5eed61f438282e755e29547daa06f6f8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Directions"], "summary": "Update Direction", "description": "Update Direction", "operationId": "cc724a874992dfdc8c8146cc52a8f8fb", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateDirectionRequest", "required": ["name_en", "name_ka"], "properties": {"name_ka": {"type": "string"}, "name_en": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Direction updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Directions"], "summary": "Delete direction", "description": "Delete direction", "operationId": "ff34ae6d9321d24303a9d582ef6b3ec7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete direction", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/flows": {"get": {"tags": ["Flows"], "summary": "List of flows", "description": "List of flows", "operationId": "bd86588e64d67208bfbd945535614204", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name": {"description": "String", "type": "string"}, "program_id": {"description": "Related program", "type": "integer"}}, "type": "object", "example": {"name": "test name", "program": "related object"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Flows"], "summary": "Add Flow", "description": "Add Flow", "operationId": "4989eafd377478eb202e44965329b1f1", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreFlowRequest", "required": ["name", "program_id"], "properties": {"name": {"type": "string"}, "program_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Flow created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/flows/{id}": {"get": {"tags": ["Flows"], "summary": "Show flow", "description": "Show flow", "operationId": "15e1f81af0f2f2a633ac7c97546156e2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name": {"description": "String", "type": "string"}, "program_id": {"description": "Related program", "type": "integer"}}, "type": "object", "example": {"name": "test name", "program": "related object"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Flows"], "summary": "Update Flow", "description": "Update Flow", "operationId": "7630b211318c482a7778cf1729790745", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateFlowRequest", "required": ["name", "program_id"], "properties": {"name": {"type": "string"}, "program_id": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Flow updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Flows"], "summary": "Delete flows", "description": "Delete flows", "operationId": "32cab782415c5a84499920382aa90c8c", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete flow", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/programs": {"get": {"tags": ["Programs"], "summary": "List of programs", "description": "List of programs", "operationId": "a7a3348264b400e5c0673f684dd3a86c", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "school_id": {"description": "School relation", "type": "integer"}, "program_type_id": {"description": "Program type relation", "type": "integer"}, "academic_degree_id": {"description": "Academic degree relation", "type": "integer"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "school": "related object", "program_type": "related object", "academic_degree": "related object"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Programs"], "summary": "Add Program", "description": "Add Program", "operationId": "8c67abbb610a2893031dbaa511e0921e", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreProgramRequest", "required": ["name_en", "name_ka", "program_type_id", "school_id", "academic_degree_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "program_type_id": {"type": "integer"}, "school_id": {"type": "integer"}, "academic_degree_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Program created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/programs/{id}": {"get": {"tags": ["Programs"], "summary": "Show program", "description": "Show program", "operationId": "73815279a47272e1c3044d4b04684ad3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "school_id": {"description": "related object", "type": "integer"}, "program_type_id": {"description": "related object", "type": "integer"}, "academic_degree_id": {"description": "related object", "type": "integer"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "school_id": 1, "program_type_id": 1, "academic_degree_id": 1}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Programs"], "summary": "Update Program", "description": "Update Program", "operationId": "de9767bb8e47c823cd2f264581ce1fbc", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateProgramRequest", "required": ["name_en", "name_ka", "program_type_id", "school_id", "academic_degree_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "program_type_id": {"type": "integer"}, "school_id": {"type": "integer"}, "academic_degree_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Program updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Programs"], "summary": "Delete program", "description": "Delete program", "operationId": "ec8bf60c557231f24dde53dadbd93e33", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete program", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/schools": {"get": {"tags": ["Schools"], "summary": "List of schools", "description": "List of schools", "operationId": "eef40ec5bfce36b8c8cbdf68a0d6a559", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "address_en": {"description": "String", "type": "string"}, "address_ka": {"description": "String", "type": "string"}, "campus_id": {"description": "related object", "type": "integer"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "address_en": "test address en", "address_ka": "test address ka", "campus_id": 1}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Schools"], "summary": "Add School", "description": "Add School", "operationId": "3b199d976ecf6f7c74dbc94a0364ceca", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreProgramRequest", "required": ["name_en", "name_ka", "campus_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "campus_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Program created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/schools/{id}": {"get": {"tags": ["Schools"], "summary": "Show school", "description": "Show school", "operationId": "ac5075694a88affaa280eea8f8e57dae", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "campus_id": {"description": "Campus relation", "type": "integer"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "campus": "related object"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Schools"], "summary": "Update School", "description": "Update School", "operationId": "7d86db874edef6309468e7bc3c767302", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateSchoolRequest", "required": ["name_en", "name_ka", "campus_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "campus_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "School updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Schools"], "summary": "Delete school", "description": "Delete school", "operationId": "b2623c32e04ded932db5b5330aec6530", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete school", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/student-groups": {"get": {"tags": ["Student groups"], "summary": "List of student groups", "description": "List of student groups", "operationId": "5acaa11c3f06dff20ed8ac37063ddd95", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "program_id": {"description": "Program relation", "type": "integer"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "program": "related object"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Student groups"], "summary": "Add student group", "description": "Add student group", "operationId": "b2bd7894d7faa10988e1c3496c3227d2", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreStudentGroupRequest", "required": ["name_en", "name_ka", "program_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "program_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Student group created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/student-groups/{id}": {"get": {"tags": ["Student groups"], "summary": "Show student group", "description": "Show student group", "operationId": "e92873e4518e0bf61f2a8c70294a3eb3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "program_id": {"description": "String", "type": "object"}}, "type": "object", "example": {"name_en": "test name en", "name_ka": "test name ka", "program": "related object"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Student groups"], "summary": "Update student groups", "description": "Update student groups", "operationId": "aa97e87419413c6b477bf617f2089a30", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "UpdateSchoolRequest", "required": ["name_en", "name_ka", "program_id"], "properties": {"name_en": {"type": "string"}, "name_ka": {"type": "string"}, "program_id": {"type": "integer"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Student group updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Student groups"], "summary": "Delete student group", "description": "Delete student group", "operationId": "e54fea9518e7f6bed303f0fb61ab317d", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete student group", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/roles": {"get": {"tags": ["Roles"], "summary": "List of roles", "description": "List of roles", "operationId": "63e4aa32f39c8f0290f206db53c2d569", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"title": {"description": "String", "type": "string"}, "permissions": {"description": "String", "type": "string"}}, "type": "object", "example": {"name": "test name", "permissions": "list of permissions for role"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Roles"], "summary": "Add role", "description": "Add role", "operationId": "5154c1da16300c5ffd52e1b2f19a9676", "requestBody": {"content": {"multipart/form-data": {"schema": {"schema": "StoreRoleRequest", "required": ["title", "permissions"], "properties": {"title": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "integer"}}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Role created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/roles/{id}": {"get": {"tags": ["Roles"], "summary": "Show role", "description": "Show role", "operationId": "4adc01fe60c942172266ec817bb78d31", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"title": {"description": "String", "type": "string"}, "permissions": {"description": "String", "type": "string"}}, "type": "object", "example": {"title": "test title", "permissions": "list of permissions for role"}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Roles"], "summary": "Update Role", "description": "Update Role", "operationId": "925ec90d740e2db07af3455682318fe9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"schema": "StoreUserDriverLicenseRequest", "required": ["title", "permissions"], "properties": {"title": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "integer"}}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"200": {"description": "Role updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Roles"], "summary": "Delete role", "description": "Delete role", "operationId": "665d9fbf3bee59d34203741f4294b10b", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Delete role", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}, "/syllabi": {"get": {"tags": ["Syllabi"], "summary": "List of syllabi", "description": "List of syllabi", "operationId": "d74a88fc0d2fc863757ea891e715dee6", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name": {"description": "String", "type": "string"}, "flow_id": {"description": "String", "type": "string"}, "curriculum_id": {"description": "String", "type": "string"}, "status_id": {"description": "String", "type": "string"}, "semester_id": {"description": "String", "type": "string"}, "academic_degree_id": {"description": "String", "type": "string"}, "code": {"description": "String", "type": "string"}, "credits": {"description": "String", "type": "string"}, "hours": {"description": "String", "type": "string"}, "goal": {"description": "String", "type": "string"}, "methods": {"description": "String", "type": "string"}, "assessing_system": {"description": "String", "type": "string"}, "final_exam_prerequisite": {"description": "String", "type": "string"}, "assessing_components": {"description": "String", "type": "string"}, "retake_missed_assignment": {"description": "String", "type": "string"}, "exam_rules": {"description": "String", "type": "string"}, "academic_honesty": {"description": "String", "type": "string"}, "learn_results": {"description": "String", "type": "string"}, "additional_information": {"description": "String", "type": "string"}}, "type": "object", "example": {"id": 1, "name": "Rerum earum vitae libero.", "flow_id": 2, "curriculum_id": null, "status_id": 1, "semester_id": 1, "academic_degree_id": 1, "code": "DrilSWrD", "credits": 6, "hours": "12", "goal": "Est aliquid et velit aut pariatur. Quod nostrum ullam reiciendis rerum. Nemo nam deleniti a quos ullam dolores. Cupiditate hic sed consequatur autem beatae incidunt harum.", "methods": "Non est et aut est. Voluptas animi modi dolorem adipisci libero.", "assessing_system": "Dolorum fuga ea aperiam quaerat. Neque explicabo sed nihil et tempore consequuntur. Iusto repellat dolorem quis delectus aliquam. Et animi iste asperiores placeat dolorem. Et nulla ut adipisci rerum dolorum.", "final_exam_prerequisite": "Ab blanditiis dolorem quia reiciendis vel tempora. Adipisci iure aut distinctio magni. Quibusdam in autem consequatur adipisci quia ut.", "assessing_components": "Qui dignissimos quia nostrum voluptas velit ut sequi. Qui et veniam est accusantium ad dolor numquam. Et sed mollitia nobis autem et culpa consequatur.", "retake_missed_assignment": "A id est est rem qui harum aspernatur qui. Ut reprehenderit quibusdam quis est neque vel. Rerum voluptate accusamus in accusamus perspiciatis est dolor nihil. Qui est voluptatem facilis commodi.", "exam_rules": "Odit dolore porro id dolorum eum repellat omnis. Qui aut voluptas autem velit voluptatem odio. Et omnis delectus eos et explicabo. Qui error praesentium rem explicabo nesciunt aliquid itaque vel.", "academic_honesty": "Ratione modi beatae facilis perferendis omnis. Odit est modi et quo ut. Id delectus corporis debitis qui id. Eos iste praesentium quaerat et est.", "learn_results": "Nostrum officiis quo harum et laudantium rerum eos. Cum eaque aspernatur consequuntur aut. Cumque ab asperiores consequatur mollitia vitae debitis. Iure et fugiat aut at beatae accusantium rerum qui.", "additional_information": "Qui rerum enim minus dolor consectetur. Exercitationem dolorum architecto quidem. Ducimus et quia blanditiis voluptatem nulla repellendus cumque.", "deleted_at": null, "created_at": "2022-07-04T18:45:26.000000Z", "updated_at": "2022-07-04T18:45:26.000000Z", "flow": {"id": 2, "name": "<PERSON><PERSON><PERSON>"}, "status": {"id": 1, "name_ka": "სავალდებულო"}, "semester": {"id": 1, "name": "V"}, "lecturers": "array", "weeks": "array", "assignments": "array", "prerequisites": "array", "academic_degree": "array"}}}}}, "400": {"description": "Bad request"}}}, "post": {"tags": ["Syllabi"], "summary": "Add Syllabus", "description": "Add Syllabus", "operationId": "9cb0fa4318d36fcabadee35dfb738455", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["name", "flow_id", "curriculum_id", "status_id", "semester_id", "academic_degree_id", "code", "credits", "hours", "goal", "methods", "assessing_system", "final_exam_prerequisite", "assessing_components", "retake_missed_assignment", "exam_rules", "academic_honesty", "learn_results", "additional_information"], "properties": {"name": {"type": "string"}, "flow_id": {"type": "integer"}, "curriculum_id": {"type": "integer"}, "status_id": {"type": "integer"}, "semester_id": {"type": "integer"}, "academic_degree_id": {"type": "integer"}, "code": {"type": "string"}, "credits": {"type": "string"}, "hours": {"type": "string"}, "goal": {"type": "string"}, "methods": {"type": "string"}, "assessing_system": {"type": "string"}, "final_exam_prerequisite": {"type": "string"}, "assessing_components": {"type": "string"}, "retake_missed_assignment": {"type": "string"}, "exam_rules": {"type": "string"}, "academic_honesty": {"type": "string"}, "learn_results": {"type": "string"}, "additional_information": {"type": "string"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Syllabus created!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}}, "/syllabi/create": {"get": {"tags": ["Syllabi"], "summary": "Create syllabus", "description": "create syllabus", "operationId": "2792e0908ac5f58e4e61ef4cdde38633", "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}, "address_en": {"description": "String", "type": "string"}, "address_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"flows": {"1": "Alvera", "2": "<PERSON><PERSON><PERSON>"}, "statuses": {"1": "სავალდებულო", "2": "არჩევითი", "3": "სავალდებულო არჩევითი"}, "semesters": {"1": "V"}, "lecturers": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "identity_number": 3, "card_number": 0, "address": "3946 Wayne Haven\\nEast Tom, NC 37170-8752", "phone": "+****************", "date_of_birth": "2010-01-25", "email": "<EMAIL>", "photo": "t", "academic_degree_id": 4, "type": 1, "affiliated": 1, "cv": "b", "do_lectures_another_universities": 1, "created_at": "2022-07-04T18:45:26.000000Z", "updated_at": "2022-07-04T18:45:26.000000Z", "deleted_at": null}, "weeks": "array", "assignments": "array", "prerequisites": {"1": "Rerum earum vitae libero.", "2": "Est quis sed hic assumenda sapiente quia sed.", "3": "Quod unde quibusdam ratione error accusantium velit consequatur."}}}}}}, "400": {"description": "Bad request"}}}}, "/syllabi/{id}": {"get": {"tags": ["Syllabi"], "summary": "Show syllabus", "description": "Show syllabus", "operationId": "49f595e97491ff0d6d728b82a68ea201", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "ok", "content": {"application/json": {"schema": {"properties": {"name_en": {"description": "String", "type": "string"}, "name_ka": {"description": "String", "type": "string"}}, "type": "object", "example": {"id": 2, "name": "Est quis sed hic assumenda sapiente quia sed.", "flow_id": 2, "curriculum_id": null, "status_id": 1, "semester_id": 1, "academic_degree_id": 1, "code": "qjqMIckd", "credits": 6, "hours": "12", "goal": "Iste et laboriosam provident ad voluptas. Autem nulla perspiciatis iusto non sit. Et libero eos pariatur libero assumenda dolores consequuntur. Nihil veniam assumenda omnis perferendis.", "methods": "Animi qui exercitationem necessitatibus. Officiis voluptas at autem quo iusto eius adipisci. Quo molestiae voluptatibus temporibus magni quia optio tempora. Dicta perferendis veniam maxime ut.", "assessing_system": "Perferendis eos iure maiores saepe aut. <PERSON>eniti sit quam alias dolores autem non dolorem. Corrupti ipsam occaecati eos fugit. Sunt aliquid dolores eum cum.", "final_exam_prerequisite": "Cumque vero dolorum saepe hic. Non facere enim architecto odit. Dolores magni consequatur eum corrupti placeat.", "assessing_components": "Ullam perspiciatis illum at sunt asperiores enim. Esse molestiae enim temporibus qui.", "retake_missed_assignment": "Est sed earum id voluptatem culpa. Dicta consequuntur corrupti dolorem. Ut inventore ut sed deleniti tempore labore consequatur. Aperiam ad quis id quo quasi quo nemo explicabo.", "exam_rules": "Natus minus dignissimos nemo. Iusto accusantium ut ratione suscipit facere omnis ab. Modi dicta rerum harum. Tempora sit eius doloribus mollitia facilis cupiditate et.", "academic_honesty": "Error qui aut voluptatum consequuntur excepturi autem. Veniam perferendis nisi ab corrupti similique non. Quo aliquid dolorem neque incidunt.", "learn_results": "Eum in iure harum culpa cum et quos nisi. Fugit reprehenderit quaerat qui qui impedit. Cum eos quaerat ducimus error sed consectetur sint. Non ea excepturi quasi sed ratione deserunt blanditiis quia.", "additional_information": "Voluptatem numquam ipsam libero dolore est. Iusto odit adipisci ut voluptas aperiam. Ipsam fugiat non voluptas ipsum quisquam.", "deleted_at": null, "created_at": "2022-07-04T18:45:26.000000Z", "updated_at": "2022-07-04T18:45:26.000000Z", "flow": {"id": 2, "name": "<PERSON><PERSON><PERSON>", "program_id": 8, "deleted_at": null, "created_at": "2022-07-04T18:45:26.000000Z", "updated_at": "2022-07-04T18:45:26.000000Z"}, "curriculum": null, "status": {"id": 1, "name_ka": "სავალდებულო", "name_en": "mandatory", "created_at": null, "updated_at": null}, "semester": {"id": 1, "name": "V", "created_at": null, "updated_at": null}, "lecturers": "array", "weeks": "array", "assignments": "array", "prerequisites": "array", "academic_degree": {"id": 1, "name_en": "Bachelor", "name_ka": "ბაკალავრიატი", "deleted_at": null, "created_at": null, "updated_at": null}}}}}}, "400": {"description": "Bad request"}}}, "put": {"tags": ["Syllabi"], "summary": "Update Syllabus", "description": "Update Syllabus", "operationId": "ce4001999ab49918fd28ae9fba105d1c", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"required": ["name", "flow_id", "curriculum_id", "status_id", "semester_id", "academic_degree_id", "code", "credits", "hours", "goal", "methods", "assessing_system", "final_exam_prerequisite", "assessing_components", "retake_missed_assignment", "exam_rules", "academic_honesty", "learn_results", "additional_information"], "properties": {"name": {"type": "string"}, "flow_id": {"type": "integer"}, "curriculum_id": {"type": "integer"}, "status_id": {"type": "integer"}, "semester_id": {"type": "integer"}, "academic_degree_id": {"type": "integer"}, "code": {"type": "string"}, "credits": {"type": "string"}, "hours": {"type": "string"}, "goal": {"type": "string"}, "methods": {"type": "string"}, "assessing_system": {"type": "string"}, "final_exam_prerequisite": {"type": "string"}, "assessing_components": {"type": "string"}, "retake_missed_assignment": {"type": "string"}, "exam_rules": {"type": "string"}, "academic_honesty": {"type": "string"}, "learn_results": {"type": "string"}, "additional_information": {"type": "string"}, "lecturer_ids": {"type": "object"}, "prerequisites_ids": {"type": "object"}}, "type": "object"}}, "application/json": {"schema": {}}}}, "responses": {"201": {"description": "Syllabus updated!", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Bad request"}, "404": {"description": "Resource Not Found"}}}, "delete": {"tags": ["Syllabi"], "summary": "Delete syllabus", "description": "Delete syllabus", "operationId": "d2247aa4815ec88323e0118a503f948f", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Syllabus trashed", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Id field is required!"}}}}}, "components": {"securitySchemes": {"X-APP-ID": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-APP-ID", "in": "header"}}}, "tags": [{"name": "GIPA - API Documentation"}]}