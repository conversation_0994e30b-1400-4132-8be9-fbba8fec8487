* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    font-weight: normal;
    font-size: 1rem;
}

.wrapper {
    /*padding-left: 48px;*/
    /*padding-right: 48px;*/
    position: relative;
    margin-top: 2rem;
}

.header {
    align-items: center; /* Aligns items vertically in the center */
    justify-content: space-between; /* Distributes space between elements */
}

.header .image-box {
    float:left;
    width: 30%;
}

.header .header-container {
    float: right;
    width: 70%;
}

.heading-container h1 {
    margin: 0; /* Removes default margin from h1 */
    padding: 10px; /* Adjust if needed */
    font-size: 14px;
    border-top: 3px solid rgb(158, 36, 89);
    border-bottom: 3px solid rgb(158, 36, 89);

}

/*.header h1 {*/
/*    font-size: 16px;*/
/*    border-top: 3px solid rgb(158, 36, 89);*/
/*    border-bottom: 3px solid rgb(158, 36, 89);*/
/*    padding-top: 0.5rem;*/
/*    padding-bottom: 0.5rem;*/
/*}*/

.body-wrapper {
    /*padding-left: 48px;*/
    /*padding-right: 48px;*/
}

.body-wrapper p {
    margin-bottom: 1rem;
    font-size: 12px;
}

.divider {
    padding: 1rem;
}

table tbody tr td {
    padding: 0.5rem;
    font-size: 12px;
}

table thead tr th {
    padding: 0.5rem;
    font-size: 12px;
    font-weight: bold;
}

.date-time {
    margin-bottom: 2rem;
}

.date-time .image-box {
    float: left;
    width: 30%;
    font-weight: bold;
}

.date-time .image-box-right {
    float: right;
    text-align: right;
    width: 70%;
    font-weight: bold;
}

.date-time p {
    margin-bottom: 0;
    font-weight: bold;
}

.footer {

    border-top: 3px solid rgb(158, 36, 89);

}

.col-footer {
    float: left;
    width: 33.33%;
}

.col-footer p {
    font-size: 12px !important;
}

.span {
    font-size: 12px;
}

.section-container {
    overflow: hidden; /* Clearfix for containing floats */
}

.left-content {
    float: left;
    width: 60%;
}
.right-content {
    display: inline-block;
    vertical-align: top;
    float: left;
    width: 40%; /* Adjust the width as needed */
    box-sizing: border-box;
}
.center {
    text-align: center !important;
    font-weight: bold;
}
