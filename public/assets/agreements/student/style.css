h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 1rem;
}
ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.wrapper {
    max-width: 1170px;
    width: 100%;
    margin: 4rem auto;
}
.padding-x {
    padding: 0 20px;
}
.text-center {
    text-align: center;
}
.text-justify {
    text-align: justify;
}

.text-sm {
    font-size: 0.85rem;
}

.text-base {
    font-size: 1rem;
}

.text-lg {
    font-size: 18px;
}

.text-xl {
    font-size: 20px;
}
.text-2xl {
    font-size: 24px;
}
.text-danger {
    color: #960000;
}
.text-highlight {
    color: #ffff00;
}
.bg-orange {
    background-color: #ffff00;
}

.flex {
    display: flex;
}
.inline-block {
    display: inline-block;
}
.block {
    display: block;
}
.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pt-4 {
    padding-top: 1rem;
}
.pt-8 {
    padding-top: 2rem;
}
.pt-12 {
    padding-top: 3rem;
}
.pt-16 {
    padding-top: 4rem;
}
.pb-4 {
    padding-bottom: 1rem;
}
.pb-8 {
    padding-bottom: 2rem;
}
.text-height {
    line-height: 1.5rem;
}
.text-height-lg {
    line-height: 1.75rem;
}
.font-bold {
    font-weight: bold;
}
.list-item {
    padding-bottom: 12px;
    position: relative;
    padding-left: 2.5rem;
}
.list-item:before {
    content: "";
    display: block;
    position: absolute;
    top: 0px;
    left: 0;
}
.list-item-child,
.list-item-child-second {
    padding-left: 5rem;
    position: relative;
    padding-bottom: 12px;
}
.list-item-child:before,
.list-item-child-second:before {
    content: "2.1.1";
    display: block;
    position: absolute;
    top: 0px;
    left: 2rem;
}

.list-item-third {
    padding-left: 8.5rem;
    position: relative;
    padding-bottom: 12px;
}

.list-item-third:before,
.list-item-third-second:before {
    content: "2.1.1";
    display: block;
    position: absolute;
    top: 0px;
    left: 5rem;
}

.w-full {
    width: 100%;
}

.border {
    border: 1px solid #222;
}
.border-collapse {
    border-collapse: collapse;
}
.text-left {
    text-align: left;
}
.p-2 {
    padding: 0.5rem;
}
.p-4 {
    padding: 1rem;
}

table th,
table td {
    width: 50%;
}
