body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 6px;
    padding-left: 3px;
    padding-right: 3px;
}

h1, h3 {
    color: #000000;
    font-size: 10pt;
}

h2 {
    color: #000000;
    font-size: 10pt;
}

/* Consistent table styling */
table.uniform-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table.uniform-table th, 
table.uniform-table td {
    padding: 10px;
    border: 1px solid #bdc3c7;
    vertical-align: top;
    font-size: 10pt;
}

table.uniform-table th {
    background: #ecf0f1;
    text-align: left;
}

/* Two-column table layout */
table.uniform-table.two-column td:nth-child(1),
table.uniform-table.two-column th:nth-child(1) {
    width: 40%;
}

table.uniform-table.two-column td:nth-child(2),
table.uniform-table.two-column th:nth-child(2) {
    width: 60%;
}

/* Three-column table layout */
table.uniform-table.three-column td,
table.uniform-table.three-column th {
    width: 33.33%;
}

.logo-head {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-bottom: 50px;
    gap:10px;
}

.flex-h{
    align-items: center;
    text-align:right;
    font-weight: bold;
    font-size: 10pt;
}

.intr-p {
    font-size: 10pt;
}

.just{
    text-align: justify;
}

.img-cont {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px;
}

.logo-image {
    height: auto;
    width: 140px;
}

.footer-logo {
    height: auto;
    width: 700px;
}

.intro-container {
    font-size: 10pt;
    display: flex;
    align-items: center;
    justify-content: center;
}

.intro-text {
    text-align: justify;
}