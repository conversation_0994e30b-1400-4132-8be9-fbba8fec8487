<?php

namespace Tests\Feature;

use App\Models\Syllabus;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Response;
use Str;
use Tests\TestCase;

class SyllabusTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_create_syllabus ()
    {
        $syllabus = Syllabus::create(
            [
                'name' => 'a',
                'learn_year_id' => 1,
                'curriculum_id' => 1,
                'status_id' => 1,
                'semester_id' => 1,
                'academic_degree_id' => 1,
                'code' => Str::random(8),
                'credits' => 6,
                'contact_hours' => 12,
                'lecture_hours' => 6,
                'seminar_hours' => 6,
                'mid_and_final_exam_hours' => 2,
                'independent_work_hours' => 11,
                'total_hours' => 12,
                'goal' => 'long text',
                'methods' => 'long text',
                'assessing_system' => 'long text',
                'final_exam_prerequisite' => 'long text',
                'assessing_components' => 'long text',
                'retake_missed_assignment' => 'long text',
                'exam_rules' => 'long text',
                'academic_honesty' => 'long text',
                'additional_information' => 'long text',


            ]

        );
        $this->assertDatabaseHas('syllabi', $syllabus->toArray());

    }
}
