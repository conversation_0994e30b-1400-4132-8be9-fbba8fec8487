<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $syllabusName }}</title>


    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/charts.css/dist/charts.min.css">
    <link rel="stylesheet" href="https://unpkg.com/charts.css/dist/charts.min.css">
    <link rel="stylesheet" href="//cdn.web-fonts.ge/fonts/bpg-algeti/css/bpg-algeti.min.css">
    <style>
        .container {
            font-family: 'BPG Algeti', sans-serif;
        }
        div.general-info {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
            box-shadow: 2px 4px -2px rgba(0, 0, 0, 0.1);
        }
        div.general-info p {
            padding-bottom: 0.5rem;
            margin: 0;
        }
        p span {
            font-weight: bold;
        }
        .chart-span {
            position: relative;
            top: -22px;
            color: #333;
            font-weight: bold;
        }
        .survey-question {
            font-weight: bold;
        }
        .answer-data {
            padding-left: 2rem;
            margin: 0;
            font-size: 14px;
        }
        .answer-list {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="general-info">
        <h3>ძირითადი ინფორმაცია</h3>
        <p class=""><span>ლექტორი:</span> {{ $lecturerName }}</p>
        <p class=""><span>საგანი:</span> {{ $syllabusName }}</p>
        <p class=""><span>სასწავლო წელი:</span> {{ $learnYear }}</p>
        <p class=""><span>სკოლა:</span> {{ $program }} </p>
        <p class=""><span>პროგრამა:</span> {{ $school }} </p>
        <p><span>საერთო რაოდენობა:</span> {{$userStatistic['total_user_count']}} რესპოდენტი</p>
    </div>

<div class="general-info" style="width: 500px">
<h3>{{$survey->name}}</h3>
    <p>სტუდენტის სქესი</p>
    <table class="charts-css column show-labels">
        <tbody>
        <tr>
            <th scope="row" class="">ბიჭი</th>
            <td style="--size: calc( {{$userStatistic['male']}} / 100 ); background: #e72929; border: 2px solid #555; ">
                <span class="chart-span">{{$userStatistic['male'] > 0 ? $userStatistic['male'].'%' : ''}}</span>
            </td>
        </tr>
        <tr>
            <th scope="row" class="">გოგო</th>
            <td style="--size: calc( {{$userStatistic['female']}} / 100 ); background: #0077ff; border: 2px solid #555;">
                <span class="chart-span">{{$userStatistic['female'] > 0 ? $userStatistic['female'].'%' : ''}}</span>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<div class="answers">
    @foreach($survey->questions as $index => $question)
        <p class="survey-question">{{($index + 1).'. '.$question->name}}</p>
        <p>საერთო რაოდენობა: {{$question->answer->count()}}</p>


            @if($question->survey_question_type_id == 1)
            <div id="my-chart" style="width: 300px; height: 300px">

                <table class="charts-css column show-labels" style="height: 500px;width: 500px">
                    <tbody>
                    <tr>
                        <th scope="row" class="">1 ({{ $question->answer->where('answer_int', 1)->count() }})</th>
                        @php
                        $total = $question->answer->count();
                        if ($total > 0)
                            {
                                $answerOne = round((($question->answer->where('answer_int', 1)->count()/$total)*100),2);
                                $answerTwo = round((($question->answer->where('answer_int', 2)->count()/$total)*100),2);
                                $answerThree = round((($question->answer->where('answer_int', 3)->count()/$total)*100),2);
                                $answerFour = round((($question->answer->where('answer_int', 4)->count()/$total)*100),2);
                                $answerFive = round((($question->answer->where('answer_int', 5)->count()/$total)*100),2);
                            }else{
                            $answerOne = 0;
                            $answerTwo = 0;
                            $answerThree = 0;
                            $answerFour = 0;
                            $answerFive = 0;
                            }
                         @endphp
                        <td style="--size: calc( {{$answerOne/100}} )"> <span class="chart-span">{{$answerOne > 0 ? $answerOne.'%' : ''}}</span> </td>
                    </tr>
                    <tr>
                        <th scope="row" class="">2 ({{ $question->answer->where('answer_int', 2)->count() }})</th>
                        <td style="--size: calc( {{$answerTwo/100}} )"> <span class="chart-span">{{$answerTwo > 0 ? $answerTwo.'%' : ''}}</span> </td>
                    </tr>
                    <tr>
                        <th scope="row" class="">3 ({{ $question->answer->where('answer_int', 3)->count() }})</th>

                        <td style="--size: calc( {{$answerThree/100}} )"> <span class="chart-span">{{$answerThree > 0 ? $answerThree.'%' : ''}}</span></td>
                    </tr>
                    <tr>
                        <th scope="row" class="">4 ({{ $question->answer->where('answer_int', 4)->count() }})</th>
                        <td style="--size: calc( {{$answerFour/100}} )"> <span class="chart-span">{{$answerFour > 0 ? $answerFour.'%' : ''}} </span></td>
                    </tr>
                    <tr>
                        <th scope="row" class="">5 ({{ $question->answer->where('answer_int', 5)->count() }})</th>
                        <td style="--size: calc( {{$answerFive/100}} )"> <span class="chart-span" style="font-family: 'BPG Algeti', sans-serif;">{{$answerFive > 0 ? $answerFive.'%' : ''}}</span> </td>
                    </tr>
                    </tbody>
                </table>
            </div>

        @else
                <div>
                <p style="text-decoration: underline; margin: 0; padding-bottom: 0.5rem;">პასუხები: </p>
                <div class="answer-list">
                    @foreach($question->answer as $index => $answer)
                        <p class="answer-data">{{($index+1).': '.$answer->answer_string}}</p>
                    @endforeach
                </div>
                </div>
            @endif

        <div>
            <p style="text-decoration: underline; margin: 0; padding-bottom: 0.5rem;">კომენტარები: </p>
            <div class="">
                @php($j=0)
                @foreach($question->answer as $index => $answer)
                    @if($answer->comment)
                    <p class="answer-data">{{($j+1).': '.$answer->comment}}</p>
                    @php($j++)
                    @endif
                @endforeach
            </div>
        </div>
    @endforeach
</div>
</div>

</body>
</html>
