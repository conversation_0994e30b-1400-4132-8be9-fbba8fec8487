{{-- resources/views/users.blade.php --}}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User List</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        /* Custom styles for a solid look */
        .table th, .table td {
            vertical-align: middle; /* Center align content vertically */
        }

        .alert {
            background-color: #343a40; /* Dark background */
            color: #ffffff; /* White text */
        }
    </style>
</head>
<body>
<div class="container mt-5">
    <h1 class="mb-4">Student List</h1>

    <table class="table table-bordered table-striped">
        <thead class="thead-dark">
        <tr>
            <th>Student</th>
            <th>Birthday</th>
            <th>Program</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($students as $student)
            <tr>
                <td>{{ $student->name }} {{ $student->surname }}</td>
                <td>{{ \Carbon\Carbon::parse($student->birthday)->format('Y-m-d') }}</td>
                <td>{{ $student->program->name_ka }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <!-- Display total count of users after the table -->
    <div class="alert alert-info">
        Total: <strong>{{ $students->count() }}</strong>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
