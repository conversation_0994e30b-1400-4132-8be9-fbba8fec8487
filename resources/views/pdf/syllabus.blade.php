<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <title>syllabus</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{--    <link rel="stylesheet" href="{{asset('assets/syllabus/app.css')}}">--}}
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            text-decoration: none;
            list-style: none;
        }

        body {
            font-family: DejaVu Sans;
        }

        /*intro styles*/
        .container {
            max-width: 1280px;
            width: 100%;
            padding: 50px 25px;
            margin: 0 auto;
        }

        .title {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px 0;
            margin-bottom: 30px;
        }

        .page-syllabus .title img {
            max-width: 150px;
            width: 100%;
        }

        .page-break {
            page-break-after: always;
            border-bottom: 1px dashed black;
        }

        .page-syllabus .title p {
            max-width: 500px;
            width: 100%;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }

        /*intro styles*/


        /*table styles*/
        .table-wrapper::-webkit-scrollbar {
            display: none;
        }

        .table-wrapper {
            overflow-x: scroll;
            -ms-overflow-style: none;
            scrollbar-width: none;
            margin: 0 auto;
        }

        .page-syllabus .container .table {
            margin: 30px 0;
            border-collapse: collapse;
            max-width: 512px;
            width: 94%;
        }

        .page-syllabus .container .table th {
            width: 30%;
        }

        .page-syllabus .container .table.weeks th:first-child {
            width: 5%;
        }

        .page-syllabus .container .table.weeks th.rotate {
            transform: rotate(-90deg);
        }

        .page-syllabus .container .table ol,
        .page-syllabus .container .table ul {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px 0;
        }

        .italic {
            font-style: italic;
        }

        .underline {
            text-decoration: underline;
        }

        .page-syllabus .container .table ol li {
            list-style: auto;
        }

        .page-syllabus .container .table ul.disc {
            padding-left: 20px;
        }


        .page-syllabus .container .table ul.disc li {
            list-style: disc;
        }


        .page-syllabus .container .table ul.disc-large li::before {
            content: '●';
            color: #000;
        }

        .page-syllabus .container .table ul.disc-large li {
            display: flex;
            align-items: flex-start;
            gap: 0 20px;
        }

        .page-syllabus .container .table tr td,
        .page-syllabus .container .table tr th {
            padding: 25px;
            border: 1px solid #cccccc;
            text-align: start;
            font-size: 16px;
            vertical-align: top;
        }

        /*end table styles*/


        /*student contract styles*/
        .title-red {
            color: #EF8C89;
            font-size: 18px;
        }

        .page-contract .content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px 0;
        }

        .page-contract .content .title-md {
            font-size: 16px;
            letter-spacing: 4px;
        }

        .page-contract .content p,
        .page-contract .content .address span:first-child {
            font-size: 14px;
        }

        .page-contract .content .address {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-contract .content .text {
            margin-top: 30px;
        }

        .page-contract .content h4 {
            margin-bottom: 10px;
        }

        .page-contract .content dd,
        .page-contract .content dt {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            gap: 0 10px;
        }

        .page-contract .content dd {
            padding-left: 20px;
        }

        .page-contract .content dd span,
        .page-contract .content dt span {
            min-width: 50px;
            width: 50px;
            /*background-color: red;*/
        }

        .page-contract .main-content .text {
            margin-bottom: 60px;
        }

        .page-contract .main-content .table {
            border-collapse: collapse;
            margin-top: 50px;
            width: 768px;
            min-width: 768px;
        }

        .page-contract .main-content .table.lecturer {
            max-width: 100%;
            width: 100%;
        }

        .page-contract .main-content .table tr td,
        .page-contract .main-content .table tr th {
            padding: 15px;
            border: 1px solid #cccccc;
            text-align: start;
            font-size: 16px;
            vertical-align: top;
        }

        .page-contract .main-content .table.professional tr td {
            padding: 10px 30px 50px 10px;
        }

        .page-contract .main-content .table tr td:last-child {
            white-space: nowrap;
        }

        .page-contract .main-content .table:not(.table.professional, .table.staff) tr:last-child td {
            border: none;
            padding-top: 30px;
        }

        .page-contract .main-content .table.lecturer.decoration tr th {
            background-color: rgba(128, 128, 128, 0.33);
        }

        /* end student contract styles*/

        .center {
            width: 100%;
            text-align: center;
        }

        .table-title {
            margin: 50px 0;
        }

        .page-contract .container .main-content {
            width: 100%;
        }

        .table-weeks {
            page-break-inside: avoid;
        }

        .table-weeks tbody tr {
            page-break-inside: avoid;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid black;
        }

    </style>
</head>
<body class="page-syllabus">

<div class="container">

    <div class="title">
        <div style="text-align: center;">
            <img
                src="{{ public_path('assets/syllabus/media/logo.png') }}" alt="logo">
        </div>
        <p style="margin: 0 auto;">
            ჯიპა - საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი
            სამართლის და პოლიტიკის სკოლა
            საერთაშორისო სამართლის სამაგისტრო პროგრამა
            სილაბუსი
        </p>
    </div>

    <div class="table-wrapper">

        <table class="table">
            <tr>
                <th>სასწავლო კურსის დასახელება</th>
                <td>{{$syllabus->name}}</td>
            </tr>
            <tr>
                <th>სასწავლო კურსის საფეხური</th>
                <td>{{$syllabus->academicDegree->name_ka}}</td>
            </tr>
            <tr>
                <th>სასწავლო კურსის სტატუსი</th>
                <td>{{$syllabus->status->name_ka}}</td>
            </tr>
            <tr>
                <th>სასწავლო კურსის განხორციელების სემესტრი</th>
                <td>{{$syllabus->semester->name}}</td>
            </tr>
            <tr>
                <th>სასწავლო კურსის კოდი</th>
                <td><span>{{$syllabus->code}}</span></td>
            </tr>
            <tr>
                <th>ECTS კრედიტების რაოდენობა</th>
                <td>{{$syllabus->credits}}</td>
            </tr>
            <tr>
                <th>საათების გადანაწილება სტუდენტის დატვირთვის შესაბამისად</th>
                <td>
                    <strong>საკონტაქტო საათები: {{$syllabus->contact_hours}} საათი <br> <br> მათ შორის: </strong>
                    <br><br>
                    <ul>
                        <li>ლექცია/სემინარი - {{$syllabus->lecture_hours+$syllabus->seminar_hours}} საათი</li>
                        <li>შუალედური და დასკვნითი გამოცდა - {{$syllabus->mid_and_final_exam_hours}} საათი</li>
                    </ul>
                    <br> <br>
                    <strong>ინდივიდუალური მუშაობა - {{$syllabus->independent_work_hours}} საათი <br> <br>
                        ჯამში: {{$syllabus->total_hours}} </strong>
                </td>
            </tr>
            <tr>
                <th>
                    კურსის განმახროციელებელი/ ლექტორი
                    საკონტაქტო ინფორმაცია
                    სტუდენტებთან საკონსულტაციო შეხვედრისთვის გამოყოფილი დღე და საათი
                </th>
                <td>
                    @foreach($syllabus->lecturerContactTimes->toArray() as $lecturerContactTime)
                        <div style="margin-bottom: 20px;">
                            <ul>
                                <li>
                                    <span> {{ $lecturerContactTime['lecturer']['first_name'] }} {{ $lecturerContactTime['lecturer']['last_name'] }} - {{ $lecturerContactTime['lecturer']['affiliated'] ? 'აფილირებული' : 'არ არის აფილირებული' }}</span>
                                </li>
                                <li>ელ. მისამართი:<span>{{ $lecturerContactTime['lecturer']['email'] }}</span></li>
                                <li>ტელ: <span>{{ $lecturerContactTime['lecturer']['phone'] }}</span></li>
                            </ul>
                            <br> <br>
                            საკონსულტაციო დღეები და საათები:
                            {{$lecturerContactTime['week_day']}} {{$lecturerContactTime['start_time']}}
                            -{{$lecturerContactTime['end_time']}}, {{$syllabus['school_id']}}.
                        </div>
                    @endforeach
                </td>
            </tr>
        </table>
        <div class="page-break"></div>
        <table class="table">
            <tr>
                <th>სასწავლო კურსის მიზნები</th>
                <td>
                    {!! $syllabus->goal !!}
                </td>
            </tr>
            <tr>
                <th>საგანზე დაშვების წინაპირობა/ები</th>
                <td>
                    @forelse($syllabus->prerequisites as $index => $prerequisite)
                        {{$prerequisite->code}}{{ $index < count($syllabus->prerequisites) - 1 ? ',' : '' }}
                    @empty
                        არ აქვს
                    @endforelse
                </td>
            </tr>
            <tr>
                <th>სწავლება-სწავლის მეთოდები</th>
                <td>
                    <ul>
                        @foreach($syllabus->methods as $method)
                            <li>
                                <strong>{{$method->title}} - </strong>
                                {{$method->text}}
                            </li>
                        @endforeach
                        <li>
                            <strong>სხვა.</strong>
                        </li>
                    </ul>
                </td>
            </tr>
        </table>
        <div class="page-break"></div>
        <table class="table weeks table-weeks" style="margin-top: 30px; border-collapse: collapse;">
            <thead>
            <tr>
                <th>კვირა</th>
                <th>სასწავლო კურსის/მოდულის შინაარსი (თემები და აქტივობები)</th>
                <th>ძირითადი ლიტერატურა:</th>
                <th>დამატებითი ლიტერატურა</th>
            </tr>
            </thead>
            <tbody style="margin-top: 30px;">
            @foreach($syllabus->weeks as $week)
                <tr>
                    <td >{{$week->number}}</td>
                    <td>{{$week->title}}</td>
                    <td>
                        {{$week->main_literature}}
                    </td>
                    <td>
                        {{$week->secondary_literature}}
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
        <div class="page-break"></div>
        <table class="table">
            <tr>
                <th>შეფასების სისტემა</th>
                <td>
                    {{$syllabus->assessing_system}}
                </td>
            </tr>
            <tr>
                <th>
                    დასკვნით გამოცდაზე დაშვების წინაპირობა, დასკვნითი გამოცდის მინიმალური კომპეტენციის ზღვარი და
                    დამატებით გამოცდაზე გასვლის პირობა
                </th>
                <td>
                    {!! $syllabus->final_exam_prerequisite !!}
                </td>
            </tr>
            <tr>
                <th>შეფასების კომპონენტები</th>
                <td>
                    <strong>შეფასებები:</strong>
                    <br> <br>
                    <ul class="disc">
                        @foreach($syllabus->assignments as $assignment)
                            <li>{{ $assignment->assessmentComponent->name_ka }} - {{ $assignment->score }} ქულა</li>
                        @endforeach
                    </ul>
                    <br>
                    <strong>საბოლოო შეფასება 100 ქულა</strong>
                </td>
            </tr>
            <tr>
                <th>გაცდენილი შეფასებითი კომპონენტის აღდგენა</th>
                <td>არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები აღდგენას არ ექვემდებარება.</td>
            </tr>
            @foreach($syllabus->assignments as $assignment)
                <tr>
                    <th>{{$assignment->assessmentComponent->name_ka}}</th>
                    <td>
                        {{$assignment->description}}
                    </td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2">
                    ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ არის დაშვებული ლექტორის მიერ)
                    იკრძალება:
                    <br> <br>
                    <ul class="disc">
                        <li>დაგვიანება;</li>
                        <li>ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების შემთხვევაში უკან დაბრუნება;
                        </li>
                        <li>ხმაური;</li>
                        <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
                        <li>და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის მიმდინარეობას.</li>
                    </ul>
                    <br> <br>
                    აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში სტუდენტის მიმართ შეიძლება გავრცელდეს
                    შემდეგი სანქციები:
                    <br> <br>
                    <ul class="disc">
                        <li>შენიშვნა;</li>
                        <li>საყვედური;</li>
                        <li>სხვა პასუხისმგებლობა.</li>
                    </ul>
                </td>
            </tr>
            <tr>
                <th>აკადემიური კეთილსინიდსიერების დაღვევა - პლაგიატი</th>
                <td>
                    {!!$syllabus->academic_honesty!!}
                </td>
            </tr>
            <tr>
                <th>სწავლის შედეგები</th>
                <td>
                    <strong class="underline">ცოდნა და გაცნობიერება:</strong>
                    <br> <br>
                    {!! $syllabus->learning_outcome_knowledge !!}
                    <br> <br>
                    <strong class="underline">უნარი:</strong>
                    <br> <br>
                    {!! $syllabus->learning_outcome_skill !!}
                    <br> <br>
                    <strong class="underline">პასუხისმგებლობა და ავტონომიურობა:</strong>
                    <br> <br>
                    {!! $syllabus->learning_outcome_responsibility !!}
                </td>
            </tr>
            <tr>
                <th>დამატებითი ინფორმაცია/პირობები</th>
                <td>{!! $syllabus->additional_information !!}</td>
            </tr>
        </table>

    </div>
</div>

</body>
