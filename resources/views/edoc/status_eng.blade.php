<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>GIPA-Georgian Institute of Public Affairs</title>
    <style>
        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            font-weight: normal;
            font-size: 1rem;
        }

        .wrapper {
            padding-left: 48px;
            padding-right: 48px;
            position: relative;
            margin-top: 2rem;
        }

        .header {
            margin-bottom: 4rem;
        }

        .header h1 {
            font-size: 16px;
            border-top: 3px solid rgb(158, 36, 89);
            border-bottom: 3px solid rgb(158, 36, 89);
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }

        .body-wrapper {
            /*padding-left: 48px;*/
            /*padding-right: 48px;*/
        }

        .body-wrapper p {
            margin-bottom: 1rem;
            font-size: 12px;
        }

        .divider {
            padding: 1rem;
        }

        .heading-container {
            width: -webkit-fill-available;
        }

        .date-time {
            margin-bottom: 2rem;
        }

        .date-time p {
            margin-bottom: 0;
            font-weight: bold;
        }

        .footer {
            position: fixed;
            bottom: 80px;
            left: 48px;
            right: 48px;
            margin-top: 12rem;
            padding-top: 2rem;
            border-top: 3px solid rgb(158, 36, 89);
        }

        .col-footer {
            float: left;
            width: 33.33%;
        }

        .col-footer p {
            font-size: 12px !important;
        }

        .span {
            font-size: 12px;
        }

        .section-container {
            overflow: hidden; /* Clearfix for containing floats */
        }

        .left-content,
        .right-content {
            display: inline-block;
            vertical-align: top;
            width: 48%; /* Adjust the width as needed */
            box-sizing: border-box;
        }

    </style>
</head>
<body>
<div class="wrapper">
    <div class="header">
        <div style="float: left;">
            <img src="{{ public_path('assets/syllabus/media/logo.png') }}" width="150" alt="logo"/>
        </div>
        <div class="heading-container" style="float: right; width: 75%;">
            <div class="divider"></div>
            <h1>N(N)LE GIPA – Georgian Institute of Public Affairs</h1>
        </div>
    </div>
    <div style="clear: both;"></div>
    <div class="body-wrapper" style="margin-top: 30px;">
        <div class="date-time">
{{--            <div> {!! DNS1D::getBarcodeHTML($eDoc['document_number'] ?? '0', "C128",1.4,22) !!}</div>--}}
            <p style="float: left;">{{$eDoc['document_number'] ?? '0'}}</p>
            <p style="float: right;">{{$eDoc['updated_at'] ? \Carbon\Carbon::parse($eDoc['updated_at'])->format('d.m.Y') : \Carbon\Carbon::now()}}</p>
        </div>
        <p style="text-align: center; font-size: 16px; font-weight: bold">Certificate</p>
        <p style="text-align: justify !important;">
            To whom it may concern<br>
            {!!
str_replace(
            ['[name]',
            '[surname]',
            '[birthday]',
            '[personal_id]',
            '[school_name]',
            '[program_name]',
            '[enrollment_date]',
            '[enrollment_order]',
            ],
            [$student->name_en,
            $student->surname_en,
            \Carbon\Carbon::parse($student->birthday)->format('d.m.Y'),
            $student->personal_id,
            $student->school->name_en,
            $student->program->name_en,
            \Carbon\Carbon::parse($student->enrollment_date)->format('d.m.Y'),
            $student->enrollment_order,
            ],
            $eDoc['text']
        ) !!}
        </p>
        <div>
            <span class="span">Level of higher education: {{$student->program->academicDegree->name_en}}.</span><br>
            <span class="span">Name of educational program: {{$student->program->name_en}}.</span><br>
            <span class="span">Current Status: {{$student->status->name_en}}.</span><br>
        </div>
        <div style="margin-top: 30px;">&nbsp;</div>
        <div class="section-container">
            <div class="left-content">
                <span class="span">{{$student->school->dean_en}}</span><br>
                <span class="span">Dean</span><br>
                <span class="span">{{$student->school->name_en}}</span><br>
                <span class="span">N(N)LE GIPA-Georgian Institute of Public Affairs</span>
            </div>

            <div class="right-content">
                <!-- Right side content -->
                @if($eDoc['stamp']==0)
                    <div style="text-align: right;">
                        <img src="{{ public_path('assets/signatures/'.$student->school->signature) }}" width="150" alt="logo"/>
                    </div>
                    <div style="text-align: right;">
                        <img src="{{ public_path('assets/stamp.png') }}" width="150" alt="logo"/>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="col-footer">
            <p>www.gipa.ge</p>
            <p><EMAIL></p>
        </div>
        <div class="col-footer">
            <p>&nbsp;</p>
            <p>&nbsp;</p>
            <p>&nbsp;</p>
        </div>
        <div class="col-footer" style="text-align: right;">
            <p>Brosset Str. 2; 0108, Tbilisi,</p>
            <p>Georgia</p>
            <p>Identification Number: 204429341</p>
        </div>
    </div>
</div>
</body>
</html>
