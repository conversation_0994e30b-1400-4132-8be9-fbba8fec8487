<div class="wrapper">
    <div class="header">
        <div class="image-box">
            <img src="{{ public_path('assets/syllabus/media/logo.png') }}" width="150" alt="logo"/>
        </div>
        <div class="heading-container">
            <div class="divider"></div>
            <h1>ა(ა)იპ ჯიპა - საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი</h1>
        </div>
    </div>
    <div style="clear: both;"></div>
    <div class="body-wrapper" style="margin-top: 30px;">
        <div class="date-time">
            <div class="image-box">
                {{$eDocArray['document_number'] ?? '0'}}
            </div>
            <div class="image-box-right">{{$eDocArray['updated_at'] ? \Carbon\Carbon::parse($eDocArray['updated_at'])->format('d.m.Y') : \Carbon\Carbon::now()}}</div>

        </div>
        <p style="text-align: center; font-size: 16px; font-weight: bold">ცნობა</p>
        <p style="text-align: justify !important;">
            {!!
str_replace(
            ['[name]',
            '[surname]',
            '[birthday]',
            '[personal_id]',
            '[school_name]',
            '[program_name]',
            '[enrollment_date]',
            '[enrollment_order]',
            ],
            [$student->name,
            $student->surname,
            \Carbon\Carbon::parse($student->birthday)->format('d.m.Y'),
            $student->personal_id,
            $student->school->name_ka,
            $student->program->name_ka,
            \Carbon\Carbon::parse($student->enrollment_date)->format('d.m.Y'),
            $student->enrollment_order,
            ],
            $eDocArray['text']
        ) !!}
        </p>
        <div>
            <span class="span">უმაღლესი განათლების საფეხური: {{$student->program->academicDegree->name_ka}}.</span><br>
            <span class="span">საგანმანათლებლო პროგრამის დასახელება: {{$student->program->name_ka}}.</span><br>
        </div>
        <div>
            <table cellpadding="5px" autosize="1" border="1" width="100%" class="table-grades" style="overflow: wrap; margin-top: 25px;">
                <thead>
                <tr>
                    <th class="center" colspan="2"> საგანი</th>
                    <th scope="col" class="center">კრედიტი (ECTS)</th>
                    <th scope="col" colspan="2" class="center">შეფასება</th>
                </tr>
                </thead>
                <tbody>
                    @php
                        $count = 0;
                        foreach ($histories as $history)
                        {
                            if(round($history->point) >= 51)
                            {
                                $count += $history->credits ?? $history->syllabus->credits;
                            }
                        }
                    @endphp

                    @foreach($histories as $key=>$grade)
                        @php
                            $point = round($grade->point);
                            if ($point >= 91) {
                                $pointMarker = 'A';
                            }elseif($point>=81){
                                $pointMarker = 'B';
                            }elseif($point>=71){
                                $pointMarker = 'C';
                            }elseif($point>=61){
                                $pointMarker = 'D';
                            }elseif($point>=51){
                                $pointMarker = 'E';
                            }else{
                                $pointMarker = 'F';
                            }
                        @endphp
                        <tr>
                            <td scope="row">{{$key+1}}.</td>
                            <td>{{$grade->syllabus->name}}</td>
                            <td class="center">{{$grade->credits ?? $grade->syllabus->credits}}</td>
                            <td class="center">{{$point}}</td>
                            <td class="center">{{$pointMarker}}</td>
                        </tr>
                    @endforeach
                </tbody>

                <tfoot>
                    <tr>
                        <td colspan="2">კრედიტების ჯამი</td>
                        <td class="center">{{$count}}</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>

                    </tr>
                    <tr>
                        <td colspan="3" class="center">GPA:</td>
                        <td colspan="2" class="center">{{$student->gpa}}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <div style="margin-top: 30px;">&nbsp;</div>
        <div class="section-container">
            <div class="left-content">
                <span class="span">{{$student->school->dean_ka}}</span><br>
                <span class="span">დეკანი</span><br>
                <span class="span">{{$student->school->name_ka}}.</span><br>
                <span class="span">ა(ა)იპ ჯიპა - საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი</span>
            </div>

            <div class="right-content">
                <!-- Right side content -->
                @if($eDocArray['stamp']==0)
                    <div style="text-align: right;">
                        <img src="{{ public_path('assets/signatures/'.$student->school->signature) }}" width="150" alt="logo"/>
                    </div>
                    <div style="text-align: right;">
                        <img src="{{ public_path('assets/stamp.png') }}" width="150" alt="logo"/>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
