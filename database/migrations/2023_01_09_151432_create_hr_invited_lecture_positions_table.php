<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new

class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_invited_lecture_positions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hr_invited_lecture_info_id');
            $table->integer('salary')->nullable();
            $table->string('direction', 255)->nullable();
            $table->foreignId('school_id')->nullable();
            $table->foreignId('program_id')->nullable();
            $table->string('course')->nullable();
            $table->foreignId('work_type_id')->nullable();
            $table->string('workplace_name', 255)->nullable();
            $table->string('position')->nullable();
            $table->boolean('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_invited_lecture_positions');
    }
};
