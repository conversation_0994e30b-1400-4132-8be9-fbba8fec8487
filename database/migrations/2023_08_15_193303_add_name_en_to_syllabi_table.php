<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('syllabi', function (Blueprint $table) {
            $table->string('name_en')->nullable();
            $table->longText('goal_en')->nullable();
            $table->longText('assessing_system_en')->nullable();
            $table->longText('final_exam_prerequisite_en')->nullable();
            $table->longText('retake_missed_assignment_en')->nullable();
            $table->longText('main_literature_en')->nullable();
            $table->longText('additional_literature_en')->nullable();
            $table->longText('academic_honesty_en')->nullable();
            $table->longText('additional_information_en')->nullable();
            $table->longText('learning_outcome_knowledge_en')->nullable();
            $table->longText('learning_outcome_skill_en')->nullable();
            $table->longText('learning_outcome_responsibility_en')->nullable();
            $table->longText('exam_rules_en')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('syllabi', function (Blueprint $table) {
            //
        });
    }
};
