<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assessment_component_id')->constrained();
            $table->unsignedBigInteger('parent_id')->default(0);
            $table->unsignedTinyInteger('is_weighted')->default(0);
            $table->foreignId('syllabus_id')->constrained();
            $table->unsignedInteger('score');
            $table->unsignedInteger('min_score')->default(0);
            $table->dateTime('exam_date')->nullable();
            $table->dateTime('expiration_date')->nullable();
            $table->text('description');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assignments');
    }
};
