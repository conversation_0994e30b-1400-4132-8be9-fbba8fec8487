<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_register_programs', function (Blueprint $table) {
            $table->unsignedBigInteger('training_register_id')->nullable()->change();
            $table->unsignedBigInteger('program_id')->nullable()->change();
            $table->unsignedInteger('level_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_register_programs', function (Blueprint $table) {
            $table->unsignedBigInteger('training_register_id')->nullable(false)->change();
            $table->unsignedBigInteger('program_id')->nullable(false)->change();
            $table->unsignedInteger('level_id')->nullable(false)->change();
        });
    }
};
