<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_administration_appointments', function (Blueprint $table) {
            $table->id();
            $table->boolean('appointment')
                ->comment('კონკურსი - 0  , რეკომენდაცია - 1')->nullable();
            $table->string('vacancy_command_number')->nullable();
            $table->string('vacancy_command_number_file')->nullable();
            $table->string('appointment_command_number')->nullable();
            $table->string('appointment_command_number_file')->nullable();
            $table->date('appointment_command_number_date')->nullable();
            $table->date('vacancy_command_number_date')->nullable();
            $table->string('position')->nullable();
            $table->boolean('type_of_position')->comment('0 - დამხმარე , 1 - ადმინისტრაციული')->nullable();
            $table->date('contract_start')->nullable();
            $table->date('contract_end')->nullable();
            $table->string('contract_period')->nullable();
            $table->longText('vacation')->nullable();
            $table->longText('day_off')->nullable();
            $table->boolean('status')->comment('0 - არააქტიური , 1 - აქტიური')
                ->nullable();
            $table->boolean('educational_staff')->nullable();
            $table->foreignId('hr_administration_info_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_administration_appointments');
    }
};
