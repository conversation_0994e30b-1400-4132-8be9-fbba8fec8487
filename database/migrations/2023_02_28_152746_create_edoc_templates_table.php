<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('edoc_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('index')->nullable();
            $table->longText('text')->nullable();
            $table->boolean('automatic')->default(0)->comment('0=ადმინისტრატორის მიერ ცნობის გენერირება; 1=ავტომატურად გენერირება');
            $table->foreignId('user_type_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('edoc_templates');
    }
};
