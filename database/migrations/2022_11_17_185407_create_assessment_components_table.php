<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assessment_components', function (Blueprint $table) {
            $table->id();
            $table->string('name_ka');
            $table->string('name_en');
            $table->unsignedTinyInteger('type_id')
                ->comment('1 - standard, 2 - middle, 3 - final')
                ->default(1)
                ->index();
            $table->unsignedTinyInteger('is_parent')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assessment_components');
    }
};
