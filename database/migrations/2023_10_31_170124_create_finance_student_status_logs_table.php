<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_student_status_logs', function (Blueprint $table) {
            $table->id();
            $table->float('amount')->nullable();
            $table->unsignedInteger('student_id');
            $table->unsignedTinyInteger('status_id')->comment('1: Activate status 2: Suspend status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_student_status_logs');
    }
};
