<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bachelors_temp', function (Blueprint $table) {
            $table->id();
            $table->foreignId('program_id');
            $table->foreignId('flow_id');
            $table->string('personal_id');
            $table->string('first_name');
            $table->string('last_name');
            $table->boolean('is_registered')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bachelors_temp');
    }
};
