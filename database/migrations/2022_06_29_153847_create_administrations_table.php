<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('administrations', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('identity_number');
            $table->string('phone')->unique()->nullable();
            $table->string('email')->unique();
            $table->foreignId('administration_position_id')->nullable();
            $table->foreignId('administration_item_id')->nullable();
            $table->foreignId('school_id')->nullable();
            $table->string('photo')->nullable();
            $table->string('cv')->nullable();
            $table->foreignId('user_id');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('administrations');
    }
};
