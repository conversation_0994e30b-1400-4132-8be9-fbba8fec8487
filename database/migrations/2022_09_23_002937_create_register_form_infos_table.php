<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('register_form_infos', function (Blueprint $table) {
            $table->id();
            $table->morphs('registerable');
            $table->string('first_name');
            $table->string('last_name');
            $table->bigInteger('identity_number');
            $table->string('phone');
            $table->string('email');
            $table->boolean('gender')->comment('0 - გოგო , 1 - ბიჭი');
            $table->timestamp('date_of_birth');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('register_form_infos');
    }
};
