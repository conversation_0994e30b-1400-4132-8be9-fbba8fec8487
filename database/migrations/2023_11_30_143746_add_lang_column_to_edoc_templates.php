<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('edoc_templates', function (Blueprint $table) {
            $table->boolean('lang')->default(0)->after('signature')->comment('0=ქართული; 1=ინგლისური');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('edoc_templates', function (Blueprint $table) {
            //
        });
    }
};
