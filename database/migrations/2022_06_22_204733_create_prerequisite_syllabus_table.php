<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('prerequisite_syllabus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prerequisite_id')->references('id')->on('syllabi')->constrained();
            $table->foreignId('syllabus_id')->references('id')->on('syllabi')->constrained();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('prerequisit_syllabus');
    }
};
