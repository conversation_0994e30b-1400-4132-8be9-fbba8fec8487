<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('auditoria', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->foreignId('campus_id')->cascadeOnDelete();
            $table->boolean('student_aid');
            $table->integer('quantity');
            $table->boolean('projector');
            $table->boolean('multimedia');
            $table->boolean('exam_audience');
            $table->boolean('cameras');
            $table->boolean('computer_lab');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('auditoria');
    }
};
