<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hr_academic_lecture_positions', function (Blueprint $table) {
            $table->text('cv_georgian')->nullable();
            $table->text('cv_english')->nullable();
            $table->text('id_card')->nullable();
            $table->text('diploma')->nullable();
            $table->text('scientific_works')->nullable();
            $table->text('certificate')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hr_academic_lecture_positions', function (Blueprint $table) {
            $table->dropColumn('cv_georgian');
            $table->dropColumn('cv_english');
            $table->dropColumn('id_card');
            $table->dropColumn('diploma');
            $table->dropColumn('scientific_works');
            $table->dropColumn('certificate');
        });
    }
};
