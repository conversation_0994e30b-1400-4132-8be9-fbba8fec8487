<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('doctor_registers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flow_id');
            $table->foreignId('english_level_id');
            $table->string('photo');
            $table->string('identity_number_copy');
            $table->string('cv');
            $table->longText('about_university');
            $table->foreignId('program_id');
            $table->string('address');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('doctor_registers');
    }
};
