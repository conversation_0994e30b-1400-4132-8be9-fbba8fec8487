<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('training_registers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flow_id');
            $table->string('first_name_en');
            $table->string('last_name_en');
            $table->foreignId('english_level_id');
            $table->string('photo');
            $table->string('identity_number_copy');
            $table->string('cv');
            $table->foreignId('program_id');
            $table->boolean('employment_status');
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('training_registers');
    }
};
