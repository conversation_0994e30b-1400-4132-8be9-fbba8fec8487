<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('survey_activations', function (Blueprint $table) {
            $table->unsignedTinyInteger('answered')->default(0)->comment('1:kitxvas upasuxa 0:kitxva pasux gaucemelia');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('survey_activations', function (Blueprint $table) {
            $table->dropColumn(['answered']);
        });
    }
};
