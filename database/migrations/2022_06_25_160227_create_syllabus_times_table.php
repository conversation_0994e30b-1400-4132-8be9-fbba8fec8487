<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('syllabus_times', function (Blueprint $table) {
            $table->id();
            $table->foreignId('syllabus_id')->constrained();
            $table->unsignedInteger('weekday'); // we can save 0-6 for mon-sun
            $table->timestamp('starts_at');
            $table->unsignedInteger('duration'); //minutes
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('syllabus_times');
    }
};
