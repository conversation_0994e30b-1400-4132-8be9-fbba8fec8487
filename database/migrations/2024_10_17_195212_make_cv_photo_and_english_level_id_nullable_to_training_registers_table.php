<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_registers', function (Blueprint $table) {
            $table->string('cv')->nullable()->change();
            $table->string('photo')->nullable()->change();
            $table->unsignedBigInteger('english_level_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_registers', function (Blueprint $table) {
            $table->string('cv')->nullable(false)->change();
            $table->string('photo')->nullable(false)->change();
            $table->unsignedBigInteger('english_level_id')->nullable(false)->change();
        });
    }
};
