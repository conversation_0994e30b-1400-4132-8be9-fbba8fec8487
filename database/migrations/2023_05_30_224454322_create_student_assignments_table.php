<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Reestry\Student\Student;
use App\Models\Assignment;
use App\Models\Syllabus\Syllabus;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Student::class)->index();
            $table->foreignIdFor(Assignment::class);
            $table->foreignIdFor(Syllabus::class)->index();
            $table->dateTime('date');
            $table->float('point');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_assignments');
    }
};
