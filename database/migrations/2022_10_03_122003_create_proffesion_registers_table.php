<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('proffesion_registers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flow_id');
            $table->string('first_name_en')->nullable();
            $table->string('last_name_en')->nullable();
            $table->string('photo');
            $table->string('identity_number_copy');
            $table->string('school');
            $table->integer('school_finish_date');
            $table->foreignId('program_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('proffesion_registers');
    }
};
