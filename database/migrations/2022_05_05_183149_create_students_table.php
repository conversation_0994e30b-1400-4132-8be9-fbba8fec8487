<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('surname');
            $table->string('surname_en')->nullable();
            $table->string('personal_id')->nullable();
            $table->string('personal_id_number')->nullable();
            $table->boolean('sex')->default('0')->comment('0=მდედრობითი;1=მამრობითი');
            $table->string('address')->nullable();
            $table->string('citizenship')->nullable();
            $table->string('phone')->nullable();
            $table->string('parent_phone')->nullable();
            $table->timestamp('birthday')->nullable();
            $table->timestamp('enrollment_date')->nullable();
            $table->string('enrollment_order')->nullable();
            $table->string('email')->index()->unique();
            $table->boolean('mobility')->default('0')->comment('0=არა;1=კი');
            $table->string('photo')->nullable();
            $table->longText('notes')->nullable();
            $table->string('cv_file_name')->nullable();
            $table->string('diploma_file_name')->nullable();
            $table->string('transcript_file_name')->nullable();
            $table->string('motivation_article_file_name')->nullable();
            $table->boolean('diploma_taken')->default('0')->comment('0=არა;1=კი');
            $table->timestamp('diploma_taken_date')->nullable();
            $table->longText('bio')->nullable();
            $table->foreignId('user_id')->index();
            $table->foreignId('status_id')->index()->references('id')->on('student_status_lists');
            $table->foreignId('basics_of_enrollement_id')->references('id')->on('student_basics_of_enrollments');
            $table->foreignId('school_id');
            $table->foreignId('program_id');
            $table->foreignId('group_id')->nullable()->references('id')->on('student_groups');
            $table->foreignId('learn_year_id');
            $table->integer('course')->default(1);
            $table->foreignId('applicant_id')->nullable();
            $table->string('applicant_type')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('students');
    }
};
