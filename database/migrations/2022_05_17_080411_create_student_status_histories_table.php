<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_status_histories', function (Blueprint $table) {
            $table->id();
            $table->text('notes')->nullable();
            $table->foreignId('student_id');
            $table->foreignId('status_id')->references('id')->on('student_status_lists');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_status_histories');
    }
};
