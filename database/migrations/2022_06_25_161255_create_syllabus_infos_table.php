<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('syllabus_infos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('syllabus_id')->constrained();
            $table->foreignId('auditorium_id')->constrained();
            $table->foreignId('syllabus_time_id')->constrained();
            $table->foreignId('student_group_id')->constrained();

            /**
             * we have seperated table syllabusTimes.
             * To record when and how many times specific subject is learnt
             * we create record for each day and relate it to syllabus and curiculum
             */
            $table->foreignId('lecturer_id')->constrained();
            $table->decimal('lecturer_cost_per_hour');
            $table->string('accounting_id');
            $table->date('start_date');
            $table->date('end_date');
            $table->unsignedInteger('allowed_students_amount');
            $table->string('lang')->default('ge');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('syllabus_infos');
    }
};
