<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_registers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flow_id')->nullable();
            $table->foreignId('program_id');
            $table->string('identity_number_copy');
            $table->string('exam_document')->nullable();
            $table->longText('finished_university_info');
            $table->string('diploma_copy');
            $table->string('marks_paper');
            $table->string('cv');
            $table->string('motivation_letter');
            $table->string('acknowledge_info')->nullable();
            $table->foreignId('english_level_id');
            $table->string('card_number');
            $table->string('address');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_registers');
    }
};
