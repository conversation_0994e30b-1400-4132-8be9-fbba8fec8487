<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('syllabi', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('learn_year_id')->nullable()->constrained();
            $table->foreignId('status_id')->constrained();
            $table->foreignId('semester_id')->constrained();
            $table->foreignId('academic_degree_id')->constrained();
            $table->string('code')->unique();
            $table->unsignedBigInteger('credits');

            $table->integer('contact_hours');
            $table->integer('lecture_hours');
            $table->integer('seminar_hours');
            $table->integer('mid_and_final_exam_hours');
            $table->integer('independent_work_hours');
            $table->integer('total_hours');

            $table->longText('goal')->nullable();
            $table->longText('assessing_system')->nullable();
            $table->longText('final_exam_prerequisite')->nullable();
            $table->integer('exam_percent')->default(0);
            $table->longText('retake_missed_assignment')->nullable();

            $table->longText('main_literature')->nullable();
            $table->longText('additional_literature')->nullable();
//            $table->text('assessing_components');
            $table->longText('academic_honesty')->nullable(); //აკადემიური კეთილსინიდსიერების დაღვევა - პლაგიტი
            $table->longText('additional_information')->nullable();
            $table->longText('learning_outcome_knowledge')->nullable();//სწავლის შედეგები-ცოდნა და გაცნობიერება
            $table->longText('learning_outcome_skill')->nullable();//სწავლის შედეგები - უნარი
            $table->longText('learning_outcome_responsibility')->nullable();//სწავლის შედეგები - პასუხისმგებლობა და ავტონომიურობა
            $table->longText('exam_rules')->nullable();//სწავლის შედეგები - პასუხისმგებლობა და ავტონომიურობა
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('syllabi');
    }
};
