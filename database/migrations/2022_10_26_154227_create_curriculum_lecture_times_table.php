<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('curriculum_lecture_times', function (Blueprint $table) {
            $table->id();
            $table->foreignId('curriculum_lecture_id')->constrained('curriculum_lectures')->cascadeOnDelete();
            $table->unsignedInteger('week_day');
            $table->time('start_time');
            $table->time('end_time');
            $table->foreignId('auditorium_id')->constrained('auditoria')->cascadeOnDelete()->default(null);
            $table->foreignId('lecturer_id')->constrained('lecturers')->cascadeOnDelete();
            $table->string('lecturer_accounting_code');
            $table->float('payment_per_hour');
            $table->unsignedTinyInteger('is_lecture')->default(1);
            $table->date('lecturer_start_date');
            $table->date('lecturer_end_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lecture_times');
    }
};
