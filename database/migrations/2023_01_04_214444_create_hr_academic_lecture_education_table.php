<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_academic_lecture_education', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hr_academic_lecture_info_id');
            $table->string('qualification')->nullable();
            $table->string('country')->nullable();
            $table->foreignId('academic_degree_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_academic_lecture_education');
    }
};
