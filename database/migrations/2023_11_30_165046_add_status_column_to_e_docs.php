<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('e_docs', function (Blueprint $table) {
            $table->boolean('status')
                    ->default(0)
                    ->after('comment')
                    ->comment('1=pending;2=approved;3=declined');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('e_docs', function (Blueprint $table) {
            //
        });
    }
};
