<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bachelor_registers', function (Blueprint $table) {
            $table->id();
            $table->string('first_name_en');
            $table->string('last_name_en');
            $table->foreignId('program_id');
            $table->foreignId('flow_id');
            $table->string('parent_phone');
            $table->string('address');
            $table->string('school_document');
            $table->string('photo');
            $table->string('identity_number_copy');
            $table->string('school')->nullable();
            $table->string('military_accounting')->nullable();
            $table->string('payment_document');
            $table->foreignId('english_level_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bachelor_registers');
    }
};
