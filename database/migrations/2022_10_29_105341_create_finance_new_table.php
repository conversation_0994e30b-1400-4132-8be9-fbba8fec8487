<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_new', function (Blueprint $table) {
            $table->id();
            $table->integer('oris_id')->nullable();
            $table->string('Account1410');
            $table->string('piradi_nom');
            $table->integer('ProgramID');
            $table->integer('saswavlo_weli');
            $table->integer('Qrter');
            $table->string('PeriodFrom');
            $table->float('kontraqtis_tanxa')->nullable();
            $table->float('dam_sagnebi');
            $table->float('sareitingo_fasdakleba');
            $table->float('grantianis_fasdakleba');
            $table->float('extra');
            $table->float('sax_daxmareba');
            $table->float('sax_granti');
            $table->float('meriis_daxmareba');
            $table->float('charicxuli_studenti');
            $table->float('charicxuli_granti');
            $table->float('charicxuli_sax_daxmareba');
            $table->float('charicxuli_meriis_daxmareba');
            $table->float('akademiuris_tanxa1');
            $table->boolean('IsTrainings');
            $table->float('kvartlis_nashti');
            $table->float('kvartlis_jami');
            $table->float('nashti');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finances');
    }
};
