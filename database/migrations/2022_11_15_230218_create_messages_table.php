<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Message\MessageStatus;

return new class extends Migration {

    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable()->comment('if null it is reply');
            $table->text('body')->nullable();
            $table->foreignId('author_id');
            $table->foreignIdFor(MessageStatus::class)->default(1);
            $table->foreignId('main_message_id')->nullable()->comment('if null, it is main message, otherwise - reply');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
};
