<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        Schema::create('hr_administration_education', function (Blueprint $table) {
            $table->id();
            $table->foreignId('academic_degree_id')->nullable();
            $table->foreignId('hr_administration_info_id');
            $table->string('country')->nullable();
            $table->string('qualification')->nullable();
            $table->string('position')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_administration_education');
    }
};
