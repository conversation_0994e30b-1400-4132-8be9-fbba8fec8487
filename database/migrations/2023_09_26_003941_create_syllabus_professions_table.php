<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('syllabus_professions', function (Blueprint $table) {
            $table->id();
            $table->string('name_en')->nullable();
            $table->string('name');
            $table->foreignId('semester_id')->constrained();
            $table->foreignId('academic_degree_id')->constrained();
            $table->string('code')->unique();
            $table->unsignedBigInteger('credits');
            $table->integer('contact_hours');
            $table->integer('lecture_hours');
            $table->integer('seminar_hours');
            $table->integer('mid_and_final_exam_hours');
            $table->integer('independent_work_hours');
            $table->integer('total_hours');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('syllabus_professions');
    }
};
