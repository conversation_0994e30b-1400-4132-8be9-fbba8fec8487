<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_administration_infos', function (Blueprint $table) {
            $table->id();
            $table->string('father_name')->nullable();
            $table->boolean('gender')->nullable()
                ->comment('0 - ქალი , 1 - კაცი');
            $table->date('date_of_birth')->nullable();
            $table->integer('age')->nullable();
            $table->string('address')->nullable();
            $table->string('phone')->nullable();
            $table->boolean('family_state')->nullable()
                ->comment('დაუოჯახებელი - 0 , დასაოჯახებელი - 1');
            $table->foreignId('user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_administration_infos');
    }
};
