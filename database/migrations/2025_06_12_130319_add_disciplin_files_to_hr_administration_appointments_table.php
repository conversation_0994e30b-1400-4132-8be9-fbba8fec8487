<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hr_administration_appointments', function (Blueprint $table) {
            $table->text('disciplinary_remark_file')->nullable();
            $table->text('disciplinary_complaint_file')->nullable();
            $table->text('disciplinary_compensation_file')->nullable();
            $table->text('disciplinary_contract_termination_file')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hr_administration_appointments', function (Blueprint $table) {
            $table->dropColumn('disciplinary_remark_file');
            $table->dropColumn('disciplinary_complaint_file');
            $table->dropColumn('disciplinary_compensation_file');
            $table->dropColumn('disciplinary_contract_termination_file');
        });
    }
};
