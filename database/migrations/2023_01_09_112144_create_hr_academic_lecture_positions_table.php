<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_academic_lecture_positions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hr_academic_lecture_info_id');
            $table->foreignId('lecturer_position_id')->nullable();
            $table->boolean('grant')->nullable();
            $table->boolean('affiliated')->nullable();
            $table->foreignId('lecturer_category_id')->nullable();
            $table->integer('salary')->nullable();
            $table->integer('paid_hours')->nullable();
            $table->integer('unpaid_hours')->nullable();
            $table->string('direction')->nullable();
            $table->foreignId('school_id')->nullable();
            $table->string('appointment')->nullable();
            $table->string('vacancy_command_number')->nullable();
            $table->text('vacancy_command_number_file')->nullable();
            $table->date('vacancy_command_number_date')->nullable();
            $table->string('appointment_command_number')->nullable();
            $table->text('appointment_command_number_file')->nullable();
            $table->date('appointment_command_number_date')->nullable();
            $table->date('contract_start')->nullable();
            $table->date('contract_end')->nullable();
            $table->string('contract_period')->nullable();
            $table->boolean('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_academic_lecture_positions');
    }
};
