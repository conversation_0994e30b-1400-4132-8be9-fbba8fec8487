<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_invited_lecture_infos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('father_name')->nullable();
            $table->boolean('gender')
                ->comment('0 - ქალი , 1 - კაცი')
                ->nullable();
            $table->integer('age')->nullable();
            $table->boolean('family_state')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_invited_lecture_infos');
    }
};
