<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_assignment_last_checkouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_assignment_id')->constrained('student_assignments')->onDelete('cascade');
            $table->unsignedTinyInteger('status_id');
            $table->boolean('is_active')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_assignment_last_checkouts');
    }
};
