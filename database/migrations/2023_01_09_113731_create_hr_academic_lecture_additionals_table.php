<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hr_academic_lecture_additionals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hr_academic_lecture_info_id');
            $table->boolean('scopus_g')->nullable();
            $table->boolean('scopus_h')->nullable();
            $table->boolean('web_of_science_g')->nullable();
            $table->boolean('web_of_science_h')->nullable();
            $table->boolean('google_scholar_g')->nullable();
            $table->boolean('google_scholar_h')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hr_academic_lecture_additionals');
    }
};
