<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up()
    {
        Schema::create('method_syllabus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('syllabus_id');
            $table->foreignId('method_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('syllabus_method');
    }
};
