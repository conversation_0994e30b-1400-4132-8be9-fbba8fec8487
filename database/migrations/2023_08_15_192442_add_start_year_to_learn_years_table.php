<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('learn_years', function (Blueprint $table) {
            $table->date('start_year')->nullable()->after('hash');
            $table->date('end_year')->nullable()->after('start_year');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('learn_years', function (Blueprint $table) {
            //
        });
    }
};
