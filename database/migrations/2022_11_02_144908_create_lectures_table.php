<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lectures', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('is_current')->default(1)->index();
            $table->foreignId('syllabus_id')->constrained('syllabi');
            $table->foreignId('lecturer_id')->constrained('lecturers');
            $table->unsignedFloat('payment_per_hour')->default(0);
            $table->time('start_time');
            $table->time('end_time')->nullable();
            $table->date('lecture_date');
            $table->unsignedInteger('week_day')->index();
            $table->unsignedInteger('lecture_number')->index();
            $table->foreignId('auditorium_id')->constrained('auditoria')->nullable()->index();
            $table->unsignedTinyInteger('is_lecture')->default(1)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lectures');
    }
};
