<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('e_docs', function (Blueprint $table) {
            $table->id();
            $table->string('document_number')->nullable()->comment('connected with template index');
            $table->foreignId('user_id');
            $table->foreignId('edoc_template_id');
            $table->integer('created_by')->nullable()->comment('administrator user_id');
            $table->boolean('created')->default(0);
            $table->longText('text')->nullable();
            $table->dateTime('opened_at')->nullable();
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('e_docs');
    }
};
