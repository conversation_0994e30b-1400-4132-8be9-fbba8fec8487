<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('training_register_education', function (Blueprint $table) {
            $table->string('university')->nullable()->change();
            $table->string('faculty')->nullable()->change();
            $table->unsignedBigInteger('academic_degree_id')->nullable()->change();

            $table->integer('start_date')->nullable()->change();
            $table->integer('end_date')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('training_register_education', function (Blueprint $table) {
            $table->string('university')->nullable(false)->change();
            $table->string('faculty')->nullable(false)->change();
            $table->unsignedBigInteger('academic_degree_id')->nullable(false)->change();

            $table->integer('start_date')->nullable(false)->change();
            $table->integer('end_date')->nullable(false)->change();
        });
    }
};
