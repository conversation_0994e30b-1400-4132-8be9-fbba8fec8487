<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('program_additionals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('program_id')->constrained('programs')->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('kvalifikacia', 100)->nullable();
            $table->string('kvalifikacia_eng', 255)->nullable();
            $table->string('dargebi', 100)->nullable();
            $table->string('dargebi_eng', 255)->nullable();
            $table->string('swavlis_ena', 100)->nullable();
            $table->string('swavlis_ena_eng', 100)->nullable();
            $table->integer('safasuri')->nullable();
            $table->text('kvalifikaciis_done')->nullable();
            $table->text('kvalifikaciis_done_eng')->nullable();
            $table->string('xangrdzlivoba', 100)->nullable();
            $table->string('xangrdzlivoba_eng', 100)->nullable();
            $table->text('migebis_motxovnebi')->nullable();
            $table->text('migebis_motxovnebi_eng')->nullable();
            $table->text('other_name')->nullable();
            $table->text('other_name_eng')->nullable();
            $table->text('dashveba')->nullable();
            $table->text('dashveba2')->nullable();
            $table->text('dashveba_eng')->nullable();
            $table->text('dashveba2_eng')->nullable();
            $table->longText('programis_motxovnebi')->nullable();
            $table->longText('programis_motxovnebi_eng')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('program_additionals');
    }
};
