<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('training_register_works', function (Blueprint $table) {
            $table->id();
            $table->foreignId('training_register_id');
            $table->string('organization');
            $table->string('position');
            $table->integer('employment_field');
            $table->integer('start_date');
            $table->integer('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('training_register_works');
    }
};
