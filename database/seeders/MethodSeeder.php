<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $methods =
            [

                [
                    'title' => 'დისკუსია / დებატები',
                    'text'=> 'დისკუსიის პროცესი მკვეთრად ამაღლებს სტუდენტთა ჩართულობის ხარისხსა და აქტივობას . დისკუსია შესაძლებელია გადაიზარდოს კამათში . ეს პროცესი არ  შემოიფარგლება მხოლოდ პროფესორის  მიერ დასმული შეკითხვებით . ეს მეთოდი უვითარებს სტუდენტს კამათისა და საკუთარი აზრის დასაბუთების უნარს . '
                ],
                [
                    'title' => 'ჯგუფური მუშაობა',
                    'text'=> 'ამ მეთოდით სწავლება გულისხმობს სტუდენტთა ჯგუფებად დაყოფას და მათთვის სასწავლო დავალების მიცემას . ჯგუფის წევრები ინდივიდუალურად ამუშავებენ საკითხს და პარალელურად უზიარებენ მას ჯგუფის დანარჩენ წევრებს . დასახული ამოცანიდან გამომდინარე შესაძლებელია ჯგუფის მუშაობის პროცესში წევრებს შორის მოხდეს ფუნქციების გადანაწილება . ეს სტრატეგია უზრუნველყოფს ყველა სტუდენტის  მაქსიმალურ ჩართულობას სასწავლო პროცესში . '
                ],
                [
                    'title' => 'შემთხვევის ანალიზი(case study)',
                    'text'=> 'პროფესორი სტუდენტებთან ერთად ლექციაზე განიხილავს კონკრეტულ შემთხვევებს, რომლებიც ყოველმხრივ და საფუძვლიანად შეისწავლიან საკითხს . მაგალითად,  კაზუსის ამოხსნა . '
                ],
                [
                    'title' => 'გონებრივი იერიში(Brain storming)',
                    'text'=> ' ეს მეთოდი გულისხმობს კონკრეტული თემის ფარგლებში კონკრეტული საკითხის / პრობლემის შესახებ  მაქსიმალურად მეტი, სასურველია რადიკალურად განსხვავებული, აზრის, იდეის ჩამოყალიბებასა და მისი გამოთქმის ხელშეწყობას . აღნიშნული მეთოდი ხელს უწყობს პრობლემისადმი შემოქმედებითი მიდგომის განვითარებას . '
                ],
                [
                    'title' => 'ქმედებაზე ორიენტირებული სწავლება(learning by doing)',
                    'text'=> 'მოითხოვს პროფესორისა და სტუდენტის აქტიურ ჩართულობას სწავლების პროცესში, სადაც განსაკუთრებულ დატვირთვას იძენს თეორიული მასალის პრაქტიკული ინტერპრეტაცია . ქმედებაზე ორიენტირებული სწავლებისას სტუდენტებმა მასწავლებლის დახმარებით უნდა შეძლონ სიტუაციაზე დაყრდნობით სასწავლო ამოცანების გაცნობიერება და მიზნის დასახვა, საწყისი სიტუაციის აღწერითა და ანალიზით უნდა გამოკვეთონ პრობლემები და განსაზღვრონ პრობლების გადაჭრისთვის თანმიმდევრული ნაბიჯები, შემდეგ გააფორმონ მოქმედების გეგმა და დაიწყონ მისი განხორციელება, რომელსაც პროცესში მუდმივად გააკონტროლებენ, რამდენად სწორად მიდიან შედეგისკენ . '
                ],
                [
                    'title' => 'ევრისტიკული მეთოდი',
                    'text'=> 'ეფუძნება სტუდენტების წინაშე დასმული ამოცანის ეტაპობრივ გადაწყვეტას . ეს პროცესი სწავლებისას ფაქტების დამოუკიდებლად დაფიქსირებისა და მათ შორის კავშირების დანახვის გზით ხორციელდება . '
                ],
                [
                    'title' => 'როლური და სიტუაციური თამაშები',
                    'text'=> 'წინასწარ შემუშავებული სცენარის მიხედვით განხორციელებული თამაშები სტუდენტებს საშუალებას აძლევს სხვადასხვა პოზიციიდან შეხედონ საკითხს . იგი ეხმარება მათ ალტერნატიული თვალსაზრისის ჩამოყალიბებაში . ისევე როგორც დისკუსია, ეს თამაშებიც უყალიბებს სტუდენტს საკუთარი პოზიციის დამოუკიდებლად გამოთქმისა და კამათში მისი დაცვის უნარს . აღნიშნული მეთოდის გამოყენებით სასწავლო პროცესი მაქსიმალურად ემსგავება რეალურ სამუშაო / პრაქტიკულ გარემოს . '
                ],
                [
                    'title' => 'ინდუქციური მეთოდი',
                    'text'=> 'განსაზღვრავს ნებისმიერი ცოდნის გადაცემის ისეთ ფორმას, როდესაც სწავლის პროცესში აზრის მსვლელობა ფაქტებიდან განზოგადებისაკენ არის მიმართული ანუ მასალის გადმოცემისას პროცესი მიმდინარეობს კონკრეტულიდან ზოგადისკენ . '
                ],
                [
                    'title' => 'დედუქციური მეთოდი',
                    'text'=> 'განსაზღვრავს ნებისმიერი ცოდნის გადაცემის ისეთ ფორმას, რომელიც ზოგად ცოდნაზე დაყრდნობით ახალი ცოდნის აღმოჩენის ლოგიკურ პროცესს წარმოადგენს ანუ პროცესი მიმდინარეობს ზოგადიდან კონკრეტულისაკენ . '
                ],
                [
                    'title' => 'ახსნა - განმარტებითი მეთოდი',
                    'text'=> 'ეფუძნება მსჯელობას მოცემული საკითხის ირგვლივ . ლექტორი მასალის გადმოცემისას მოჰყავს კონკრეტული მაგალითი, რომლის დაწვრილებით განხილვაც ხდება მოცემული თემის ფარგლებში . '
                ],
                [
                    'title' => 'თანამშრომლობითი(cooperative) სწავლება',
                    'text'=> 'იმგვარი სწავლების სტრატეგიაა, სადაც ჯგუფის თითოეული წევრი ვალდებულია არა მხოლოდ თვითონ შეისწავლოს, არამედ დაეხმაროს თავის თანაგუნდელს საგნის უკეთ შესწავლაში . თითოეული ჯგუფის წევრი მუშაობს პრობლემაზე, ვიდრე ყველა მათგანი არ დაეუფლება საკითხს . '
                ],
                [
                    'title' => 'პრობლემაზე დაფუძნებული სწავლება(PBL)',
                    'text'=> 'არის აქტიური სწავლების პედაგოგიკა, რომელიც ეფუძნება მცირე ჯგუფში კოლაბორაციული სწავლების იდეას იმ სტუდენტებთან, რომლებიც აქტიურად არიან პასუხისმგებელი საკუთარი სწავლის პროცესზე და უკვე არსებულ ცოდნასთან დაკავშირების მეშვეობით ახალი ცოდნის კონსტრუქციაზე . პრობლემაზე დაფუძნებული სწავლის პროცესი სტუდენტს აყენებს აქტიური შემმეცნებლის როლში, რომელსაც წამოჭრილ პრობლემებთან გამკლავება უხდება . კერძოდ, პრობლემაზე დაფუძნებული სწავლებისას სტუდენტი აწყდება პრობლემას, რომლის გადაჭრაც მას უჭირს მხოლოდ იმ ცოდნით, რომელიც აქვს . იგი არკვევს, თუ რა უნდა იცოდეს დასმული პრობლემის უკეთ გასაგებად . ამ პროცესში მოსწავლე აქტიურია, მიმართავს თვითგანათლებას, ეძებს საჭირო ინფორმაციას(წიგნებს, გამოკვლევებს, ანგარიშებს, ელექტრონულ ინფორმაციას) და ა . შ . ამის შემდეგ ახალი ცოდნით აღჭურვილი სტუდენტი კვლავ უბრუნდება პრობლემას და ახერეხებს მის გადაჭრას . '
                ],
                [
                    'title' => 'დემონსტრირების(პრეზენტაციის) მეთოდი',
                    'text'=> 'ეს მეთოდი ინფორმაციის ვიზუალურად წარმოდგენას გულისხმობს . შედეგის მიღწევის თვალსაზრისით ის საკმაოდ შესასწავლი მასალის დემონსტრირება შესაძლებელია როგორც ლექტორის,  ასევე სტუდენტის მიერ . ეს მეთოდი გვეხმარება თვალსაჩინო გავხადოთ სასწავლო მასალის აღქმის სხვადასხვა საფეხური, დავაკონკრეტოთ, თუ რისი შესრულება მოუწევთ სტუდენტებს დამოუკიდებლად;'
                ],
                [
                    'title' => 'სინთეზის მეთოდი',
                    'text'=> 'გულისხმობს ცალკეული საკითხების დაჯგუფებით ერთი მთლიანის შედგენას . ეს მეთოდი ხელს უწყობს პრობლემის, როგორც მთლიანის დანახვის უნარის განვითარებას . '
                ]
            ];
        DB::table('methods')->insert($methods);
    }
}
