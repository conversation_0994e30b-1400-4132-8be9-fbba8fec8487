<?php

namespace Database\Seeders;

use App\Models\Finance\Finance;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FinanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            2020 => [
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2020,
                    'Qrter' => 1,
                    'PeriodFrom' => '2020-1',
                    'kontraqtis_tanxa' => 1600,
                    'dam_sagnebi' => 100,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 100,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 200,
                    'kvartlis_jami' => 1200,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2020,
                    'Qrter' => 2,
                    'PeriodFrom' => '2020-2',
                    'kontraqtis_tanxa' => 1600,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 100,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 400,
                    'kvartlis_jami' => 400,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2020,
                    'Qrter' => 3,
                    'PeriodFrom' => '2020-3',
                    'kontraqtis_tanxa' => 1600,
                    'dam_sagnebi' => 100,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 100,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 200,
                    'kvartlis_jami' => 200,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2020,
                    'Qrter' => 4,
                    'PeriodFrom' => '2020-4',
                    'kontraqtis_tanxa' => 1600,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 100,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 100,
                    'kvartlis_jami' => 100,
                    'nashti' => 0
                ]
            ],
            2021 => [
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2021,
                    'Qrter' => 1,
                    'PeriodFrom' => '2021-1',
                    'kontraqtis_tanxa' => 1200,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 600,
                    'kvartlis_jami' => 4800,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2021,
                    'Qrter' => 2,
                    'PeriodFrom' => '2021-2',
                    'kontraqtis_tanxa' => 1200,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 300,
                    'kvartlis_jami' => 1200,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2021,
                    'Qrter' => 3,
                    'PeriodFrom' => '2021-3',
                    'kontraqtis_tanxa' => 1200,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1200,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 20,
                    'kvartlis_jami' => 500,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2021,
                    'Qrter' => 4,
                    'PeriodFrom' => '2021-4',
                    'kontraqtis_tanxa' => 1200,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 0,
                    'kvartlis_jami' => 100,
                    'nashti' => 0
                ],
            ],
            2022 => [
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2022,
                    'Qrter' => 1,
                    'PeriodFrom' => '2022-1',
                    'kontraqtis_tanxa' => 1500,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 200,
                    'kvartlis_jami' => 4700,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2022,
                    'Qrter' => 2,
                    'PeriodFrom' => '2022-2',
                    'kontraqtis_tanxa' => 1500,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 400,
                    'kvartlis_jami' => 3600,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2022,
                    'Qrter' => 3,
                    'PeriodFrom' => '2022-3',
                    'kontraqtis_tanxa' => 1500,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 100,
                    'kvartlis_jami' => 2700,
                    'nashti' => 0
                ],
                [
                    'Account1410' => '1300-200-313-100',
                    'piradi_nom' => '***********',
                    'ProgramID' => 1,
                    'saswavlo_weli' => 2022,
                    'Qrter' => 4,
                    'PeriodFrom' => '2022-4',
                    'kontraqtis_tanxa' => 1500,
                    'dam_sagnebi' => 0,
                    'sareitingo_fasdakleba' => 0,
                    'grantianis_fasdakleba' => 0,
                    'extra' => 0,
                    'sax_granti' => 0,
                    'sax_daxmareba' => 0,
                    'meriis_daxmareba' => 0,
                    'charicxuli_studenti' => 1400,
                    'charicxuli_granti' => 0,
                    'charicxuli_sax_daxmareba' => 0,
                    'charicxuli_meriis_daxmareba' => 100,
                    'akademiuris_tanxa1' => 0,
                    'IsTrainings' => 0,
                    'kvartlis_nashti' => 0,
                    'kvartlis_jami' => 200,
                    'nashti' => 0
                ],
            ],
        ];
        Finance::insert($data[2020]);
        Finance::insert($data[2021]);
        Finance::insert($data[2022]);
    }
}
