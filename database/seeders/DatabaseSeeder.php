<?php

namespace Database\Seeders;

use App\Models\HR\Lecturer\Academic\LecturerCategory;
use Database\Seeders\News\NewsSeeder;
use Database\Seeders\Reestry\AcademicDegreesSeeder;
use Database\Seeders\Reestry\AdministrationSeeder;
use Database\Seeders\Reestry\AuditoriumSeeder;
use Database\Seeders\Reestry\CampusSeeder;
use Database\Seeders\Reestry\DirectionSeeder;
use Database\Seeders\Reestry\FlowSeeder;
use Database\Seeders\Reestry\LecturerSeeder;
use Database\Seeders\Reestry\SchoolSeeder;
use Database\Seeders\Reestry\StatusSeeder;
use Database\Seeders\Reestry\StudentSeeder;
use Database\Seeders\Reestry\StudentStatusSeeder;
use Database\Seeders\Survey\SurveyActivationSeeder;
use Database\Seeders\Survey\SurveyAnswerSeeder;
use Database\Seeders\Survey\SurveyAnswersSeeder;
use Database\Seeders\Survey\SurveyQuestionSeeder;
use Database\Seeders\Survey\SurveyQuestionTypeSeeder;
use Database\Seeders\Survey\SurveySeeder;
use Database\Seeders\Survey\SurveySettingSeeder;
use Database\Seeders\Survey\SurveySettingTypeSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            StudentStatusSeeder::class,
            UserTypeSeeder::class,
            UserSeeder::class,
            RoleSeeder::class,
            PermissionSeeder::class,
            RolePermissionSeeder::class,
            UserRoleSeeder::class,
            StatusSeeder::class,
            AcademicDegreesSeeder::class,
            LecturerSeeder::class,
            AdministrationSeeder::class,
            AuditoriumSeeder::class,
            CampusSeeder::class,
            DirectionSeeder::class,
            SchoolSeeder::class,
            FlowSeeder::class,
            StudentSeeder::class,
            SemesterSeeder::class,
            SyllabusSeeder::class,
            LearningOutcomeSeeder::class,
            OwnSeeder::class,
            EnglishLevelSeeder::class,
            MethodSeeder::class,
            MessageSeeder::class,
            FinanceSeeder::class,
            FinanceLogSeeder::class,
            MessageStatusSeeder::class,
            WorkTypeSeeder::class,
            LecturerPositionSeeder::class,
            LecturerCategorySeeder::class,
            NewsSeeder::class,
            EdocTemplateSeeder::class,
            EDocSeeder::class,
            SurveySeeder::class,
            SurveySettingTypeSeeder::class,
            SurveySettingSeeder::class,
            SurveyQuestionTypeSeeder::class,
            SurveyQuestionSeeder::class,
            SurveyActivationSeeder::class,
            SurveyAnswerSeeder::class,
            AssessmentComponentsSeeder::class,
            BachelorsTempSeeder::class,
            SettingsSeeder::class,
            DataUpdateSeeder::class,
            SeveritySeeder::class
        ]);

    }
}
