<?php

namespace Database\Seeders;

use App\Models\AssessmentComponent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AssessmentComponentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $components = [
            [
                'name_ka' => 'კომპონენტი 1 ქართული',
                'name_en' => 'Component 1 English',
                'type_id' => 1,
                'is_parent' => 0
            ],
            [
                'name_ka' => 'კომპონენტი 2 ქართული',
                'name_en' => 'Component 2 English',
                'type_id' => 2,
                'is_parent' => 1
            ],
            [
                'name_ka' => 'კომპონენტი 3 ქართული',
                'name_en' => 'Component 3 English',
                'type_id' => 3,
                'is_parent' => 0
            ],
        ];

        foreach ($components as $component) {
            AssessmentComponent::create($component);
        }
    }
}
