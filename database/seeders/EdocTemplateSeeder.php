<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EdocTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $edoc = [
            [
                'name' => 'განაცხადი თანხის გადავადების შესახებ',
                'index' => '01-25/1',
                'text' => "აღწერა",
                'automatic' => 0,
                'user_type_id' => 3
            ],
            [
                'name' => 'ფორმა 26',
                'index' => '01-23/1',
                'text' => "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                'automatic' => 0,
                'user_type_id' => 1
            ],
            [
                'name' => 'ცნობა სტატუსის შესახებ',
                'index' => '01-24/1',
                'text' => "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                'automatic' => 0,
                'user_type_id' => 3
            ]
        ];
        DB::table('edoc_templates')->insert($edoc);
    }
}
