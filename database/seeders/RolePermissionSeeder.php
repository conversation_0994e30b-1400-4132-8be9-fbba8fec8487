<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roleId = 1;


        $role = Role::find($roleId);

        if ($role) {
            $permissions = Permission::all();

            foreach ($permissions as $permission) {
                if (!$role->permissions->contains($permission)) {
                    $role->permissions()->attach($permission->id);
                }
            }
        }
    }
}
