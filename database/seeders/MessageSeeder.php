<?php

namespace Database\Seeders;

use App\Models\Message\Message;
use Illuminate\Database\Seeder;

class MessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            'title' => "ფინალურის აღდგენა",
            'body' => 'გთხოვთ, დამრთოთ ნება აღვადგინო გაცდენილი ფინალური გამოცდა',
            'author_id' => 2,
            'message_status_id' => 1,
            'main_message_id' => null,
        ];
        $message = Message::create($data);
        $message->addresses()->create([
            'user_id' => 1,
            'message_status_id' => 1,
            'viewed_at' => now(),
        ]);


    }
}
