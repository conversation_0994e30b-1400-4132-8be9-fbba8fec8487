<?php

namespace Database\Seeders;

use App\Models\Setting;
use DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Setting::create(['key' => 'current_semester', 'value' => '1']);
        Setting::create(['key' => 'learn_year', 'value' => '2022']);
        Setting::create(['key' => 'finance_start_learn_year', 'value' => '01/09/2022']);
        Setting::create(['key' => 'finance_learn_year_manual', 'value' => '01/09/2022,31/08/2023']);
        Setting::create(['key' => 'finance_learn_year_auto', 'value' => '01/09/2022,30/06/2023']);
        Setting::create(['key' => 'elective_subjects_learn_year_id', 'value' => 1]);
    }
}
