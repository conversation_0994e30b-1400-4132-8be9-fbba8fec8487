<?php

namespace Database\Seeders;

use App\Models\User\User;
use App\Models\User\UserType;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = [
            [
                'email' => '<EMAIL>',
                'name' => 'Administrator',
                'password' => bcrypt('password'),
                'user_type_id' => UserType::ADMINISTRATION,
                'is_super_admin' => 1
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Lecturer',
                'password' => bcrypt('password'),
                'user_type_id' => UserType::LECTURER
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Student',
                'password' => bcrypt('password'),
                'user_type_id' => UserType::STUDENT
            ]
        ];

        foreach ($users as $user) {
            User::updateOrCreate(
                [
                    'email' => $user['email']
                ],
                [
                    'name' => $user['name'],
                    'password' => $user['password'],
                    'user_type_id' => $user['user_type_id'],
                    'is_super_admin' => $user['is_super_admin'] ?? 0
                ],
            );
        }
    }
}
