<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Role::create(['title' => 'Super Administrator']);
        Role::create(['title' => 'School Administrator']);
        Role::create(['title' => 'Coordinator']);
        Role::create(['title' => 'Financial Manager']);
        Role::create(['title' => 'Quality Manager']);
        Role::create(['title' => 'Demo']);
        Role::create(['title' => 'Intern']);
    }
}
