<?php

namespace Database\Seeders\Survey;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SurveyAnswerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'survey_activation_id' => 1,
                'survey_question_id' => 1,
                'answer_int' => 1,
                'comment' => "პირველი კომენტარი"
            ],
            [
                'survey_activation_id' => 1,
                'survey_question_id' => 1,
                'answer_int' => 4,
                'comment' => "მეორე კომენტარი"
            ],
            [
                'survey_activation_id' => 1,
                'survey_question_id' => 1,
                'answer_int' => 5,
                'comment' => "მესამე კომენტარი"
            ]

        ];
        DB::table('survey_answers')->insert($data);
    }
}
