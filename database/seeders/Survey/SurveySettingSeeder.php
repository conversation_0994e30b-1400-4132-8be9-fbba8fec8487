<?php

namespace Database\Seeders\Survey;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SurveySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'survey_id' => 1,
                'survey_setting_type_id' => 1,
                'school_id' => 1
            ],
            [
                'survey_id' => 2,
                'survey_setting_type_id' => 2,
                'school_id' => Null
            ]
        ];
        DB::table('survey_settings')->insert($data);
    }
}
