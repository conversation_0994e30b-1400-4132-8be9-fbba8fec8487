<?php

namespace Database\Seeders\Survey;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SurveySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'name' => 'ლექტორის შეფასება',
                'description' => 'კითხვარი განკუთვნილია ჯგუფის მიერ ლექტორის შეფასებისთვის'
            ],
            [
                'name' => 'საუნივერსიტეტო',
                'description' => 'კითხვარი განკუთვნილია ჯგუფის მიერ ლექტორის შეფასებისთვის 2'
            ]
        ];
        DB::table('surveys')->insert($data);
    }
}
