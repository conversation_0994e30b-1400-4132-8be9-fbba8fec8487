<?php

namespace Database\Seeders\Survey;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SurveyQuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'survey_id' => 1,
                'survey_question_type_id' => 1,
                'name' => 'კითხვა 1',
                'question_required' => 0,
                'comment_required' => 0
            ],
            [
                'survey_id' => 2,
                'survey_question_type_id' => 2,
                'name' => 'კითხვა 2',
                'question_required' => 1,
                'comment_required' => 1
            ]
        ];
        DB::table('survey_questions')->insert($data);
    }
}
