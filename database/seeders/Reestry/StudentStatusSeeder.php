<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\Student\StudentStatusList;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StudentStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (StudentStatusList::STATUSES as $status) {
            StudentStatusList::create([
                'name_en' => $status[0],
                'name_ka' => $status[1]
            ]);
        }
    }
}
