<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\User\User;
use Illuminate\Database\Seeder;

class LecturerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //Lecturer::factory()->hasDirections(3)->for(User::factory()->create())->create();
        $users = User::factory()->count(5)->create();
        Lecturer::factory()
            ->hasDirections(3)
            ->count(5)
            ->sequence(
                fn ($sequence) => ['user_id' => $users[$sequence->index]->id]
            )
            ->create();
    }
}
