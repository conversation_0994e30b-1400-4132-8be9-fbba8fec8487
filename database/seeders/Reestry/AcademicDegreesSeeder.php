<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\AcademicDegree;
use Illuminate\Database\Seeder;

class AcademicDegreesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (AcademicDegree::DEGREES as $degree) {
            AcademicDegree::create([
                'name_en' => $degree[0],
                'name_ka' => $degree[1],
                'url' => $degree[2]
            ]);
        }
    }
}
