<?php

namespace Database\Seeders\Reestry;

use DB;
use Illuminate\Database\Seeder;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $statuses = [
            ['name_ka' => "სავალდებულო", 'name_en' => 'mandatory'],
            ['name_ka' => "სავალდებულო არჩევითი", 'name_en' => 'mandatory optional'],
            ['name_ka' => "არჩევითი", 'name_en' => 'optional'],
        ];

        DB::table('statuses')->insert($statuses);
    }
}
