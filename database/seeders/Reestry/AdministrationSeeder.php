<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Administration\AdministrationPosition;
use App\Models\User\User;
use Illuminate\Database\Seeder;

class   AdministrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $administrationPosition = AdministrationPosition::factory()->create();
        Administration::factory()->for($administrationPosition)->create();
    }
}
