<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\Campus;
use Illuminate\Database\Seeder;

class CampusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
//        $campuses = [
//            [
//                'name_en' => 'N/A',
//                'name_ka' => 'გორგასალის კამპუსი',
//                'address_ka' => 'N/A',
//                'address_en' => 'N/A',
//            ],
//            [
//                'name_en' => 'N/A',
//                'name_ka' => 'იეთიმ გურჯის კამპუსი',
//                'address_ka' => 'N/A',
//                'address_en' => 'N/A',
//            ],
//            [
//                'name_en' => 'N/A',
//                'name_ka' => 'ბროსეს კამპუსი',
//                'address_ka' => 'N/A',
//                'address_en' => 'N/A',
//            ],
//            [
//                'name_en' => 'N/A',
//                'name_ka' => 'ფანჯიკიძის კამპუსი',
//                'address_ka' => 'N/A',
//                'address_en' => 'N/A',
//            ],
//            [
//                'name_en' => 'N/A',
//                'name_ka' => 'ფრონტლაინის კამპუსი',
//                'address_ka' => 'N/A',
//                'address_en' => 'N/A',
//            ]
//        ];
        Campus::factory()->create();
    }
}
