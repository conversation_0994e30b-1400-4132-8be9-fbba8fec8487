<?php

namespace Database\Seeders\Reestry;

use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentBasicsOfEnrollment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\User\User;
use Illuminate\Database\Seeder;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::factory()->count(5)->create();
        $status = 1;
        $basicOfEnrollment = StudentBasicsOfEnrollment::factory()->create();
        $school = School::factory()->create();
        $program = Program::factory()->create();
        $group = StudentGroup::factory()->create();
        $learnYear = LearnYear::factory()->create();
        $counter = 0;
        Student::factory()
            ->count(5)
            ->hasStatus()
            ->hasBasicOfEnrollment()
            ->hasLearnYear()
            ->for($school)
            ->for($program)
            ->for($group)
            ->state(function ($attributes) use (&$counter, $users) {
                $user = $counter === 0 ? User::find(3) : $users->shift();
                $counter++;

                return ['user_id' => $user ? $user->id : null];
            })
            ->create();
//        $user = User::factory()->create();
//        $status = StudentStatusList::factory()->create();
//        $basicOfEnrollment = StudentBasicsOfEnrollment::factory()->create();
//        $school = School::factory()->create();
//        $program = Program::factory()->create();
//        $group = StudentGroup::factory()->create();
//        $learnYear = LearnYear::factory()->create();
//        Student::factory()
//            ->count(5)
//            ->for($user)
//            ->hasStatus()
//            ->hasBasicOfEnrollment()
//            ->for($school)
//            ->for($program)
//            ->for($group)
//            ->hasLearnYear()
//            ->create();
    }
}
