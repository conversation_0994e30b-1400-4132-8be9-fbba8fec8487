<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use phpDocumentor\Reflection\Types\Null_;

class EDocSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'user_id' => 1,
                'edoc_template_id' => 1,
                'created' => 0,
                'created_by' => Null,
                'document_number' => '01-23/1',
                'text' => "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                'opened_at' => Carbon::createFromFormat(
                    'd/m/Y H:i:s',
                    '10/02/2023 14:00:00'
                )
            ],
            [
                'user_id' => 2,
                'edoc_template_id' => 2,
                'created' => 0,
                'created_by' => Null,
                'document_number' => '01-24/1',
                'text' => "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                'opened_at' => Carbon::createFromFormat(
                    'd/m/Y H:i:s',
                    '10/02/2023 14:00:00'
                )
            ],
            [
                'user_id' => 1,
                'edoc_template_id' => 1,
                'created' => 1,
                'created_by' => 1,
                'document_number' => '01-24/2',
                'text' => "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
                'opened_at' => Carbon::createFromFormat(
                    'd/m/Y H:i:s',
                    '10/02/2023 14:00:00'
                )
            ]
        ];

        DB::table('e_docs')->insert($data);
    }
}
