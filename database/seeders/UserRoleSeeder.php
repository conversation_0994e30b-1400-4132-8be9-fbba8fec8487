<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\RoleUser;
use App\Models\User\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roleId = 1;
        $userId = 1;

        $role = Role::find($roleId);
        $user = User::find($userId);

        if ($role && $user) {
            $existingRoleUser = RoleUser::where('user_id', $userId)
                ->where('role_id', $roleId)
                ->first();

            if (!$existingRoleUser) {
                $roleUser = new RoleUser();
                $roleUser->user_id = $user->id;
                $roleUser->role_id = $role->id;
                $roleUser->save();
            }
        }
    }
}
