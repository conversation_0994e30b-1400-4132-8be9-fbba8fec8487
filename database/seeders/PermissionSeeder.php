<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $permissions = [
            'students.index',
            'students.store',
            'students.show',
            'students.update',
            'students.destroy',
            'students.export',
            'lecturers.index',
            'lecturers.store',
            'lecturers.show',
            'lecturers.update',
            'lecturers.destroy',
            'lecturers.export',
            'administrations.index',
            'administrations.store',
            'administrations.show',
            'administrations.update',
            'administrations.destroy',
            'administrations.export',
            'administration-positions.index',
            'administration-positions.store',
            'administration-positions.show',
            'administration-positions.update',
            'administration-positions.destroy',
            'administration-positions.export',
            'administration-items.index',
            'administration-items.store',
            'administration-items.show',
            'administration-items.update',
            'administration-items.destroy',
            'administration-items.export',
            'student-groups.index',
            'student-groups.store',
            'student-groups.show',
            'student-groups.update',
            'student-groups.destroy',
            'student-groups.export',
            'auditoriums.index',
            'auditoriums.store',
            'auditoriums.show',
            'auditoriums.update',
            'auditoriums.destroy',
            'auditoriums.export',
            'campuses.index',
            'campuses.store',
            'campuses.show',
            'campuses.update',
            'campuses.destroy',
            'campuses.export',
            'learn-years.index',
            'learn-years.store',
            'learn-years.show',
            'learn-years.update',
            'learn-years.destroy',
            'flows.index',
            'flows.store',
            'flows.show',
            'flows.update',
            'flows.destroy',
            'flows.export',
            'programs.index',
            'programs.store',
            'programs.show',
            'programs.update',
            'programs.destroy',
            'programs.export',
            'schools.index',
            'schools.store',
            'schools.show',
            'schools.update',
            'schools.destroy',
            'schools.export',
            'assessments.index',
            'assessments.store',
            'assessments.show',
            'assessments.update',
            'assessments.destroy',
            'library.index',
            'library.store',
            'library.show',
            'library.update',
            'library.destroy',
            'calendar.index',
            'calendar.update',
            'finances.index',
            'permissions.index',
            'permissions.store',
            'permissions.show',
            'permissions.update',
            'permissions.destroy',
            'roles.index',
            'roles.store',
            'roles.show',
            'roles.update',
            'roles.destroy',
            'syllabus.index',
            'syllabus.create',
            'syllabus.store',
            'syllabus.show',
            'syllabus.edit',
            'syllabus.update',
            'syllabus.destroy',
            'syllabus.export',
            'library-subject.index',
            'library-subject.store',
            'library-subject.show',
            'library-subject.update',
            'library-subject.destroy',
            'curriculum.index',
            'curriculum.copyCurriculum',
            'curriculum.store',
            'edoc-templates.index',
            'edoc-templates.store',
            'edoc-templates.show',
            'edoc-templates.update',
            'edoc-templates.destroy',
            'edoc-inbox.index',
            'edoc-sent.index',
            'edoc.store',
            'edoc.update',
            'edoc.view',
            'edoc.exportPDF',
            'hr-academic-lecturers.index',
            'hr-academic-lecturers.store',
            'hr-administrations.index',
            'hr-administrations.store',
            'hr-invited-lecturers.index',
            'hr-invited-lecturers.store',
            'news.index',
            'news.store',
            'news.show',
            'news.update',
            'news.destroy',
            'surveys.index',
            'surveys.store',
            'surveys.show',
            'surveys.edit',
            'surveys.update',
            'surveys.destroy',
            'surveys.analyse',
            'bachelor.index',
            'bachelor.destroy',
            'master.index',
            'master.destroy',
            'phd.index',
            'phd.destroy',
            'hse.index',
            'hse.destroy',
            'tcc.index',
            'tcc.destroy',
            'calendar_events.store',
            'calendar_events.show',
            'calendar_events.update',
            'calendar_events.destroy'

        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['title' => $permission]);
        }
    }

}
