<?php

namespace Database\Seeders;

use App\Models\Severity;
use FontLib\Table\Type\post;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SeveritySeeder extends Seeder
{

    const SEVERITIES = [
        'Low',
        'Medium',
        'High',
        'Critical'
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (self::SEVERITIES as $severity) {
            Severity::create([
                'name' => $severity
            ]);
        }
    }
}
