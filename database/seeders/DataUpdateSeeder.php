<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DataUpdateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('lecturers')->where('id', 1)->update([
            'user_id' => 2,
        ]);
        DB::table('schools')->where('id', 1)->update([
            'campus_id' => 1,
        ]);
        DB::table('programs')->where('id', 1)->update([
            'school_id' => 1,
        ]);
        DB::table('students')->where('id', 1)->update([
            'personal_id' => 37001054189,
        ]);
        DB::table('students')->where('id', 2)->update([
            'personal_id' => 37001054182,
        ]);
        DB::table('students')->where('id', 3)->update([
            'personal_id' => 37001054181,
        ]);
    }
}
