<?php

namespace Database\Seeders;

use App\Jobs\Curriculum\GenerateLectureJob;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Auditorium;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Semester;
use App\Models\Status;
use App\Models\Syllabus\Syllabus;
use Illuminate\Database\Seeder;

class SyllabusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $learnYear = LearnYear::factory()->create();
        $status    = Status::first();
        $semester  = Semester::first();
        $degree    = AcademicDegree::first();

        $syllabi = Syllabus::factory()
            ->count(3)
            ->for($learnYear)
            ->for($status)
            ->for($semester)
            ->for($degree)
            ->create();

        $lecturer = Lecturer::first();

        foreach ($syllabi as $syllabus) {
            $curriculum = Curriculum::factory()
                ->for($syllabus)
                ->create();

            $lectures = CurriculumLecture::factory()
                ->count(3)
                ->for($curriculum)
                ->create();

            foreach ($lectures as $lecture) {
                $auditorium = Auditorium::orderByRaw('RAND()')->first();

                CurriculumLectureTime::factory()
                    ->count(3)
                    ->for($lecture)
                    ->for($lecturer)
                    ->for($auditorium)
                    ->create();
            }
            $studentGroups = [1];
            (new GenerateLectureJob(Curriculum::find($curriculum->id),$studentGroups))->handle();
        }
    }
}
