<?php

namespace Database\Factories\Curriculum;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Curriculum\CurriculumLecture>
 */
class CurriculumLectureFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'free_places' => $this->faker->numberBetween(0, 30),
            'registered_free_students' => 0,
            'lectures_count' => random_int(1, 3)
        ];
    }
}
