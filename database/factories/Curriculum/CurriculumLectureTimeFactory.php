<?php

namespace Database\Factories\Curriculum;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Curriculum\CurriculumLectureTime>
 */
class CurriculumLectureTimeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'auditorium_id' => $this->faker->numberBetween(1, 10),
            'week_day' => $this->faker->numberBetween(1, 7),
            'start_time' => $this->faker->time(),
            'end_time' => $this->faker->time(),
            'payment_per_hour' => $this->faker->numberBetween(100, 1000),
            'is_lecture' => $this->faker->boolean(),
            'lecturer_id' => $this->faker->numberBetween(1, 10),
            'lecturer_accounting_code' => Str::random(10),
            'lecturer_start_date' => $this->faker->dateTimeBetween('now', '+1 week'),
            'lecturer_end_date' => $this->faker->dateTimeBetween('+5 month', '+6 month'),
        ];
    }
}
