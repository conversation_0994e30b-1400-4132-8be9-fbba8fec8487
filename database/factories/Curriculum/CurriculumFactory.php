<?php

namespace Database\Factories\Curriculum;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Curriculum\Curriculum>
 */
class CurriculumFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'start_date' => $this->faker->dateTimeBetween('now', '+1 week'),
            'end_date' => $this->faker->dateTimeBetween('+4 month', '+6 month'),
            'allowed_amount_of_students' => $this->faker->numberBetween(10, 100),
            'minimum_amount_of_students' => $this->faker->numberBetween(10, 100),
            'registration_start_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'registration_end_date' => $this->faker->dateTimeBetween('now', '+1 year'),
            'flow_id' => 1,
        ];
    }
}
