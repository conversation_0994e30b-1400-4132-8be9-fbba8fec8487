<?php

namespace Database\Factories\Reestry;

use App\Models\Reestry\Program\Program;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class LearnYearFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'program_id' => Program::factory()->create(),
            'name' => $this->faker->firstName,
            'price' => random_int(2250,4000),
            'hash' => md5(random_int(1,3))
        ];
    }
}
