<?php

namespace Database\Factories\Reestry;

use App\Models\Reestry\Campus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\Auditorium>
 */
class AuditoriumFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'campus_id' => Campus::factory()->make(),
            'student_aid' => $this->faker->boolean,
            'quantity' => $this->faker->year,
            'projector' => $this->faker->boolean,
            'exam_audience' => $this->faker->boolean,
            'multimedia' => $this->faker->boolean,
            'cameras' => $this->faker->boolean,
            'computer_lab' => $this->faker->boolean,
        ];
    }
}
