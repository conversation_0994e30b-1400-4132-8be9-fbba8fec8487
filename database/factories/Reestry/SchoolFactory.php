<?php

namespace Database\Factories\Reestry;

use App\Models\Reestry\Campus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\School>
 */
class SchoolFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name_ka' => $this->faker->firstName,
            'name_en' => $this->faker->lastName,
            'campus_id' => Campus::factory()->create()
        ];
    }
}
