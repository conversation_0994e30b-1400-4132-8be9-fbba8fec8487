<?php

namespace Database\Factories\Reestry\Student;

use App\Models\Reestry\Program\Program;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\Student\StudentGroup>
 */
class StudentGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name_ka' => $this->faker->firstName,
            'name_en' => $this->faker->lastName,
            'program_id' => Program::factory()->create()
        ];
    }
}
