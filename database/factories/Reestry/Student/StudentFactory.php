<?php

namespace Database\Factories\Reestry\Student;

use App\Models\Reestry\Flow;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\StudentBasicsOfEnrollment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\Student\Student>
 */
class StudentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->firstName,
            'surname' => $this->faker->lastName,
            'personal_id' => $this->faker->phoneNumber,
            'personal_id_number' => $this->faker->phoneNumber,
            'sex' => $this->faker->boolean,
            'address' => $this->faker->address,
            'citizenship' => $this->faker->country,
            'phone' => $this->faker->phoneNumber,
            'birthday' =>  Carbon::createFromFormat(
                'd/m/Y',
                '10/02/2000'
            ),
            'enrollment_date' => Carbon::createFromFormat(
                'd/m/Y',
                '10/02/2000'
            ),
            'enrollment_order' => $this->faker->country,
            'email' => $this->faker->email,
            'mobility' => $this->faker->boolean,
            'photo' => $this->faker->firstName,
            'notes' => $this->faker->firstName,
            'cv_file_name' => $this->faker->firstName,
            'diploma_file_name' => $this->faker->firstName,
            'transcript_file_name' => $this->faker->firstName,
            'motivation_article_file_name' => $this->faker->firstName,
            'diploma_taken' => $this->faker->boolean,
            'diploma_taken_date' => Carbon::createFromFormat(
                'd/m/Y',
                '10/02/2000'
            ),
            'bio' => $this->faker->firstName,
            //'user_id' => 3,
            'user_id' => User::factory()->make(),
            'status_id' => StudentStatusList::factory()->create(),
            'basics_of_enrollement_id' => StudentBasicsOfEnrollment::factory()->create(),
            'school_id' => School::factory()->create(),
            'program_id' => Program::factory()->create(),
            'group_id' => StudentGroup::factory()->create(),
            'learn_year_id' => LearnYear::factory()->create()
        ];
    }
}
