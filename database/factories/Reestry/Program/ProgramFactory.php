<?php

namespace Database\Factories\Reestry\Program;

use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\School;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class ProgramFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name_ka' => $this->faker->firstName,
            'name_en' => $this->faker->lastName,
            'school_id' => School::factory()->create(),
            'academic_degree_id' => AcademicDegree::factory()->create()
        ];
    }
}
