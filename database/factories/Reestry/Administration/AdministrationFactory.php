<?php

namespace Database\Factories\Reestry\Administration;

use App\Models\Reestry\Administration\AdministrationPosition;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdministrationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'identity_number' => $this->faker->numberBetween(37001053121,38001349011),
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->email,
            'administration_position_id' => AdministrationPosition::factory()->make(),
            'photo' => Null,
            'cv' => $this->faker->name,
            'user_id' => 1,
        ];
    }
}
