<?php

namespace Database\Factories\Reestry\Lecturer;

use App\Models\Reestry\AcademicDegree;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\Lecturer\Lecturer>
 */
class LecturerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'identity_number' => $this->faker->randomDigit(),
            'card_number' => $this->faker->streetAddress,
            'address' => $this->faker->address,
            'phone' => $this->faker->phoneNumber,
            'date_of_birth' => Carbon::createFromFormat(
                'd/m/Y',
                '10/07/2000'
            ),
            'email' => $this->faker->email,
            'photo' => $this->faker->randomLetter,
            'academic_degree_id' => AcademicDegree::factory(),
            'affiliated' => $this->faker->boolean,
            'cv' => $this->faker->randomLetter,
            'do_lectures_another_university' => $this->faker->boolean,
            //'user_id' => 2,
            'user_id' => User::factory()->make(),
        ];
    }
}
