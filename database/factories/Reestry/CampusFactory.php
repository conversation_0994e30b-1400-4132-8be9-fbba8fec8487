<?php

namespace Database\Factories\Reestry;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reestry\Campus>
 */
class CampusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name_en' => $this->faker->name,
            'name_ka' => $this->faker->name,
            'address_ka' => $this->faker->name,
            'address_en' => $this->faker->name,
        ];
    }
}
