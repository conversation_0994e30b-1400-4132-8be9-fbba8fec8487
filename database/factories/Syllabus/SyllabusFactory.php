<?php

namespace Database\Factories\Syllabus;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Syllabus\Syllabus>
 */
class SyllabusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->sentence(),
            'code' => Str::random(8),
            'credits' => 6,
            'contact_hours' => 40,
            'lecture_hours' => 15,
            'seminar_hours' => 15,
            'mid_and_final_exam_hours' => 10,
            'independent_work_hours' => 20,
            'total_hours' => 100,
            'goal' => $this->faker->paragraph(),
            'assessing_system' => $this->faker->paragraph(),
            'final_exam_prerequisite' => $this->faker->paragraph(),
//            'assessing_components' => $this->faker->paragraph(),
            'academic_honesty' => $this->faker->paragraph(),
            'additional_information' => $this->faker->paragraph(),
            'main_literature' => $this->faker->paragraph(),
            'additional_literature' => $this->faker->paragraph(),
        ];
    }
}
