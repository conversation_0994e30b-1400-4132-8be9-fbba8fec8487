<?php

namespace Database\Factories\RegisterForm;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RegisterForms\BachelorRegister>
 */
class BachelorRegisterFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'parent_phone' => $this->faker->phoneNumber,
            'photo' => $this->faker->streetAddress,
            'identity_number_copy' => $this->faker->streetAddress,
            'school' => $this->faker->safari,
            'degree' => $this->faker->boolean,
            'military_accounting' => $this->faker->boolean
        ];
    }
}
