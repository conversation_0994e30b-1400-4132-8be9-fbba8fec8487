<?php

namespace App\Exports\Hr;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AdministrationExport implements FromCollection, WithHeadings
{
    public function __construct(public $administrations)
    {
        //
    }

    /**
    * @return void
     */
    public function collection()
    {
        return $this->administrations->map(function ($item){
            $gender = null;
            if ($item?->hrAdministrationInfo?->gender == 0)
            {
                $gender = 'ქალი';
            }elseif ($item?->hrAdministrationInfo?->gender == 1)
            {
                $gender = 'კაცი';
            }

            $familyState = null;
            if ($item?->hrAdministrationInfo?->family_state == 0)
            {
                $familyState = 'დასაოჯახებელი';
            }elseif ($item?->hrAdministrationInfo?->family_state == 1)
            {
                $familyState = 'დაოჯახებული';
            }

            $qualifications = $item?->hrAdministrationInfo?->hrAdministrationEducations->pluck('qualification')->toArray();
            $qualificationCountries = $item?->hrAdministrationInfo?->hrAdministrationEducations->whereNotNull('country')->pluck('country')->toArray();
            $academicDegrees = $item?->hrAdministrationInfo
                ?->hrAdministrationEducations
                ?->load('academicDegree')
                ->pluck('academicDegree.name_ka')
                ->unique()
                ->toArray()
            ;

            return [
                $item->id,
                $item->last_name,
                $item->first_name,
                $item->identity_number,
                $item->phone,
                $item->email,
                $item?->administrationPosition?->name_ka,
                $item?->administrationItem?->name_ka,
                $item?->school?->name_ka,
                $item->photo ? 'კი' : 'არა',
                $item->cv ? 'კი' : 'არა',
                // info
                $item?->hrAdministrationInfo?->father_name,
                $gender,
                $item?->hrAdministrationInfo?->age,
                $familyState,
                $item?->hrAdministrationInfo?->date_of_birth,
                $item?->hrAdministrationInfo?->address,
                // education
                $qualifications ? implode(',', $qualifications) : null,
                $qualificationCountries ? implode(',', $qualificationCountries) : null,
                $academicDegrees ? implode(',', $academicDegrees) : null,
                // position
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->appointment ? 'კონკურსი':'რეკომენდაცია',
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->vacancy_command_number,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->vacancy_command_number_file ? 'კი':'არა',
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->vacancy_command_number_date,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->appointment_command_number,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->appointment_command_number_file ? 'კი':'არა',
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->appointment_command_number_date,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->position,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->type_of_position ? 'დამხმარე' : 'ადმინისტრაციული',
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->contract_start,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->contract_end,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->contract_period,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->vacation,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->day_off,
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->status ? 'კი':'არა',
                $item?->hrAdministrationInfo?->hrAdministrationAppointment?->educational_staff,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Id',
            'გვარი',
            'სახელი',
            'პირადი ნომერი',
            'ტელეფონი',
            'იმეილი',
            'პოზიცია',
            'ადმინისტრაციული ერთეული',
            'სკოლა',
            'ფოტო',
            'რეზიუმე',
            // info relation
            'მამის სახელი',
            'სქესი',
            'ასაკი',
            'ოჯახური მდგომარეობა',
            'დაბადების თარიღი',
            'მისამართი',
            // education
            'კვალიფიკაცია',
            'კვალიფიკაციის ქვეყანა',
            'კვალიფიკაციის აკადემიური მოსწრება',
            // appointment relation
            'დანიშვნა',
            'ვაკანსიის დანიშვნის ნომერი',
            'ვაკანსიის დანიშვნის ფაილი', // კი/არა
            'ვაკანსიის დანიშვნის დრო',
            'დანიშვნის ნომერი',
            'დანიშვნის ფაილი', // კი/არა
            'დანიშვნის დრო',
            'პოზიცია', // პროფესორი
            'პოზიციის ტიპი',
            'კონტრაქტის დაწყება',
            'კონტრაქტის დასრულება',
            'კონტრაქტის პერიოდი',
            'შვებულება', // კი არა
            'დასვენების დღე',
            'აფილირებული', // კი ან არა
            'საგანმანათლებლო პერსონალი', // ki ara
        ];

    }

}
