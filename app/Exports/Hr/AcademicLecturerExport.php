<?php

namespace App\Exports\Hr;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AcademicLecturerExport implements FromCollection, WithHeadings
{
    public function __construct(public $lecturers)
    {
        //
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->lecturers->map(function ($lecturer){
            $gender = null;
            if ($lecturer?->hrAcademicLectureInfo?->gender == 0)
            {
                $gender = 'ქალი';
            }elseif ($lecturer?->hrAcademicLectureInfo?->gender == 1)
            {
                $gender = 'კაცი';
            }
            $familyState = null;
            if ($lecturer?->hrAcademicLectureInfo?->family_state == 0)
            {
                $familyState = 'დასაოჯახებელი';
            }elseif ($lecturer?->hrAcademicLectureInfo?->family_state == 1)
            {
                $familyState = 'დაოჯახებული';
            }
            $qualifications = $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureEducations->pluck('qualification')->toArray();
            $qualificationCountries = $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureEducations->whereNotNull('country')->pluck('country')->toArray();
            $academicDegrees = $lecturer?->hrAcademicLectureInfo
                ?->hrAcademicLectureEducations
                ->load('academicDegree')
                ->pluck('academicDegree.name_ka')
                ->unique()
                ->toArray()
            ;

            return [
                $lecturer->id,
                $lecturer->last_name,
                $lecturer->first_name,
                $lecturer->identity_number,
                $lecturer->card_number,
                $lecturer->address,
                $lecturer->phone,
                $lecturer->date_of_birth,
                $lecturer->email,
                $lecturer->photo ? 'კი' : 'არა',
                $lecturer?->academicDegree?->name_ka,
                $lecturer->affiliated ? 'მოწვეული' : 'აკადემიური',
                $lecturer->cv ? 'კი' : 'არა',
                $lecturer->do_lectures_another_university ? 'კი' : 'არა',

                $lecturer?->hrAcademicLectureInfo?->father_name,
                $gender,
                $lecturer?->hrAcademicLectureInfo?->age,
                $familyState,

                $qualifications ? implode(',', $qualifications) : null,
                $qualificationCountries ? implode(',', $qualificationCountries) : null,
                $academicDegrees ? implode(',', $academicDegrees) : null,

                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->lecturerPosition?->title,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->grant ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->affiliated ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->lecturerCategory?->title,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->salary,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->paid_hours,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->unpaid_hours,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->direction,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->school->name_ka,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->appointment,

                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->vacancy_command_number,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->vacancy_command_number_file ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->vacancy_command_number_date,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->appointment_command_number,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->appointment_command_number_file ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->appointment_command_number_date,

                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->contract_start,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->contract_end,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->contract_period,
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLecturePosition?->status ? 'კი' : 'არა',

                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->scopus_g ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->scopus_h ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->web_of_science_g ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->web_of_science_h ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->google_scholar_g ? 'კი' : 'არა',
                $lecturer?->hrAcademicLectureInfo?->hrAcademicLectureAdditional?->google_scholar_h ? 'კი' : 'არა',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Id',
            'გვარი',
            'სახელი',
            'პირადი ნომერი',
            'პირადობის ნომერი',
            'მისამართი',
            'ტელეფონი',
            'დაბადების თარიღი',
            'იმეილი',
            'ფოტო',
            'აკადემიური მოსწრება',
            'ტიპი', // მოწვეული აკადემიური
            'რეზიუმე',
            'სხვა უნივერსიტეტში ატარებს ლექციას?',
            // info relation
            'მამის სახელი',
            'სქესი',
            'ასაკი',
            'ოჯახური მდგომარეობა',
            // education
            'კვალიფიკაცია',
            'კვალიფიკაციის ქვეყანა',
            'კვალიფიკაციის აკადემიური მოსწრება',

            // position relation
            'პოზიცია', // პროფესორი
            'გრანტი', // კი ან არა
            'აფილირებული', // კი ან არა
            'ლექტორის კატეგორია', // ა,ბ,ც
            'ხელფასი',
            'ანაზღაურებული საათები',
            'აუნაზღაურებელი საათები',
            'მიმართულება',
            'სკოლა',
            'დანიშვნა',

            'ვაკანსიის დანიშვნის ნომერი',
            'ვაკანსიის დანიშვნის ფაილი', // კი/არა
            'ვაკანსიის დანიშვნის დრო',

            'დანიშვნის ნომერი',
            'დანიშვნის ფაილი', // კი/არა
            'დანიშვნის დრო',

            'კონტრაქტის დაწყება',
            'კონტრაქტის დასრულება',
            'კონტრაქტის პერიოდი',
            'მინიჭება', // კი არა

            // hrAcademicLectureAdditional
            'Scopus-G',
            'Scopus-H',
            'Web of science-G',
            'Web of science-H',
            'Google scholar-G',
            'Google scholar-H',
        ];
    }
}
