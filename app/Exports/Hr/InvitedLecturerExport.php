<?php

namespace App\Exports\Hr;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class InvitedLecturerExport implements FromCollection, WithHeadings
{
    public function __construct(public $lecturers)
    {
        //
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->lecturers->map(function ($lecturer){
            $gender = null;
            if ($lecturer?->hrInvitedLectureInfo?->gender == 0)
            {
                $gender = 'ქალი';
            }elseif ($lecturer?->hrInvitedLectureInfo?->gender == 1)
            {
                $gender = 'კაცი';
            }
            $familyState = null;
            if ($lecturer?->hrInvitedLectureInfo?->family_state == 0)
            {
                $familyState = 'დასაოჯახებელი';
            }elseif ($lecturer?->hrInvitedLectureInfo?->family_state == 1)
            {
                $familyState = 'დაოჯახებული';
            }
            $qualifications = $lecturer?->hrInvitedLectureInfo?->hrInvitedLectureEducations->pluck('qualification')->toArray();
            $qualificationCountries = $lecturer?->hrInvitedLectureInfo?->hrInvitedLectureEducations->whereNotNull('country')->pluck('country')->toArray();
            $academicDegrees = $lecturer?->hrInvitedLectureInfo
                ?->hrInvitedLectureEducations
                ?->load('academicDegree')
                ->pluck('academicDegree.name_ka')
                ->unique()
                ->toArray()
            ;

            return [
                $lecturer->id,
                $lecturer->last_name,
                $lecturer->first_name,
                $lecturer->identity_number,
                $lecturer->card_number,
                $lecturer->address,
                $lecturer->phone,
                $lecturer->date_of_birth,
                $lecturer->email,
                $lecturer->photo ? 'კი' : 'არა',
                $lecturer?->academicDegree?->name_ka,
                $lecturer->affiliated ? 'მოწვეული' : 'აკადემიური',
                $lecturer->cv ? 'კი' : 'არა',
                $lecturer->do_lectures_another_university ? 'კი' : 'არა',
                // info
                $lecturer?->hrInvitedLectureInfo?->father_name,
                $gender,
                $lecturer?->hrInvitedLectureInfo?->age,
                $familyState,
                // education
                $qualifications ? implode(',', $qualifications) : null,
                $qualificationCountries ? implode(',', $qualificationCountries) : null,
                $academicDegrees ? implode(',', $academicDegrees) : null,
                // position
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->salary,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->direction,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->school?->name_ka,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->program?->name_ka,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->course,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->workType?->title,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->workplace_name,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->position,
                $lecturer?->hrInvitedLectureInfo?->hrInvitedLecturePosition?->status ? 'კი' : 'არა',

            ];
        });
    }

    public function headings(): array
    {
        return [
            'Id',
            'გვარი',
            'სახელი',
            'პირადი ნომერი',
            'პირადობის ნომერი',
            'მისამართი',
            'ტელეფონი',
            'დაბადების თარიღი',
            'იმეილი',
            'ფოტო',
            'აკადემიური მოსწრება',
            'ტიპი', // მოწვეული აკადემიური
            'რეზიუმე',
            'სხვა უნივერსიტეტში ატარებს ლექციას?',
            // info relation
            'მამის სახელი',
            'სქესი',
            'ასაკი',
            'ოჯახური მდგომარეობა',
            // education
            'კვალიფიკაცია',
            'კვალიფიკაციის ქვეყანა',
            'კვალიფიკაციის აკადემიური მოსწრება',
            // position relation
            'ხელფასი',
            'მიმართულება',
            'სკოლა',
            'პროგრამა',
            'კურსი',
            'სამუშაოს ტიპი',
            'სამუშაოს დასახელება',
            'პოზიცია',
            'სტატუსი',
        ];
    }
}
