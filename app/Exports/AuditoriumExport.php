<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AuditoriumExport implements FromCollection, WithHeadings, WithMapping
{
    const customColumns = ['campus_id','student_aid','projector','multimedia',
        'exam_audience','cameras','computer_lab'];
    public $columns, $auditoriums;

    public function __construct($columns, $auditoriums)
    {
        $this->columns = $columns;
        $this->auditoriums = $auditoriums;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->auditoriums;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($auditoriums): array
    {
        return collect($this->columns)->map(function ($column) use ($auditoriums) {
            if (in_array($column, self::customColumns)) {
                return match ($column) {
                    'campus_id' => $auditoriums->campus->name_ka,
                    'student_aid' => $auditoriums->student_aid ? 'კი' : 'არა',
                    'projector' => $auditoriums->projector ? 'კი' : 'არა',
                    'multimedia' => $auditoriums->multimedia ? 'კი' : 'არა',
                    'exam_audience' => $auditoriums->exam_audience ? 'კი' : 'არა',
                    'cameras' => $auditoriums->cameras ? 'კი' : 'არა',
                    'computer_lab' => $auditoriums->computer_lab ? 'კი' : 'არა'
                };
            }
            return $auditoriums->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name' => 'სახელი',
            'campus_id' => 'კამპუსი',
            'student_aid' => 'დაიშვება შშმ პირი',
            'quantity' => 'სტუდენტების რაოდენობა',
            'projector' => 'პროექტორი',
            'multimedia' => 'მულტიმედია',
            'exam_audience' => 'საგამოცდო აუდიტორია',
            'cameras' => 'კამერა',
            'computer_lab' => 'კომპიუტერის ლაბორატორია'
        };
    }
}
