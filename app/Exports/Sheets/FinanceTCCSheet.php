<?php

namespace App\Exports\Sheets;

use App\Models\TempPersonalId;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class FinanceTCCSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data->map(function ($data, $key){
            return [
                'N' => $key + 1,
                'გვარი' => $data->max_student_surname ?? '0',
                'სახელი' => $data->student_name ?? '0',
                'პირადი N' => $data->piradi_nom,
                'სკოლა' => $data->historical_school_name ?? '0',
                'პროგრამა' => $data->historical_program_name ?? '0',
                'სტატუსი' => $data->student_status_name ?? '0',
                'კონტრაქტის თანხა' => number_format($data->current_kontraqtis_tanxa, 2, '.', ''),
                'დამატებითი საგნების საფასური' => number_format($data->total_dam_sagnebi, 2, '.', ''),
                'სარეიტინგო ფასდაკლება (მ.შ გრანტიანის ფასდაკლება)' => number_format($data->total_sareitingo_fasdakleba + $data->total_grantianis_fasdakleba, 2, '.', ''),
                'ექსტრა' => number_format($data->total_extra, 2, '.', ''),
                'აკადემიური თანხა' => number_format($data->total_akademiuris_tanxa1, 2, '.', ''),
                'ძველი დავალიანება' => number_format($data->last_kvartlis_nashti, 2, '.', ''),
                'სტუდენტი' => number_format($data->student_total_price, 2, '.', ''),
                'გრანტი' => number_format($data->total_sax_granti, 2, '.', ''),
                'სოც.დახმარება' => number_format($data->total_sax_daxmareba, 2, '.', ''),
                'სხვა.დახმარება' => number_format($data->total_meriis_daxmareba, 2, '.', ''),
                'მიღებული' => number_format($data->total_charicxuli_studenti, 2, '.', ''),
                'ჯამური დავალიანება' => number_format($data->quarter_debt_total ?? $data->last_kvartlis_jami, 2, '.', ''),
                'ORIS' => number_format($data->last_kvartlis_jami ?? 0, 2, '.', ''),
                'ინდ.გრაფიკი' => $this->formatQuarterDebt($data),
                'შენიშვნა' => TempPersonalId::query()->where('personal_id', $data->piradi_nom)->exists() ? 'ელოდება დაფინანსებას' : '',
            ];
        });
    }

    public function formatQuarterDebt($data)
    {
        // Get the historical student record for this specific program
        $historicalStudent = \App\Models\Reestry\Student\Student::where('personal_id', $data->piradi_nom)
            ->where('program_id', $data->ProgramID)
            ->with('financeScheduler.calendars')
            ->first();

        $financeScheduler = $historicalStudent?->financeScheduler;

        $quarterDept = $financeScheduler?->calendars->map(function ($item, $key) {
            $formattedDate = Carbon::createFromFormat('d/m/Y', $item->start_date)->format('d.m.Y');
            $formattedPrice = number_format($item->amount, 2);

            return sprintf('%d) %s - %s', $key + 1, $formattedDate, $formattedPrice);
        });

        return $quarterDept?->implode(', ');
    }

    public function headings(): array
    {
        if ($this->data->isEmpty()) {
            return [
                'N', 'გვარი', 'სახელი', 'პირადი N', 'სკოლა', 'პროგრამა', 'სტატუსი',
                'კონტრაქტის თანხა', 'დამატებითი საგნების საფასური',
                'სარეიტინგო ფასდაკლება (მ.შ გრანტიანის ფასდაკლება)',
                'ექსტრა', 'აკადემიური თანხა', 'ძველი დავალიანება', 'სტუდენტი',
                'გრანტი', 'სოც.დახმარება', 'სხვა.დახმარება', 'მიღებული',
                'ჯამური დავალიანება', 'ORIS', 'ინდ.გრაფიკი', 'შენიშვნა'
            ];
        }
        return array_keys($this->data->first());
    }

    public function collection()
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'TCC';
    }
}
