<?php

namespace App\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class BachelorEmailsExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function __construct(public $data)
    {
    }

    public function collection()
    {
        return $this->data->map(function ($data, $index) {
            return [
                $index + 1,
                $data->last_name_en = strtolower($data->last_name_en),
                $data->first_name_en = strtolower($data->first_name_en),
                $data->registerFormInfo?->email,
                $data->program?->name_ka,
                Carbon::make($data->created_at)->format('d-m-Y'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'გვარი',
            'სახელი',
            'ელ-ფოსტა',
            'პროგრამა',
            'რეგისტრაციის თარიღი',
        ];
    }
}
