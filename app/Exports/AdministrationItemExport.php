<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AdministrationItemExport implements FromCollection, WithMapping, WithHeadings
{
    public $columns, $administrationItem;

    public function __construct($columns, $administrationItem)
    {
        $this->columns = $columns;
        $this->administrationItem = $administrationItem;
    }

    public function collection()
    {
        return $this->administrationItem;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($administrationItem): array
    {
        return collect($this->columns)->map(function ($column) use ($administrationItem) {
            return $administrationItem->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
        };
    }
}
