<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MinorExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function __construct(public $minors){}

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->minors->map(function($minor){

            $minorId = $minor->minor_id;
            if($minorId == 1)
            {
                $minorName = 'ფსიქოლოგია';
            }elseif($minorId == 2)
            {
                $minorName = 'საერთაშორისო ურთიერთობები';
            }
            elseif($minorId == 3)
            {
                $minorName = 'საჯარო მმართველობა';
            }

            return [
                $minor->student->name . ' ' . $minor->student->surname,
                $minor->student->personal_id,
                $minorName ?? '',
                $minor?->flow?->name ?? '',
                $minor->created_at
            ];
        });
    }

    public function headings(): array
    {
        return [
            'სახელი გვარი',
            'პირადი ნომერი',
            'მაინორის დასახელება',
            'სასწავლო სემესტრი',
            'თარიღი',
        ];
    }
}
