<?php

namespace App\Exports;

use App\Models\Reestry\Student\Student;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProgramWithGpaExport implements FromCollection, ShouldAutoSize, WithHeadings
{
    public function collection()
    {
        $studentIds = [1024079117, 1805049759, 1019076921, 1011089061, 1017056506, 19101115266, 31601058530, 57701064952, 61901091549, 60001145922, 1808067190, 1024076488, 1008056364, 62001041060, 1108065208, 9501029735, 24401050839, 1019074154, 1808067079, 54501062062, 1611107467, 1027069991, 1108067264, 1019081489, 1005022144, 1708067187, 56401027124, 1308067818, 1008055542, 1024082188, 1601108299, 1017050834, 62902016365, 39001032535, 25001048605, 1001077722, 1211108846, 1908066284, 20001065626, 1819091957, 1801107766, 31501059315, 1008062519, 61304075495, 61501093186, 1701108812, 1001066902, 19801115542, 45301038284, 61001088073, 1605047789, 1356007198, 62001042024, 1008056616, 1801110586, 1105045983, 1027086636, 1617061928, 1411100908, 62001045122, 1124096548, 62602009687, 1005038185, 19601114139, 1417062082, 61001064777, 59001126282, 1624092278, 1008051718, 29301041924, 1105047528, 1805050334, 19201115388, 1024088357, 1008052745, 1008052947, 1811108300, 1901103547, 1008053073, 1908067852, 1024075678, 1008060692, 1005036552, 1008052394, 1617061258, 1005032115, 1201108761, 1024057026, 1001079215, 1724093428, 19101115986, 1024089810, 1424096865, 1805048422, 19901114816, 1008043446, 1424096345, 1005029148, 1817061373, 1917061065, 62003016454];
        return Student::query()
            ->with('program')
            ->whereIn('personal_id', $studentIds)
            ->get()
            ->map(function ($student) {
                return [
                    trim((string) $student->personal_id),
                    (string) $student->name,
                    (string) $student->surname,
                    (string) $student?->program?->name_ka,
                    (string) $student->gpa,
                ];
            })
        ;
    }

    public function headings(): array
    {
        return [
            'პირადი ნომ.',
            'სახელი',
            'გვარი',
            'პროგრამა',
            'GPA',
        ];
    }
}
