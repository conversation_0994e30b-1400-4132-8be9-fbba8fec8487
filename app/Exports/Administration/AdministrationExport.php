<?php

namespace App\Exports\Administration;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use function collect;

class AdministrationExport implements FromCollection, WithMapping, WithHeadings
{
    const relatedColumns = ['administration_position_id','administration_item_id','school_id'];
    public $columns, $administrations;

    public function __construct($columns, $administrations)
    {
        $this->columns = $columns;
        $this->administrations = $administrations;
    }

    public function collection()
    {
        return $this->administrations;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($administrations): array
    {
        return collect($this->columns)->map(function ($column) use ($administrations) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'administration_position_id' => $administrations->administrationPosition?->name_ka,
                    'administration_item_id' => $administrations->administrationPosition->name_ka ?? 'არ აქვს',
                    'school_id' => $administrations->school->name_ka ?? 'არ აქვს',
                };
            }
            return $administrations->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'first_name' => 'სახელი',
            'last_name' => 'გვარი',
            'identity_number' => 'პირადობის ნომერი',
            'phone' => 'ტელეფონის ნომერი',
            'email' => 'Email',
            'administration_position_id' => 'ადმინისტრაციული პოზიცია',
            'administration_item_id' => 'ადმინისტრაციული ერთეული',
            'school_id' => 'სკოლა',
            'photo' => 'ფოტო',
            'cv' => 'CV'
        };
    }
}
