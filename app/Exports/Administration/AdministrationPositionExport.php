<?php

namespace App\Exports\Administration;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use function collect;

class AdministrationPositionExport implements FromCollection, WithHeadings, WithMapping
{
    public $columns, $administrationPositions;

    public function __construct($columns, $administrationPositions)
    {
        $this->columns = $columns;
        $this->administrationPositions = $administrationPositions;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->administrationPositions;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($administrationPositions): array
    {
        return collect($this->columns)->map(function ($column) use ($administrationPositions) {
            return $administrationPositions->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)'
        };
    }
}
