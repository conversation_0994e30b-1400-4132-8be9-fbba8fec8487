<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ProgramExport implements FromCollection, WithMapping, WithHeadings
{
    const relatedColumns = ['academic_degree_id','school_id'];
    public $columns, $programs;

    public function __construct($columns, $programs)
    {
        $this->columns = $columns;
        $this->programs = $programs;
    }

    public function collection()
    {
        return $this->programs;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($programs): array
    {
        return collect($this->columns)->map(function ($column) use ($programs) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'academic_degree_id' => $programs->academicDegree->name_ka,
                    'school_id' => $programs->school->name_ka,
                };
            }
            return $programs->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
            'academic_degree_id' => 'აკადემიური ხარისხი',
            'school_id' => 'სკოლა',
        };
    }
}
