<?php

namespace App\Exports;

use App\Models\Lectures\Lecture;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CompareSyllabiHoursExport implements FromCollection, WithHeadings
{
    public $data;

    public function __construct()
    {
        $this->data = Lecture::query()
            ->whereDate('lecture_date', '>=', now()->startOfYear()->month(9)->day(1))
            ->whereDate('lecture_date', '<=', now())
            ->with(['syllabus','lecturer'])
            ->orderByDesc('id')
            ->get()
            ->groupBy('syllabus_id')
            ->map(function ($record){
                return $record->groupBy('lecturer_id')->map(function ($record){
                    return $record->groupBy('is_lecture')->map(function ($record){
                        $hours = $record->first()->is_lecture
                            ? ($record->first()->syllabus->lecture_hours??null)
                            : ($record->first()->syllabus->seminar_hours??null)
                        ;

                        $workedHours = $record->reduce(function ($carry, $lecture) {
                            $start = Carbon::createFromFormat('H:i:s', $lecture->start_time);
                            $end = Carbon::createFromFormat('H:i:s', $lecture->end_time);
                            $diffInSeconds = $end->diffInHours($start);

                            return $carry + $diffInSeconds;
                        }, 0);

                        $timeDifference = $workedHours - $hours;
                        if ($timeDifference != 0)
                        {
                            return [
                                'საგნის კოდი' => $record->first()->syllabus->code ?? '',
                                'დასახელება' => $record->first()->syllabus->name ?? '',
                                'ლექტორი' => ($record->first()->lecturer->first_name ?? '') . ' '. ($record->first()->lecturer->last_name ?? ''),
                                'ტიპი' => $record->first()->is_lecture ? 'ლექცია' : 'სემინარი',
                                'ცხრილის საათები' => ($timeDifference < 0 ? '<' : '>') .' '.abs($timeDifference),
                                'სკოლა' => $record->first()->syllabus->learnYear->program->name_ka ?? '',
                                'პროგრამა' => $record->first()->syllabus->learnYear->program->school->name_ka ?? '',
                                'საათები' => $hours

                            ];
                        }
                        return null;
                    });
                })
                ->flatten(1)
            ;
            })
            ->values()
            ->flatten(1)
            ->filter(function($value) {
                return !is_null($value);
            })
        ;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->data;
    }

    public function headings(): array
    {
        return array_keys((array)$this->data->first());
    }
}
