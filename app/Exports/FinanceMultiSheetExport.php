<?php

namespace App\Exports;

use App\Exports\Sheets\FinanceBaMaPhDSheet;
use App\Exports\Sheets\FinanceHSSheet;
use App\Exports\Sheets\FinanceTCCSheet;
use App\Services\FinanceService;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class FinanceMultiSheetExport implements WithMultipleSheets
{
    use Exportable;

    protected $filter;
    protected $financeService;

    public function __construct($filter)
    {
        $this->filter = $filter;
        $this->financeService = new FinanceService();
        $this->financeService->setAdditionalData();
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        // Sheet 1: BA-MA-PHD (academic_degree_id in [1,2,3] AND IsTrainings = 0)
        $baMaPhDData = $this->financeService->getFilteredFinanceData($this->filter, [1, 2, 3], 0, true);
        $sheets[] = new FinanceBaMaPhDSheet($baMaPhDData);

        // Sheet 2: HS (academic_degree_id = 4 AND IsTrainings = 0)
        $hsData = $this->financeService->getFilteredFinanceData($this->filter, [4], 0, true);
        $sheets[] = new FinanceHSSheet($hsData);

        // Sheet 3: TCC (academic_degree_id = 5 AND IsTrainings = 1)
        $tccData = $this->financeService->getFilteredFinanceData($this->filter, [5], 1, true);
        $sheets[] = new FinanceTCCSheet($tccData);

        return $sheets;
    }
}
