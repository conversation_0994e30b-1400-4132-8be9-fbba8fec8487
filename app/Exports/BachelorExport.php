<?php

namespace App\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;

class BachelorExport implements FromCollection, WithHeadings, WithMapping
{
    const relatedColumns = ['program_id',
        'registerFormInfo.first_name',
        'registerFormInfo.last_name',
        'registerFormInfo.identity_number',
        'registerFormInfo.phone',
        'registerFormInfo.email',
        'registerFormInfo.gender',
        'registerFormInfo.date_of_birth'];
    public $columns, $students;

    public function __construct($students)
    {
        $this->columns = [
            'registerFormInfo.first_name',
            'first_name_en',
            'registerFormInfo.last_name',
            'last_name_en',
            'registerFormInfo.identity_number',
            'registerFormInfo.phone',
            'registerFormInfo.email',
            'registerFormInfo.gender',
            'registerFormInfo.date_of_birth',
            'address',
            'parent_phone',
            'photo',
            'identity_number_copy',
            'school',
            'program_id',
            'flow_id',
            'english_level_id',
            'school_document',
            'military_accounting',
            'payment_document',
        ];
        $this->students = $students;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($students): array
    {
        return collect($this->columns)->map(function ($column) use ($students) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'program_id' => $students->program->name_ka,
                    'registerFormInfo.first_name' => $students->registerFormInfo->first_name,
                    'registerFormInfo.last_name' => $students->registerFormInfo->last_name,
                    'registerFormInfo.identity_number' => $students->registerFormInfo->identity_number,
                    'registerFormInfo.phone' => $students->registerFormInfo->phone,
                    'registerFormInfo.email' => $students->registerFormInfo->email,
                    'registerFormInfo.gender' => $students->registerFormInfo->gender == 0 ? 'მდედრობითი' : 'მამრობითი',
                    'registerFormInfo.date_of_birth' => $students->registerFormInfo->date_of_birth,
                };
            }
            if ($column === 'registerFormInfo.gender') {
                return $students->{$column} == 0 ? 'მდედრობითი' : 'მამრობითი';
            }
            if ($column === 'registerFormInfo.date_of_birth') {
                $dateOfBirth = Carbon::createFromFormat('Y-m-d H:i:s', $students->{$column});
                return $dateOfBirth->format('d.m.Y');
            }
            if ($column === 'photo') {
                $photoUrl = 'https://api.portal.gipa.ge/storage/' . $students->{$column};
                return '=HYPERLINK("'.$photoUrl.'", "Click to view photo")';
            }
            if ($column === 'school_document') {
                $photoUrl = 'https://api.portal.gipa.ge/storage/' . $students->{$column};
                return '=HYPERLINK("'.$photoUrl.'", "Click to view school_document")';
            }
            if ($column === 'identity_number_copy') {
                $photoUrl = 'https://api.portal.gipa.ge/storage/' . $students->{$column};
                return '=HYPERLINK("'.$photoUrl.'", "Click to view school_document")';
            }
            if ($column === 'payment_document') {
                $photoUrl = 'https://api.portal.gipa.ge/storage/' . $students->{$column};
                return '=HYPERLINK("'.$photoUrl.'", "Click to view school_document")';
            }
            if ($column === 'military_accounting') {
                $photoUrl = 'https://api.portal.gipa.ge/storage/' . $students->{$column};
                return '=HYPERLINK("'.$photoUrl.'", "Click to view school_document")';
            }
            return $students->{$column};
        })->toArray();
    }

    private function getHeadingValue($column)
    {
        return match ($column) {
            'parent_phone' => 'მშობელის ტელეფონი',
            'photo' => 'ფოტო',
            'identity_number_copy' => 'პირადი ნომრის კოპია',
            'school' => 'სკოლა',
            'program_id' => 'პროგრამის ID',
            'flow_id' => 'სრული გადმოწერის ID',
            'english_level_id' => 'ინგლისური ენის დონის ID',
            'school_document' => 'სკოლის დოკუმენტი',
            'military_accounting' => 'სამხედრო რეესტრაცია',
            'payment_document' => 'გადახდის დოკუმენტი',
            'first_name_en' => 'სახელი ინგლისურად',
            'last_name_en' => 'გვარი ინგლისურად',
            'address' => 'მისამართი',
            'registerFormInfo.first_name' => 'სახელი',
            'registerFormInfo.last_name' => 'გვარი',
            'registerFormInfo.identity_number' => 'პირადი ნომერი',
            'registerFormInfo.phone' => 'ტელეფონი',
            'registerFormInfo.email' => 'ელ. ფოსტა',
            'registerFormInfo.gender' => 'სქესი',
            'registerFormInfo.date_of_birth' => 'დაბ. თარიღი',
        };
    }


    public function collection()
    {
        return $this->students;
    }
}
