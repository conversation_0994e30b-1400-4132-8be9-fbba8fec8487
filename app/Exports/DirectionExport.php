<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DirectionExport implements FromCollection, WithMapping, WithHeadings
{
    public $columns, $directions;

    public function __construct($columns, $directions)
    {
        $this->columns = $columns;
        $this->directions = $directions;
    }

    public function collection()
    {
        return $this->directions;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($directions): array
    {
        return collect($this->columns)->map(function ($column) use ($directions) {
            return $directions->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
        };
    }
}
