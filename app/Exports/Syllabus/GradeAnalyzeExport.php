<?php

namespace App\Exports\Syllabus;

use App\Models\Lectures\Lecture;
use App\Models\Lectures\StudentAttendance;
use App\Models\Syllabus\Syllabus;
use App\Models\User\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class GradeAnalyzeExport implements FromCollection, WithHeadings, WithStyles
{
    public function __construct($programId, $learnYearId, $flowId)
    {
        $this->syllabus = Syllabus::query()
            ->with(['learnYear', 'students'])
            ->when($programId, function ($query, $value){
                $query->whereHas('learnYear', function ($query) use ($value){
                    $query->where('program_id', $value);
                });
            })
            ->when($learnYearId, function ($query, $value){
                $query->where('learn_year_id', $value);
            })
            ->when($flowId, function ($query, $value){
                $query->whereHas('curriculum', function ($query) use ($value){
                    $query->where('flow_id', $value);
                });
            })
            ->get()
        ;

    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->syllabus->map(function ($item, $index){

            $totalHours = $item->lecture_hours + $item->seminar_hours + $item->mid_and_final_exam_hours;
            $lectureIds = Lecture::whereSyllabusId($item->id)
                ->pluck('id')
                ->toArray()
            ;

//            $missedLectures = StudentAttendance::whereIn('lecture_id', $lectureIds)
////                ->where('student_id', $studentId)
//                ->where('is_present', 0)
//                ->count()
//            ;

//            $result['missedLecturesInPercent'] = number_format(($missedLectures / $totalHours) * 100, 2);

            $students = StudentAttendance::query()
                ->whereIn('lecture_id', $lectureIds)
                ->where('is_present', 0)
                ->select(['student_id', DB::raw('COUNT(*) as missed_count')])
                ->groupBy('student_id')
                ->get()
                ->filter(function ($student) use ($totalHours) {
                    $missedPercent = ($student->missed_count / $totalHours) * 100;
                    return $missedPercent > 33.33;
                });


            $studentCount = $item->students->count();
            $firstCount = $item->students->where('point', '>=', 51)->count();
            $secondCount = $item->students->where('point', '>=', 91)->where('point', '<=', 100)->count();
            $thirdCount = $item->students->where('point', '>=', 81)->where('point', '<=', 90)->count();
            $fourthCount = $item->students->where('point', '>=', 71)->where('point', '<=', 80)->count();
            $fifthCount = $item->students->where('point', '>=', 61)->where('point', '<=', 70)->count();
            $sixCount = $item->students->where('point', '>=', 51)->where('point', '<=', 60)->count();
            $sevenCount = $item->students->where('point', '<=', 50)->count();

            return [
                (int) $index + 1,
                $item->name ?? '',
                $item->lecturers->map(fn($lecturer) => "{$lecturer->first_name} {$lecturer->last_name}")->implode(', '),
                $item->code ?? '',
                $studentCount,
                round($item->students->avg('point'), 2).'%',

                $firstCount ? round($firstCount, 2) : '0',
                $studentCount ? round($firstCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($secondCount, 2) : '0',
                $studentCount ? round($secondCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($thirdCount, 2) : '0',
                $studentCount ? round($thirdCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($fourthCount, 2) : '0',
                $studentCount ? round($fourthCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($fifthCount, 2) : '0',
                $studentCount ? round($fifthCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($sixCount, 2) : '0',
                $studentCount ? round($sixCount / $studentCount * 100, 0).'%' : '0%',

                $secondCount ? round($sevenCount, 2) : '0',
                $studentCount ? round($sevenCount / $studentCount * 100, 0).'%' : '0%',

                (string) $students->count()
            ];
        });
    }

    public function headings(): array
    {
        return [
            'N',
            'საგანი',
            'ლექტორი',
            'კოდი',
            'სულ სტუდენტი',
            'საშუალო',
            '>=51',
            '',
            '91-100',
            '',
            '81-90',
            '',
            '71-80',
            '',
            '61-70',
            '',
            '51-60',
            '',
            '0-50',
            '',
            'გაცდენა'
        ];
    }

    /**
     * @throws Exception
     */
    public function styles(Worksheet $sheet): void
    {
        $sheet->mergeCells('G1:H1')->getStyle('G1:H1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('I1:J1')->getStyle('I1:J1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('K1:L1')->getStyle('K1:L1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('M1:N1')->getStyle('M1:N1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('O1:P1')->getStyle('O1:P1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('Q1:R1')->getStyle('Q1:R1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->mergeCells('S1:T1')->getStyle('S1:T1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(15);

        $headerRow = $sheet->getStyle('1');
        $headerRow->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

//        $borderRange = 'A1:D1';
//        $sheet->getStyle($borderRange)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
//
//        // Set borders for multiple ranges (optional)
//        $dataRanges = ['F2:G10', 'H2:I10', 'J2:K10']; // example ranges
//        foreach ($dataRanges as $range) {
//            $sheet->getStyle($range)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
//        }
    }
}
