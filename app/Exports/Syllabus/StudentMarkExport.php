<?php

namespace App\Exports\Syllabus;

use App\Models\Assignment;
use App\Models\Curriculum\Curriculum;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class StudentMarkExport extends SyllabusService implements FromCollection, WithHeadings, WithMapping
{

    protected SyllabusService $syllabusService;
    private array $headings;

    private static int $tries = 0;

    private const allowedTemplateValues = ['Id', 'Student'];

    public $columns, $auditoriums;
    private Syllabus $syllabus;
    private bool $template;

    /**
     * @param Syllabus $syllabus
     * @param bool $withoutValue true when requested only headings.
     */

    public function __construct(Syllabus $syllabus, bool $template = false)
    {
        $this->columns = [];
        $this->syllabus = $syllabus;
        $this->syllabusService = new SyllabusService();
        $this->headings = [];
        $this->template = $template;
    }

    public function collection()
    {
        return $this->syllabus;
    }

    public function headings(): array
    {
        $assignments = $this->syllabusService->subjectHeadings($this->syllabus, listener: 'excel');
        $curriculumId = Curriculum::whereSyllabusId($this->syllabus->id)->first()?->id;
        $studentGroups = implode(',',$this->syllabusService->setStudentGroups($curriculumId, true));
        $syllabusRows = [
            "Syllabus Id : {$this->syllabus->id}",
            "Syllabus Name : {$this->syllabus->name}",
            "Semester : {$this->syllabus->semester->name}",
            "Student groups : $studentGroups",
            "Lecturers : {$this->syllabusService->setLecturers($this->syllabus->id)['lecturer_names']}",
            'studentName',
            'Email'
        ];

        $this->headings = $this->template
            ? ['Id', 'Student', ...$assignments] : [...$syllabusRows, ...$assignments];
        return collect($this->headings)->map(function ($column, $key) use ($syllabusRows) {
            if (
                ($key > 4 && is_array($column))
                ||
                ($this->template && !in_array($column, self::allowedTemplateValues))
            ) {
                return $this->getHeadingValue($column['id'], true);
            }
            if (in_array($column, self::allowedTemplateValues) && $this->template) {
                return $column;
            }
            return $syllabusRows[$key];
        })->toArray();
    }

    public function map($studentMarks): array
    {
        self::$tries++;
        if (self::$tries == 1) {
            $students = $this->syllabus->toArray();
            $result = [];
            foreach ($students['students'] as $key => $student) {
                $firstName = $student['student']['name'];
                $lastName = $student['student']['surname'];
                $id = $student['student_id'];
                $fullName = "$lastName $firstName - $id";
                $email = $student['student']['email'];
                $result = [...$result, collect($this->headings)
                    ->map(function ($column, $key) use ($id, $firstName, $lastName, $fullName, $email) {
                        if ($this->template) {
                            return match ($column) {
                                'Id' => $id,
                                'Student' => "$firstName $lastName",
                                default => ''
                            };
                        } else {
                            if (is_array($column)) {
                                return StudentAssignment::where('assignment_id', $column['id'])
                                    ->where('student_id', $id)->first()?->point ?? "0";
                            }
                            if ($column === 'studentName') {
                                return $fullName;
                            }
                            if ($column === 'Email') {
                                return $email;
                            }
                            return '';
                        }
                    })->toArray()];
            }
            $result = collect($result)->sortBy(function ($item) {
                return $item[5];
            });
            return $result->toArray();
        }
        return [];
    }

    public
    function getHeadingValue(string|int $column, bool $dynamic = false)
    {
        $assignment = Assignment::find($column)->load('assessmentComponent');
        $assignment = $assignment?->assessmentComponent->name_ka.' - '.$assignment?->id;

        return $dynamic ? $assignment : match ($column) {
                'subject' => 'საგანი',
                'syllabusId' => 'სილაბუსის აიდი',
                'course' => 'კურსი',
                'studentGroups' => 'სტუდენტური ჯგუფები',
                'lecturers' => 'ლექტორები',
                default => 'test'
            };
    }
}
