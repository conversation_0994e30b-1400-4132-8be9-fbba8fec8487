<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Border;

class SurveyExport implements FromCollection,WithEvents
{
    /**
     * @var array|string[]
     */
    public array $headers;
    public Collection $answers;

    public function __construct(public $survey, public $response)
    {
        $headers = ['N'];
        $headers[] = 'საგანი';
        $headers[] = 'პროგრამა';
        $headers[] = 'ლექტორი';
        $headers[] = 'სემესტრი';
        $answers = [];

        foreach ($survey->questions as $question)
        {
            $headers[] = $question->name;
            $headers[] = 'კომენტარი';

            foreach ($question->answer as $index => $answer)
            {
                $answers[] = [
                    $index+1,
                    $response['syllabusName'],
                    $response['program'],
                    $response['lecturerName'],
                    $response['learnYear'],

                    $question->survey_question_type_id == 0 ? $answer->answer_int : $answer->answer_string,
                    $question->comment,
                ];
            }
        }

        $this->headers = $headers;
        $this->answers = collect($answers);
    }


    public function collection()
    {
        return collect([[''],$this->headers])->merge($this->answers);
    }



    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->mergeCells('E1:F1')->setCellValue('E1', $this->survey->name.' - '.$this->survey->desctiorion);

                $event->sheet->getDelegate()->getStyle('E1:F1')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                        ],
                    ],
                ]);
            },
        ];
    }

}
