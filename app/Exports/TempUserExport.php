<?php

namespace App\Exports;

use App\Models\TempUser;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class TempUserExport implements FromCollection
{

    /**
    * @return Collection
    */
    public function collection()
    {
        return TempUser::query()
            ->with('student.program')
            ->whereHas('student', function ($student){
                return $student->where('status_id', '!=', 1);
            })
            ->get()
            ->map(function ($tempUser){
                return [
                    $tempUser->student->name,
                    $tempUser->student->surname,
                    $tempUser->personal_id,
                    $tempUser->student->program->name_ka,
                    $tempUser->student->status->name_ka,
                ];
            });
    }
}
