<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SchoolExport implements FromCollection, WithMapping, WithHeadings
{
    const relatedColumns = ['campus_id'];
    public $columns, $schools;

    public function __construct($columns, $schools)
    {
        $this->columns = $columns;
        $this->schools = $schools;
    }

    public function collection()
    {
        return $this->schools;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($schools): array
    {
        return collect($this->columns)->map(function ($column) use ($schools) {
            if(in_array($column,self::relatedColumns)){
                return match($column){
                    'campus_id' => $schools->campus->name_ka
                };
            }
            return $schools->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
            'campus_id' => 'კამპუსი'
        };
    }
}
