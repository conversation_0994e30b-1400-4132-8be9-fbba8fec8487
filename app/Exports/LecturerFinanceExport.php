<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class LecturerFinanceExport implements FromCollection, WithHeadings
{
    public $data;

    public function __construct(public $lectures)
    {
        $groupedLectures = $this->lectures->groupBy('lecturer_id');
        $this->data = [];
        $key=0;
        foreach ($groupedLectures as $lecturerId => $lectures)
        {
            $groupedSyllabus = $lectures->groupBy('syllabus_id');


            foreach ($groupedSyllabus as $syllabus)
            {

                $firstLecturer = $syllabus->first();
                $workedMinutes = 0;
                $earned = 0;
                foreach ($syllabus as $lecture)
                {
                    $start_datetime = Carbon::parse($lecture->start_time);
                    $end_datetime = Carbon::parse($lecture->end_time);
                    $workedMinute = $start_datetime->diffInMinutes($end_datetime);
                    $nashti = $workedMinute % 60;
                    $workedMinute -= $nashti;

                    if ($nashti >= 0 && $nashti <= 29) {
                        $workedMinute += 0; // 0 წუთი
                    } elseif ($nashti >= 30 && $nashti <= 44) {
                        $workedMinute += 30;
                    } elseif ($nashti >= 45) {
                        $workedMinute += 60;
                    }
                    $earned += $lecture->payment_per_hour * ($workedMinute/60);
                    $workedMinutes += $workedMinute;

                }

                // 1სთ - 45წუთი დან 1 წათი და 29 წუთი
                // 1საათი და 30წუთი - 1 საათ და 30 წუთ - 1 საათი და 45 წუთი
                // 2სთ - 1 საათი და 45 წუთი - 1 საათი და 30 წუთი
                $firstSyllabus = $syllabus->first();
                $position = $firstSyllabus?->syllabus?->name;
                $department = $firstSyllabus?->syllabus?->learnYear?->program?->school?->name_ka
                    . '/'
                    . $firstSyllabus?->syllabus?->learnYear?->program?->name_ka;

                Log::info('lecturer finance - syllabus_id:', ['syllabus_id' => $firstSyllabus?->syllabus_id]);

                $this->data[$key]['OMPEmployeeID'] = $firstLecturer?->curricula?->lecture?->times?->where('lecturer_id', $lecturerId)?->first()?->lecturer_accounting_code;
                $this->data[$key]['თანამშრომელი']  = $firstLecturer?->lecturer?->first_name . ' '. $firstLecturer?->lecturer?->last_name;
                $this->data[$key]['ჰონორარი'] = $workedMinutes > 0 ? round($earned / $workedMinutes, 2) : 0;
                $this->data[$key]['წაკითხული საათების რაოდენობა'] = round($workedMinutes / 60, 2); // convert to hours
                $this->data[$key]['ასანაზღაურებელი'] = round($earned, 2);
                $this->data[$key]['თანამდებობა'] = $position;
                $this->data[$key]['განყოფილება'] = $department;
                $key += 1;
            }
        }
        $this->data = collect($this->data)
            ->sortBy('განყოფილება')
//            ->groupBy(['OMPEmployeeID', 'თანამდებობა', 'განყოფილება'])
            ->groupBy('OMPEmployeeID')
            ->map(function ($OMPEmployeeIDs) {
                return $OMPEmployeeIDs->groupBy('განყოფილება')->map(function ($department) {
                    return $department->groupBy('თანამდებობა')->map(function ($positions) {

                        $workedMinutes = 0;
                        $earned = 0;
                        foreach ($positions as $position)
                        {
                            $workedMinutes += $position['წაკითხული საათების რაოდენობა'] * 60;
                            $earned += $position['ასანაზღაურებელი'];
                        }
                        $position['წაკითხული საათების რაოდენობა'] = $workedMinutes/60;
                        $position['ასანაზღაურებელი'] = $earned;
                        $position['ჰონორარი'] =  $workedMinutes > 0 ? round($earned / $workedMinutes, 2) : 0;

                        return $position;
                    });
                });
            })
            ->flatten(2)
        ;

    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->data;
    }

    public function headings(): array
    {
        if ($this->data->first())
        {
            return array_keys($this->data->first());
        }
        return [];
    }
}
