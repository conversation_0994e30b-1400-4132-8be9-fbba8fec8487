<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class FlowExport implements FromCollection, WithMapping, WithHeadings
{
    const relatedColumns = ['program_id'];
    public $columns, $flows;

    public function __construct($columns, $flows)
    {
        $this->columns = $columns;
        $this->flows = $flows;
    }

    public function collection()
    {
        return $this->flows;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($flows): array
    {
        return collect($this->columns)->map(function ($column) use ($flows) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'program_id' => $flows->program->name_ka
                };
            }
            return $flows->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name' => 'სახელი',
            'program_id' => 'პროგრამა'
        };
    }
}
