<?php

namespace App\Exports;

use App\Models\Syllabus\Syllabus;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SyllabusLiteratureExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Syllabus::query()
            ->where('lmb_id', null)
            ->where('is_external', 0)
            ->get()->map(function ($item, $index) {
            return [
                $index + 1,
                $item?->learnYear->program?->name_ka,
                $item->code,
                $item->name,
                html_entity_decode(strip_tags($item->main_literature)),
                html_entity_decode(strip_tags($item->additional_literature)),

            ];
        });
    }

    public function headings(): array
    {
        return [
            'N',
            'პროგრამა',
            'კოდი',
            'სახელი',
            'ძირითადი ლიტერატურა',
            'დამხმარე ლიტერატურა',
        ];
    }
}
