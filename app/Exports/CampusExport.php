<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CampusExport implements FromCollection, WithMapping, WithHeadings
{
    public $columns, $campuses;

    public function __construct($columns, $campuses)
    {
        $this->columns = $columns;
        $this->campuses = $campuses;
    }

    public function collection()
    {
        return $this->campuses;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($campuses): array
    {
        return collect($this->columns)->map(function ($column) use ($campuses) {
            return $campuses->{$column};
        })->toArray();
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
            'address_ka' => 'მისამართი (GEO)',
            'address_en' => 'მისამართი (ENG)',
        };
    }
}
