<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class LecturerExport implements FromCollection, WithHeadings, WithMapping
{
    const relatedColumns = ['academic_degree_id', 'do_lectures_another_universities'];
    public $columns, $lecturers;

    public function __construct($columns, $lecturers)
    {
        $this->columns = $columns;
        $this->lecturers = $lecturers;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($lecturers): array
    {
        return collect($this->columns)->map(function ($column) use ($lecturers) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'academic_degree_id' => $lecturers->academicDegree->name_ka,
                    'do_lectures_another_universities' => $lecturers->do_lectures_another_universities ?
                        'კი' : 'არა'
                };
            }
            return $lecturers->{$column};
        })->toArray();
    }

    private function getHeadingValue($column)
    {
        return match ($column) {
            'first_name' => 'სახელი',
            'last_name' => 'გვარი',
            'identity_number' => 'პირადობის ნომერი',
            'card_number' => 'ბარათის ნომერი',
            'address' => 'მისამართი',
            'phone' => 'ტელეფონის ნომერი',
            'date_of_birth' => 'დაბადების თარიღი',
            'email' => 'Email',
            'academic_degree_id' => 'აკადემიური ხარისხი',
            'type' => 'ტიპი',
            'affiliated' => 'აფილირებული',
            'do_lectures_another_universities' => 'სხვა უნივერსიტეტში ატარებს ლექციებს',
            'photo' => 'ფოტო',
            'cv' => 'CV'
        };
    }

    public function collection()
    {
        return $this->lecturers;
    }
}
