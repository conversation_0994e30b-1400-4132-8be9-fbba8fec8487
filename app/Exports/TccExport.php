<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TccExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function __construct(public $data) {}

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->data->map(function ($data, $index) {
            return [
                $index + 1,
                $data->registerFormInfo->last_name,
                $data->registerFormInfo->first_name,
                $data->registerFormInfo->email,
                $data->registerFormInfo->date_of_birth,
                $data->registerFormInfo->identity_number,
                $data->registerFormInfo->phone,
                $data->program->name_ka,
                $data->created_at
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'გვარი',
            'სახელი',
            'ელ-ფოსტა',
            'დაბადების თარიღი',
            'პირადობის ნომერი',
            'ტელეფონი',
            'პროგრამა',
            'რეგისტრაციის თარიღი',
        ];
    }
}
