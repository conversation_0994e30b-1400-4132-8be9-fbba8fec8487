<?php

namespace App\Exports\Student;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class StudentExportByProgram implements FromCollection, WithHeadings
{
    public function __construct(public $logs)
    {
        $this->logs = $this->logs->map(function ($log){
            return [
                $log->id,
                $log->surname,
                $log->name,
                $log->personal_id,
                $log->enrollment_order,
                Carbon::parse($log->enrollment_date)->format('d.m.Y'),
                $log->program->name_ka,
                $log->status->name_ka,
            ];
        });
    }

    public function headings(): array
    {
        return ['სტუდენტის ID', 'გვარი', 'სახელი', 'პირადი N', 'ჩარიცხვის ბრძანება', 'ბრძანების თარიღი', 'სასწავლო პროგრამა', 'სტატუსი'];
    }

    /**
     * @return Collection
     */
    public function collection(): Collection
    {
        return $this->logs;
    }
}
