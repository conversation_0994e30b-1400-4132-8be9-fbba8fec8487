<?php

namespace App\Exports\Student;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class FinanceStatusLogExport implements FromCollection, WithHeadings
{
    public function __construct(public $logs)
    {
        $this->logs = $this->logs->map(function ($log){
            return [
                optional($log->student)->name,
                optional($log->student)->surname,
                optional($log->student)->personal_id,
                optional($log->student)->enrollment_order,
                $log->student?->program?->name_ka,
                $log->status_id == 1 ? 'აქტიური' : 'შეჩერებული',
                $log->created_at,
                $log->amount,
            ];
        });
    }

    public function headings(): array
    {
        return ['სახელი', 'გვარი', 'პირადი N', 'ჩარიცხვის ბრძანება', 'სასწავლო პროგრამა', 'სტატუსი', 'თარიღი','თანხა'];
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->logs;
    }
}
