<?php

namespace App\Exports\Student;

use App\Models\Curriculum\Curriculum;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\SyllabusStudentGuest;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SyllabusPointsExport implements FromCollection, WithHeadings
{
    public array $headers;
    public $collection;

    public function __construct($learnYearId, $flowId)
    {
        $curriculum = Curriculum::query()
            ->with('syllabus')
            ->where('flow_id', $flowId)
            ->whereHas('syllabus', function($student) use($learnYearId){
                return $student->where('learn_year_id', $learnYearId);
            })
            ->get();

        $syllabusIds = $curriculum->pluck('syllabus_id');

        $studentIdsFromHistory = StudentSyllabusHistory::query()
            ->select(['id', 'student_id', 'syllabus_id'])
            ->with(['student', 'syllabus' => function($syllabus){
                $syllabus->select(['id', 'name']);
            }])
            ->whereHas('student', function($student) use($learnYearId){
                return $student->where('learn_year_id', $learnYearId);
            })
            ->whereIn('syllabus_id', $syllabusIds)
            ->get()
        ;

        $studentIdsFromGuests = SyllabusStudentGuest::query()
            ->select(['id', 'student_id', 'syllabus_id'])
            ->with(['student', 'syllabus' => function($syllabus){
                $syllabus->select(['id', 'name']);
            }])
            ->whereHas('student', function($student) use($learnYearId){
                return $student->where('learn_year_id', $learnYearId);
            })
            ->whereIn('syllabus_id', $syllabusIds)
            ->whereNotIn('student_id', $studentIdsFromHistory->pluck('student_id'))
            ->get()
        ;

        $studentSyllabus = $studentIdsFromHistory->merge($studentIdsFromGuests);
        $studentSyllabus = $studentSyllabus->groupBy('student_id');

        $assignment = StudentAssignment::query()
            ->whereIn('student_id', $studentSyllabus->pluck('*.student_id')->flatten())
            ->whereIn('syllabus_id', $studentSyllabus->pluck('*.syllabus_id')->flatten())
            ->get()
        ;

        $collection = [];
        $headers = ['Personal #','გვარი სახელი'];
        $indexN = 0;
        foreach ($studentSyllabus as $key => $students)
        {
            $collection[$indexN] = [
                $students->first()->student->personal_id,
                $students->first()->student->surname.' '.$students->first()->student->name
            ];
            foreach ($students as $student)
            {
                if(!in_array($student->syllabus->name, $headers))
                {
                    $headers[] = $student->syllabus->name;
                }

                $syllabusHeaderIndex = array_search($student->syllabus->name,$headers);

                $collection[$indexN][$syllabusHeaderIndex] = $assignment
                    ->where('syllabus_id', $student->syllabus->id)
                    ->where('student_id', $key)
                    ->sum('point')
                    ?? '0'
                ;
            }

            $indexN++;
        }

        foreach ($collection as $key => $item)
        {
            if ($key > 10)
            {
                foreach (range(0, max(array_keys($item))) as $range)
                {
                    if (!isset($item[$range]))
                    {
                        $collection[$key][$range] = '';
                    }
                }
                ksort($collection[$key]);
            }
        }

        $this->headers = $headers;
        $this->collection = $collection;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return collect($this->collection)->sortBy(function ($item) {
            return $item[1];
        });
    }

    public function headings(): array
    {
        return $this->headers;
    }
}
