<?php

namespace App\Exports\Student;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use function collect;

class StudentGroupExport implements FromCollection, WithHeadings, WithMapping
{
    const relatedColumns = ['program_id'];
    public $columns, $studentGroups;

    /**
     * @return \Illuminate\Support\Collection
     */

    public function __construct($columns, $studentGroups)
    {
        $this->columns = $columns;
        $this->studentGroups = $studentGroups;
    }

    public function collection()
    {
        return $this->studentGroups;
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        });
    }

    public function map($studentGroups): array
    {
        return collect($this->columns)->map(function ($column) use ($studentGroups) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'program_id' => $studentGroups->program->name_ka
                };
            }
            return $studentGroups->{$column};
        });
    }

    public function getHeadingValue($column)
    {
        return match ($column) {
            'name_ka' => 'სახელი (GEO)',
            'name_en' => 'სახელი (ENG)',
            'program_id' => 'პროგრამა'
        };
    }
}
