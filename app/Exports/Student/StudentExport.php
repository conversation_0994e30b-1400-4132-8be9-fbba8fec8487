<?php

namespace App\Exports\Student;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use function collect;

class StudentExport implements FromCollection, WithHeadings, WithMapping
{
    const relatedColumns = ['user_id', 'school_id', 'program_id', 'group_id', 'status_id', 'learn_year_id', 'basics_of_enrollment_id'];
    public $columns, $students;

    public function __construct($columns, $students)
    {
        $this->columns = $columns;
        $this->students = $students;
        if (!in_array('gpa', $this->columns))
        {
            $this->columns[] = 'gpa';
        }
    }

    public function headings(): array
    {
        return collect($this->columns)->map(function ($column) {
            return $this->getHeadingValue($column);
        })->toArray();
    }

    public function map($students): array
    {
        return collect($this->columns)->map(function ($column) use ($students) {
            if (in_array($column, self::relatedColumns)) {
                return match ($column) {
                    'user_id' => $students->user->name,
                    'school_id' => $students->school->name_ka,
                    'program_id' => $students->program->name_ka,
                    'group_id' => $students->studentGroup->name_ka ?? '',
                    'status_id' => $students->status->name_ka,
                    'learn_year_id' => $students->learnYear->name ?? '',
                    'basics_of_enrollment_id' => $students->basicOfEnrollment->name,
                };
            }
            return $students->{$column};
        })->toArray();
    }

    private function getHeadingValue($column)
    {
        return match ($column) {
            'address' => 'მისამართი',
            'basics_of_enrollement_id' => 'ჩარიცხვის საფუძვლები',
            'bio' => 'ბიო',
            'birthday' => 'დაბადების თარიღი',
            'citizenship' => 'მოქალაქეობა',
            'created_at' => 'შექმნის თარიღი',
            'cv_file_name' => 'CV File სახელი',
            'deleted_at' => 'წაშლის თარიღი',
            'diploma_file_name' => 'დიპლომის ფაილის სახელი',
            'diploma_taken' => 'დიპლომი',
            'diploma_taken_date' => 'დიპლომის აღების თარიღი',
            'email' => 'Email',
            'photo' => 'Photo',
            'enrollment_date' => 'ჩარიცხვის თარიღი',
            'enrollment_order' => 'ჩარიცხვის ორდერი',
            'group_id' => 'ჯგუფი',
            'id' => 'Id',
            'learn_year_id' => 'ნაკადი',
            'mobility' => 'მობილობა',
            'motivation_article_file_name' => 'სამოტივაციო წერილის ფაილის სახელი',
            'name' => 'სახელი',
            'notes' => 'შენიშვნა',
            'personal_id' => 'პირადობის ნომერი',
            'personal_id_number' => 'საბუთის ნომერი',
            'phone' => 'ტელეფონი',
            'program_id' => 'პროგრამა',
            'school_id' => 'სკოლა',
            'sex' => 'სქესი',
            'status_id' => 'სტატუსი',
            'surname' => 'გვარი',
            'transcript_file_name' => 'დიპლომის ფაილის სახელი',
            'updated_at' => 'განახლება',
            'user_id' => 'სტუდენტი',
            'gpa' => 'GPA'
        };
    }

    public function collection()
    {
        return $this->students;
    }
}
