<?php

namespace App\Http\Resources\Administration;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CalendarLectureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'type' => 'lecture',
            'display' => 'block',
            'is_lecture' => $this->is_lecture,
            'title' => $this->syllabus->name ?? null,
            'start' => Carbon::parse($this->lecture_date . ' ' . $this->start_time)->format('Y-m-d H:i'),
            'end' => Carbon::parse($this->lecture_date . ' ' . $this->end_time)->format('Y-m-d H:i'),
            'color' => $this->syllabus->learnYear->program->color ?? "#acc5ff",
            'lecturer' => [
                'id' => $this->lecturer->id,
                'name' => $this->lecturer->first_name . ' ' . $this->lecturer->last_name,
            ],
            'auditorium' => [
                'id' => $this?->auditorium?->id ?? null,
                'name' => $this?->auditorium?->name ?? null,
            ],
            'campus' => [
                'id' => $this?->auditorium?->campus?->id ?? null,
                'name' => $this?->auditorium?->campus?->name_ka ?? null,
            ],
            'syllabus' => [
                'id' => $this->syllabus->id ?? null,
                'name' => $this->syllabus->name ?? null,
            ]
        ];
    }
}
