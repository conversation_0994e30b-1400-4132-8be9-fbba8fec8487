<?php

namespace App\Http\Resources\Administration;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CalendarEventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'type' => 'event',
            'display' => 'block',
            'title' => $this->title,
            'description' => $this->description,
            'start' => Carbon::parse($this->start)->format('Y-m-d H:i'),
            'end' => Carbon::parse($this->end)->format('Y-m-d H:i'),
            'color' => '#009EF7'
        ];
    }
}
