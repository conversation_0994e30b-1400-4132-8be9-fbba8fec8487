<?php

namespace App\Http\Resources;

use App\Models\Finance\Finance;
use App\Models\Reestry\Student\Student;
use App\Services\FinanceService;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;

class FinanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $student = Student::wherePersonalId($this->piradi_nom)->first();
        $financeService = new FinanceService($student->user_id, $this->id);
        $extra = $this->Qrter > 2
            ? $this->dam_sagnebi + Finance::where('piradi_nom', '=', $this->piradi_nom)
                ->where('saswavlo_weli', '=', $this->saswavlo_weli)
                ->where('Qrter', '=', 3)->first()->extra : $this->dam_sagnebi;
        Cache::increment('ratingSale',(float)($this->sareitingo_fasdakleba + $this->grantianis_fasdakleba));
        Cache::increment('extra',(float)$this->extra);
        Cache::increment('contractMoney',(float)($this->kontraqtis_tanxa));
        return [
            'id' => $this->id,
            'quarter' => $this->Qrter,
            'ratingSale' => $this->sareitingo_fasdakleba + $this->grantianis_fasdakleba,
            'extra' => $this->extra,
            'academicMoney' => (float) $this->akademiuris_tanxa1,
            'firstName' => $student->name,
            'lastName' => $student->surname,
            'personalId' => $this->piradi_nom,
            'contractMoney' => $this->kontraqtis_tanxa,
            'extraSubjects' => $extra,
            'has_graphic' => $financeService->hasGraphic(),
            'oldDebt' => $financeService->oldDebt(),
            'currentYearDebtWithGrant' => $financeService->currentYearDebtWithGrant(),
            'debt' => $financeService->debt(),
            'quarterDebt' =>  $financeService->quarterDebt() < 0 ? $financeService->quarterDebt() : null,
            'quarterExtra' =>  $financeService->quarterDebt() > 0 ? $financeService->quarterDebt() : null,
            'grant' => $this->sax_granti * $this->Qrter,
            'otherHelp' => (int) $financeService->getSumByColumn('meriis_daxmareba'),
            'socialHelp' => (int) $financeService->getSumByColumn('sax_daxmareba'),
            //'payments' => $financeService->payments(),
            'totalPriceCurrent' => $financeService->currentTotalPrice(),
            'studentPaid' => (float) $financeService->getSumByColumn('charicxuli_studenti'),
            'currentYearStudentDebt' => (float) $financeService->getSumByColumn('charicxuli_studenti')
        ];
    }
}
