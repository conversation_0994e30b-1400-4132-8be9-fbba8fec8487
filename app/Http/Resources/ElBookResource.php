<?php

namespace App\Http\Resources;

use App\Models\Topic;
use Illuminate\Http\Resources\Json\JsonResource;

class ElBookResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'author' => $this->author ?? '',
            'subject' => $this->subject ?? '',
            'lecturer' =>  $this->lecturer?->first_name .
                ' ' . $this->lecturer?->last_name,
            'topic' => $this->topics?->pluck('title'),
            'published_date' => $this->published_date,
            'created_at' => $this->created_at,
            'files' => $this->files?->pluck('filename','path')->all()
        ];
    }
}
