<?php

namespace App\Http\Requests\Reestry\Auditorium;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'campus_id' => 'required|exists:campuses,id',
            'quantity' => 'required|integer',
            'student_aid' => 'required|boolean',
            'projector' => 'required|boolean',
            'multimedia' => 'required|boolean',
            'exam_audience' => 'required|boolean',
            'cameras' => 'required|boolean',
            'computer_lab' => 'required|boolean',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'შეავსე აუდიტორიის სახელის ველი',
            'campus_id.required' => 'მიუთითე კამპუსი',
            'campus_id.exists' => 'ასეთი კამპუსი არ არსებობს',
            'quantity.required' => 'მიუთითე სტუდენტების რაოდენობა',
            'quantity.integer' => 'მიუთითე რაოდენობა (რიცხვი)',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
