<?php

namespace App\Http\Requests\Reestry\Administration;

use App\Models\Reestry\Administration\Administration;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'administration_position_id' => 'nullable|exists:administration_positions,id',
            'administration_item_id' => 'nullable|exists:administration_items,id',
            'school_id' => 'nullable|exists:schools,id',
            'phone' => [
                'nullable',
                Rule::unique('administrations', 'phone')->ignore($this->route('administration')),
                'min:9',
            ],
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore(
                    Administration::find($this->route('administration'))->user_id),
            ],
            'identity_number' => [
                'required',
                'string',
                Rule::unique('administrations', 'identity_number')->ignore($this->route('administration')),
            ],
//            'photo' => 'sometimes|image',
//            'cv' => 'sometimes|mimes:pdf',
            'role_id' => 'sometimes|exists:roles,id',
            'program_ids' => 'sometimes|array',
            'program_ids.*' => 'sometimes|exists:programs,id'
        ];

        if ($this->hasFile('photo')) {
            $rules['photo'] = 'image';
        }

        if ($this->hasFile('cv')) {
            $rules['cv'] = 'required|mimes:pdf,doc,docx';
        }
        return $rules;
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
