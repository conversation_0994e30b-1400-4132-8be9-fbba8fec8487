<?php

namespace App\Http\Requests\Reestry\Administration;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'administration_position_id' => 'nullable|exists:administration_positions,id',
            'administration_item_id' => 'nullable|exists:administration_items,id',
            'school_id' => 'nullable|exists:schools,id',
            'photo' => 'sometimes|image',
            'cv' => 'sometimes|mimes:pdf,doc,docx',
            'role_id' => 'sometimes|exists:roles,id',
            'phone' => [
                'nullable',
                'unique:administrations,phone,NULL,id,deleted_at,NULL',
                'min:9',
            ],
            'email' => [
                'required',
                'email',
                'unique:users,email,NULL,id,deleted_at,NULL',
            ],
            'identity_number' => [
                'required', 'string',
                'unique:administrations,identity_number,NULL,id,deleted_at,NULL',
            ],
            'program_ids' => 'sometimes|array',
            'program_ids.*' => 'sometimes|exists:programs,id'
        ];
    }

    public function messages()
    {
        return [
            'first_name.required' => 'შეავსე სახელის ველი',
            'last_name.required' => 'შეავსე გვარის ველი',
            'administration_position_id.required' => 'მიუთითე ადმინისტრაციის პოზიცია',
            'administration_position_id.exists' => 'ასეთი ადმინისტრაციის პოზიცია არ არსებობს',
            'photo.required' => 'ატვირთე ფოტო',
            'photo.image' => 'ატვირთული ფაილი არ არის ფოტო',
            'cv.required' => 'ატვირთე CV',
            'cv.mimes' => 'ატვირთული ფაილი არ არის CV',
            'phone.required' => 'შეავსე ტელეფონის ნომრის ველი',
            'phone.min' => 'მიუთითე სწორად ტელეფონის ნომერი',
            'phone.unique' => 'ასეთი ტელეფონის ნომრით ადმინისტრაციის წევრი უკვე არსებობს',
            'email.required' => 'შეავსე Email ველი',
            'email.email' => 'მიუთითე Email სწორ ფორმატში',
            'email.unique' => 'ასეთი Email ით ადმინისტრაციის წევრი უკვე არსებობს',
            'identity_number.required' => 'შეავსე პირადობის ნომრის ველი',
            'identity_number.min' => 'მიუთითე პირადობის ნომერი სწორად',
            'identity_number.max' => 'მიუთითე პირადობის ნომერი სწორად',
            'identity_number.unique' => 'ასეთი პირადობის ნომრით ადმინისტრაციის წევრი უკვე არსებობს',
            'identity_number.integer' => 'მიუთითე პირადობის ნომერი სწორად',
            'administration_item_id.required_without' => 'მიუთითე ან სკოლა ან ადმინისტრაციული ერთეული'
        ];
    }

    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
