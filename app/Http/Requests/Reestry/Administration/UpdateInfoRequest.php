<?php

namespace App\Http\Requests\Reestry\Administration;

use App\Models\Reestry\Administration\Administration;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'photo' => 'sometimes|image',
            'first_name' => 'required',
            'phone' => [
                'required',
                Rule::unique('administrations', 'phone')
                    ->ignore(optional(Administration::where('id', $this->route('id'))->first())->id),
                'min:9',
            ],
//            'email' => [
//                'required',
//                'email',
//                Rule::unique('users', 'email')->ignore($this->route('id')),
//            ],
            'cv' => 'sometimes|mimes:pdf'
        ];
    }
}
