<?php

namespace App\Http\Requests\Reestry\Student;

use Illuminate\Foundation\Http\FormRequest;

class StudentAttachGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'id' => 'required|exists:student_groups,id',
            'student_ids' => 'required|array',
            'student_ids.*' => 'required|exists:students,id'
        ];
    }
}
