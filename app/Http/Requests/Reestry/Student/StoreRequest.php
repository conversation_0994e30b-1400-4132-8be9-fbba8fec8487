<?php

namespace App\Http\Requests\Reestry\Student;

use App\Validations\Student\FileValidations;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{

    const fileTypes = ['cv_file_name', 'diploma_file_name', 'transcript_file_name', 'motivation_article_file_name', 'photo'];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'email' => 'required|unique:users,email,NULL,id,deleted_at,NULL',
            'name' => 'required|max:255',
            'surname' => 'required|max:255',
            'personal_id' => 'required',
//            'personal_id' => 'required|unique:students,personal_id,NULL,id,deleted_at,NULL',
            'personal_id_number' => 'sometimes|max:255',
            'sex' => 'required|max:1',
            'phone' => 'required|unique:students|max:255',
            'school_id' => 'required|max:255',
            'enrollment_date' => 'sometimes',
            'program_id' => 'required|max:255',
            'group_id' => 'sometimes|exists:student_groups,id',
            'status_id' => 'required|exists:student_status_lists,id',
            'learn_year_id' => 'sometimes|exists:learn_years,id',
            'mobility' => 'sometimes|max:255',
            'basics_of_enrollement_id' => 'sometimes|exists:student_basics_of_enrollments,id',
            'notes' => 'sometimes|max:255',
            'photo' => 'sometimes|mimes:jpeg,png,jpg,gif,svg|max:100000',
            'cv_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
            'diploma_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf,jpg,png,jpeg|max:100000',
            'transcript_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
            'motivation_article_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
        ];
        if ($this->has('diploma_taken')) {
            $rules['diploma_taken_date'] = 'required';
        }
        return $rules;
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
