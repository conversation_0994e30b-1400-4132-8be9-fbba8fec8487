<?php

namespace App\Http\Requests\Reestry\Student\Group;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ka' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'program_id' => 'required|exists:programs,id',
        ];
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
