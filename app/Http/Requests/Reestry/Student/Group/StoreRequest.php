<?php

namespace App\Http\Requests\Reestry\Student\Group;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ka' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'program_id' => 'required|exists:programs,id',
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელის ველი',
            'name_en.required' => 'Name field is required',
            'program_id.required' => 'მიუთითე პროგრამა',
            'program_id.exists' => 'ასეთი პროგრამა არ არსებობს'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
