<?php

namespace App\Http\Requests\Reestry\Student;

use App\Models\Reestry\Student\Student;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use JetBrains\PhpStorm\ArrayShape;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|max:255',
            'surname' => 'required|max:255',
            'email' => ['required', 'email',
                Rule::unique('users', 'email')
                    ->ignore(Student::find($this->route('student'))->user_id)
            ],
            'personal_id' => 'required',
//            'personal_id' => [
//                'required',
//                Rule::unique('students', 'personal_id')
//                    ->ignore($this->route('student'))
//            ],
            'phone' => ['required',
                Rule::unique('students', 'phone'
                )->ignore($this->route('student'))
            ],
            'personal_id_number' => [
                'sometimes',
                Rule::unique('students', 'personal_id_number')
                    ->ignore($this->route('student'))
            ],
            'sex' => 'required|max:1',
            'school_id' => 'required|max:255',
            'program_id' => 'required|max:255',
            'enrollment_date' => 'sometimes',
            'basics_of_enrollement_id' => 'sometimes|exists:student_basics_of_enrollments,id',
            'group_id' => 'sometimes|exists:student_groups,id',
            'status_id' => 'required|exists:student_status_lists,id',
            'learn_year_id' => 'sometimes|exists:learn_years,id',
            'mobility' => 'sometimes|max:255',
            'notes' => 'sometimes|max:255',
            'cv_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
            'diploma_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf,jpg,png,jpeg|max:100000',
            'transcript_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
            'motivation_article_file_name' => 'sometimes|mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000',
            'photo' => 'sometimes|mimes:jpg,png,jpeg'
        ];
        if ($this->has('diploma_taken')) {
            $rules['diploma_taken_date'] = 'required';
        }
        return $rules;
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
