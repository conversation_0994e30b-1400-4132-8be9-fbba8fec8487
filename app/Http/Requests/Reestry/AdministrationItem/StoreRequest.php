<?php

namespace App\Http\Requests\Reestry\AdministrationItem;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ka' => 'required',
            'name_en' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელი (GEO) ველი',
            'name_en.required' => 'შეავსე სახელი (ENG) ველი'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
