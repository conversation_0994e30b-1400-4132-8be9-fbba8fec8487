<?php

namespace App\Http\Requests\Reestry\School;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use function response;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        return [
            'name_en'=>['required',
                Rule::unique('schools', 'name_en')->ignore($this->route('school'))
            ],
            'name_ka'=>['required',
                Rule::unique('schools', 'name_ka')->ignore($this->route('school'))
            ],
            'campus_id'=>['required','exists:campuses,id'],
        ];
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
