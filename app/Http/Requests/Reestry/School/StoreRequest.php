<?php

namespace App\Http\Requests\Reestry\School;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_en'=>['required','unique:schools,name_en,NULL,id,deleted_at,NULL'],
            'name_ka'=>['required','unique:schools,name_ka,NULL,id,deleted_at,NULL'],
            'campus_id'=>['required','exists:campuses,id']
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელის ველი',
            'name_ka.unique' => 'ასეთი სახელით სკოლა უკვე არსებობს',
            'name_en.required' => 'Name is required',
            'name_en.unique' => 'Name must be unique',
            'campus_id.required' => 'აირჩიე კამპუსი',
            'campus_id.exists' => 'ასეთი კამპუსი არ არსებობს'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
