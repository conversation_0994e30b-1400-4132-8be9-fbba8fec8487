<?php

namespace App\Http\Requests\Reestry\Program;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_en' => 'required|string|max:255',
            'name_ka' => 'required|string|max:255',
            'color'   => 'required|string|max:255',
            'school_id' => 'required|exists:schools,id',
            'academic_degree_id' => 'required|exists:academic_degrees,id',
        ];
    }

    public function messages()
    {
        return [
            'name_en.required' => 'Name is required',
            'name_ka.required' => 'შეავსე სახელის ველი',
            'school_id.required' => 'მიუთითე სკოლა',
            'program_type_id.required' => 'მიუთითე პროგრამის ტიპი',
            'academic_degree_id.required' => 'მიუთითე აკადემიური ხარისხი',
            'school_id.exists' => 'ასეთი სკოლა არ არსებობს',
            'program_type_id.exists' => 'ასეთი პროგრამის ტიპი არ არსებობს',
            'academic_degree__id.exists' => 'ასეთი აკადემიური ხარისხი არ არსებობს',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
