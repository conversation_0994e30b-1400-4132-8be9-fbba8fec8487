<?php

namespace App\Http\Requests\Reestry\Campuse;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use function response;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        return [
            'name_ka' => ['required',
                Rule::unique('campuses', 'name_ka')->ignore($this->route('campus'))],
            'address_ka' => ['required',
                Rule::unique('campuses', 'address_ka')->ignore($this->route('campus'))],
            'name_en' => ['required',
                Rule::unique('campuses', 'name_en')->ignore($this->route('campus'))],
            'address_en' => ['required',
                Rule::unique('campuses', 'address_en')->ignore($this->route('campus'))],
        ];
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
