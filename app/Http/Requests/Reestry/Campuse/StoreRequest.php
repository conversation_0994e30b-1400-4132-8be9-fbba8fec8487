<?php

namespace App\Http\Requests\Reestry\Campuse;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ka' => ['required', 'unique:campuses,name_ka,NULL,id,deleted_at,NULL'],
            'address_ka' => ['required', 'unique:campuses,address_ka,NULL,id,deleted_at,NULL'],
            'name_en' => ['required', 'unique:campuses,name_en,NULL,id,deleted_at,NULL'],
            'address_en' => ['required', 'unique:campuses,address_en,NULL,id,deleted_at,NULL'],
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელის ველი',
            'name_ka.unique' => 'ასეთი სახელით კამპუსი უკვე არსებობს',
            'address_ka.required' => 'შეავსე მისამართის ველი',
            'address_ka.unique' => 'ასეთი მისამართით კამპუსი უკვე არსებობს',
            'name_en.required' => 'Name is required',
            'name_en.unique' => 'Name must be unique',
            'address_en.required' => 'Address is required',
            'address_en.unique' => 'Address must be required!'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
