<?php

namespace App\Http\Requests\Reestry\Lecturer;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'identity_number' =>
                [
                    'required',
                    'unique:lecturers,identity_number,NULL,id,deleted_at,NULL',
                ],
            'card_number' => [
                'sometimes',
                'string',
                'unique:lecturers,card_number,NULL,id,deleted_at,NULL',
            ],
            'address' => 'sometimes|string',
            'phone' => 'required|string|min:9|unique:lecturers,phone,NULL,id,deleted_at,NULL',
            'date_of_birth' => 'sometimes|date_format:d/m/Y',
            'email' => 'required|email|unique:users,email',
            'academic_degree_id' => 'sometimes|exists:academic_degrees,id',
            'affiliated' => 'boolean',
            'do_lectures_another_university' => 'boolean',
            'directions_id' => 'sometimes|array'
        ];
        if (isset($this->photo)) {
            $rules['photo'] = 'required|image';
        } else {
            $rules['photo'] = 'nullable|string';
        }

        if (isset($this->cv)) {
            $rules['cv'] = 'required|file|mimes:pdf,doc,docx';
        } else {
            $rules['cv'] = 'nullable|string';
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'first_name.required' => 'შეავსე სახელის ველი',
            'last_name.required' => 'შეავსე გვარის ველი',
            'identity_number.required' => 'შეავსე პირადობის ნომრის ველი',
            'identity_number.min' => 'შეავსე პირადობის ნომერი სწორად',
            'identity_number.max' => 'შეავსე პირადობის ნომერი სწორად',
            'identity_number.unique' => 'ასეთი პირადობის ნომრით ლექტორი უკვე არსებობს',
            'card_number.required' => 'შეავსე ბარათის ნომრის ველი',
            'card_number.min' => 'შეავსე ბარათის ნომერი სწორად',
            'card_number.max' => 'შეავსე ბარათის ნომერი სწორად',
            'card_number.unique' => 'ასეთი ბარათის ნომრით ლექტორი უკვე არსებობს',
            'address.required' => 'შეავსე მისამართის ველი',
            'phone.required' => 'შეავსე მობილურის ნომრის ველი',
            'phone.min' => 'ტელეფონის ნომერი არ არის მითითებული სრულად',
            'phone.unique' => 'ამ ნომრით ლექტორი უკვე არსებობს',
            'date_of_birth.required' => 'შეავსე დაბადების თარიღის ველი',
            'date_of_birth.date' => 'არ არის მითითებული დაბადების თარიღი სწორად',
            'email.required' => 'შეავსე Email ის ველი',
            'email.email' => 'მიუთითე Email სწორ ფორმატში',
            'email.unique' => 'ასეთი Email-ით ლექტორი უკვე არსებობს',
            'photo.required' => 'ატვირთე ფოტო',
            'photo.image' => 'ატვირთული ფაილი არ არის სურათი',
            'academic_degree_id.required' => 'მიუთითე აკადემიური ხარისხი',
            'academic_degree_id.exists' => 'ასეთი აკადემიური ხარისხი არ მოიძებნა!',
            'cv.required' => 'ატვირთე CV',
            'cv.mimes' => 'ატვირთული ფაილი არ არის CV',
            'directions_id.required' => 'მიუთითე მიმართულებები',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
