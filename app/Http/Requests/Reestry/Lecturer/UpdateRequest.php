<?php

namespace App\Http\Requests\Reestry\Lecturer;

use App\Models\Reestry\Lecturer\Lecturer;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        $rules = [
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'identity_number' => [
                'required',
            ],
            'card_number' => [
                'sometimes',
                'string',
            ],
            'address' => 'sometimes|string',
            'phone' => 'required|string|min:9|unique:lecturers,phone,' . $this->route('lecturer') . ',id,deleted_at,NULL',
            'date_of_birth' => 'sometimes|date_format:d/m/Y',
            'email' => ['required', 'email',
                Rule::unique('users', 'email')->withoutTrashed()->ignore(
                    Lecturer::find($this->route('lecturer'))->user_id
                )
            ],
            'academic_degree_id' => 'sometimes|exists:academic_degrees,id',
            'affiliated' => 'boolean',
            'do_lecturers_another_university' => 'boolean',
//            'directions_id' => 'required|array'
        ];

        if ($this->hasFile('photo')) {
            $rules['photo'] = 'image';
        }

        if ($this->hasFile('cv')) {
            $rules['cv'] = 'mimes:pdf,doc,docx';
        }

        return $rules;
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
