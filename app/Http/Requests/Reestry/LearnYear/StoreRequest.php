<?php

namespace App\Http\Requests\Reestry\LearnYear;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'program_id' => 'required|exists:programs,id',
            'price' => 'required|numeric',
            'start_year' => 'nullable|date',
            'end_year' => 'nullable|date|after_or_equal:start_year',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'შეავსე სასწავლო წლის სახელის ველი',
            'program_id.required' => 'მიუთითე პროგრამა',
            'program_id.exists' => 'ასეთი პროგრამა არ არსებობს'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
