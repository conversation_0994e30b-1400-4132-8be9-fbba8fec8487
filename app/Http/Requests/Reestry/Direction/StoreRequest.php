<?php

namespace App\Http\Requests\Reestry\Direction;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_en' => 'required|unique:directions,name_en,NULL,id,deleted_at,NULL',
            'name_ka' => 'required|unique:directions,name_ka,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელის (GEO) ველი',
            'name_en.required' => 'შეავსე სახელის (ENG) ველი',
            'name_ka.unique' => 'ასეთი სახელით (GEO) მიმართულება უკვე არსებობს',
            'name_en.unique' => 'ასეთი სახელით (ENG) მიმართულება უკვე არსებობს',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
