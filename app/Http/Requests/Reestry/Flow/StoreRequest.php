<?php

namespace App\Http\Requests\Reestry\Flow;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use function response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'program_id' => 'required|exists:programs,id'
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'შეავსე სახელის ველი',
            'program_id.required' => 'მიუთითე პროგრამა',
            'program_id.exists' => 'ასეთი პროგრამა არ არსებობს'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
