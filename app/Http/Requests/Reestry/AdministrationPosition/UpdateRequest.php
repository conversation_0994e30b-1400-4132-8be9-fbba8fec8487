<?php

namespace App\Http\Requests\Reestry\AdministrationPosition;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use function response;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            'name_ka' => ['required',
            Rule::unique('administration_positions','name_ka')->ignore($this->route('administration_position'))],
            'name_en' => ['required',
                Rule::unique('administration_positions','name_en')->ignore($this->route('administration_position'))],
            ];

    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
