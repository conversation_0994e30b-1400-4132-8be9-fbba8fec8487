<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetStudentSyllabusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'student_ids' => 'required|array',
            'student_ids.*' => 'required|exists:students,id',
            'syllabus_id' => 'required|exists:syllabi,id',
            'curriculum_lecture_time_ids' => 'required|array',
            'curriculum_lecture_time_ids.*' => 'required|exists:curriculum_lecture_times,id',
        ];
    }
}
