<?php

namespace App\Http\Requests;

use App\Models\Finance\Finance;
use App\Models\Reestry\Student\Student;
use App\Services\FinanceService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class FinanceScheduleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'deadlines' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    $sum = array_reduce($this->deadlines, function ($carry, $item) {
                        return $carry + $item['amount'];
                    });
                    $student = Student::whereUserId($this->user_id)
                        ->first();
                    if (!$student)
                    {
                        $fail('თანხა არასწორადაა განაწილებული');
                        return 0;
                    }
                    $studentIdNumber = $student->personal_id;
                    $studentProgramId = $student->program_id;
                    $financeId = Finance::wherePiradiNom($studentIdNumber)
                        ->where('ProgramID', $studentProgramId)
                        ->latest('id')->first()->id ?? false;
                    if (!$financeId)
                    {
                        $fail('თანხა არასწორადაა განაწილებული');
                        return 0;
                    }
                    $price = (new FinanceService($this->user_id, $financeId))->currentTotalPriceForStatement(1);
                    if (number_format($sum,2) < number_format($price,2)) {
                        $fail('თანხა არასწორადაა განაწილებული');
                    }
                    return 0;
                    //new FinanceService(Auth::id())->currentTotalPrice();
                }
            ],
            'deadlines.*.start_date' => 'required|date_format:d/m/Y|after_or_equal:today',
            'deadlines.*.end_date' => 'required|date_format:d/m/Y|after_or_equal:today', //სავარაუდოდ მომდევნო სამი დღის თარიღის არის
            'deadlines.*.amount' => 'required|numeric'
        ];
    }
}
