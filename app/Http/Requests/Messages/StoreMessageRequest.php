<?php

namespace App\Http\Requests\Messages;

use Illuminate\Foundation\Http\FormRequest;

class StoreMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => 'required|string|max:225',
            'body' => 'required|string|max:5000',
            'author_id' => 'required|exists:users,id',
            'addressees' => 'required|array',
            'addressees.*' => 'integer|exists:users,id'
        ];
    }
}
