<?php

namespace App\Http\Requests\Auth;

use App\Models\User\User;
use Auth;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'old_password' => ['required',
                function ($attribute, $value, $fail) {
                    if (!Hash::check($value, User::find(Auth::id())->password)) {
                        $fail('არსებული პაროლი არასწორია');
                    }
                }],
            'password' => 'required|confirmed|min:8'
        ];
    }

    public function messages()
    {
        return [
            'old_password.required' => 'შეიყვანე არსებული პაროლი',
            'password.required' => 'შეიყვანე ახალი პაროლი',
            'password.confirmed' => 'პაროლები არ ემთხვევა',
            'password.min' => 'პაროლი უნდა შეიცავდეს მინიმუმ 8 სიმბოლოს'
        ];
    }
}
