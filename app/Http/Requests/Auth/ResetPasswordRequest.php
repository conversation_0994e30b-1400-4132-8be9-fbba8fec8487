<?php

namespace App\Http\Requests\Auth;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'token' => ['required', 'exists:password_resets,token',
                function ($attribute, $value, $fail) {
                    $creationTime = DB::table('password_resets')
                        ->where('token', '=', $value)
                        ->first();
                    if ($creationTime) {
                        if (now()->diffInMinutes($creationTime->created_at)
                            > config('auth.password_reset_expiration_minutes')) {
                            $fail('შეცდომა! შესაძლოა ლინკი არ არის ვალიდური.');
                        }
                    }
                    else {
                        $fail('შეცდომა! შესაძლოა ლინკი არ არის ვალიდური.');
                    }
                },
            ],
            'password' => 'required|min:8|confirmed'
        ];
    }

    public function messages()
    {
        return [
            'token.required' => 'შეცდომა! შესაძლოა ლინკი არ არის ვალიდური.',
            'token.exists' => 'შეცდომა! შესაძლოა ლინკი არ არის ვალიდური.',
            'password.required' => 'შეიყვანე პაროლი',
            'password.min' => 'პაროლი უნდა შეიცავდეს მინიმუმ 8 სიბოლოს',
            'password.confirmed' => 'პაროლები არ ემთხვევა'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
