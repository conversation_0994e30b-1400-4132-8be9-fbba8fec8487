<?php

namespace App\Http\Requests\Auth;

use App\Models\User\User;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use function response;

class AuthorizationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'email' => 'required|email|exists:users,email',
            'password' => [
                'required',
                function ($attribute, $value, $fail) {
                    $user = User::where('email', $this->email);
                    if ($user->exists()) {
                        if (!Hash::check($value, $user->first()->password)) {
                            $fail('პაროლი არ არის სწორი');
                        }
                    }
                }
            ],
        ];
    }

    public function messages()
    {
        return [
            'email.required' => 'შეავსე Email ველი',
            'email.email' => 'შეიყვანე Email სწორ ფორმატში',
            'email.exists' => 'ელ-ფოსტა არ მოიძებნა',
            'password.required' => 'შეიყვანე პაროლი'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
