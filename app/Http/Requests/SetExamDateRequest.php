<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetExamDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'assignments' => 'required|array',
            'assignments.*.id' => 'required|exists:assignments,id',
            'assignments.*.exam_date' => 'sometimes|date_format:Y-m-d H:i:s',
            'assignments.*.expiration_date' => 'nullable|date_format:Y-m-d H:i:s'
        ];
    }

}
