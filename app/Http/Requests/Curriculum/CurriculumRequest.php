<?php

namespace App\Http\Requests\Curriculum;

use Illuminate\Foundation\Http\FormRequest;

class CurriculumRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'syllabus_id' => 'required|numeric|exists:syllabi,id',
            'learn_year_id' => 'required|numeric|exists:flows,id',
            'start_date' => 'required|date|date_format:d-m-Y',
            'end_date' => 'required|date|date_format:d-m-Y',
            'allowed_amount_of_students' => 'nullable|numeric',
            'minimum_amount_of_students' => 'nullable|numeric',
            'registration_start_date' => 'nullable|date|date_format:d-m-Y H:i',
            'registration_end_date' => 'nullable|date|date_format:d-m-Y H:i',
            'language' => 'nullable|string|in:ka,en',
            'student_flow_ids' => 'array',
            'student_flow_ids.*' => 'exists:students,learn_year_id',
            'lecture_groups' => 'required|array',
            'lecture_groups.*' => 'required|array',
            'lecture_groups.*.free_places' => 'nullable|numeric',
            'lecture_groups.*.lectures_count' => 'required|integer',
            'lecture_groups.*.times' => 'required|array',
            'lecture_groups.*.times.*' => 'required|array',
            'lecture_groups.*.times.*.lecture_id' => 'nullable|numeric|exists:curriculum_lectures,id',
            'lecture_groups.*.times.*.is_lecture' => 'required|numeric|in:0,1',
            'lecture_groups.*.times.*.lecturer_id' => 'required|numeric|exists:lecturers,id',
            'lecture_groups.*.times.*.lecturer_start_date' => 'nullable|date|date_format:d-m-Y',
            'lecture_groups.*.times.*.lecturer_end_date' => 'nullable|date|date_format:d-m-Y',
            'lecture_groups.*.times.*.lecturer_accounting_code' => 'required|string',
            'lecture_groups.*.times.*.payment_per_hour' => 'required|numeric',
            'lecture_groups.*.times.*.lecture_time_id' => 'nullable|numeric|exists:curriculum_lecture_times,id',
            'lecture_groups.*.times.*.week_day' => 'required|numeric|between:0,6',
            //'lecture_groups.*.times.*.lectures_count' => 'required|integer',
            'lecture_groups.*.times.*.start_time' => 'required|date_format:H:i',
            'lecture_groups.*.times.*.end_time' => [
                'required',
                function ($attribute, $value, $fail) {
                    if ($value !== '24:00') {
                        if (!strtotime($value) || date('H:i', strtotime($value)) !== $value) {
                            $fail("The $attribute field has an invalid time format.");
                        }
                    }
                },
            ],
            'lecture_groups.*.times.*.auditorium_id' => 'required|numeric|exists:auditoria,id',
            'lecture_groups.*.times.*.student_group_ids' => 'nullable|array',
            'lecture_groups.*.times.*.student_group_ids.*' => 'nullable|numeric|exists:student_groups,id',
        ];
    }

    public function attributes()
    {
        return [
            'lecture_groups.*' => 'lecture groups',
            'lecture_groups.*.times' => 'lecture groups times',
            'lecture_groups.*.times.*' => 'lecture groups times',
            'lecture_groups.*.times.*.lecture_id' => 'lecture',
            'lecture_groups.*.times.*.free_places' => 'free places',
            'lecture_groups.*.times.*.is_lecture' => 'is lecture',
            'lecture_groups.*.times.*.lecturer_id' => 'lecturer',
            'lecture_groups.*.times.*.lecturer_start_date' => 'lecturer start date',
            'lecture_groups.*.times.*.lecturer_end_date' => 'lecturer end date',
            'lecture_groups.*.times.*.lecturer_accounting_code' => 'lecturer accounting code',
            'lecture_groups.*.times.*.payment_per_hour' => 'payment per hour',
            'lecture_groups.*.times.*.lecture_time_id' => 'lecture time',
            'lecture_groups.*.times.*.week_day' => 'week day',
            'lecture_groups.*.times.*.start_time' => 'start time',
            'lecture_groups.*.times.*.end_time' => 'end time',
            'lecture_groups.*.times.*.auditorium_id' => 'auditorium',
            'lecture_groups.*.times.*.student_group_ids' => 'student groups',
            'lecture_groups.*.times.*.student_group_ids.*' => 'student group',
        ];
    }
}
