<?php

namespace App\Http\Requests\Curriculum;

use Illuminate\Foundation\Http\FormRequest;

class FreeTimeSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'week_day'      => 'required|numeric|between:0,6',
            'lecturer_id'   => 'required|exists:lecturers,id',
            'auditorium_id' => 'required|exists:auditoria,id',
            'auditorium_start_date' => 'required|date',
            'auditorium_end_date' => 'required|date',
        ];
    }
}
