<?php

namespace App\Http\Requests\Curriculum;

use Illuminate\Foundation\Http\FormRequest;

class LectureUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'lecturer_id'   => 'numeric|exists:lecturers,id',
            'auditorium_id' => 'numeric|exists:auditoria,id',
            'start'         => 'date_format:d-m-Y H:i',
            'end'           => 'date_format:d-m-Y H:i',
        ];
    }
}
