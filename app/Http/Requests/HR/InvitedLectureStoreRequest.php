<?php

namespace App\Http\Requests\HR;

use Illuminate\Foundation\Http\FormRequest;

class InvitedLectureStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'father_name' => 'nullable|string|max:255',
            'email' => 'required|string|email:dns',
            'address' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'gender' => 'nullable|boolean',
            'age' => 'nullable|integer',
            'date_of_birth' => 'required|date',
            'family_state' => 'nullable|boolean',
            'salary' => 'nullable|integer',
            'direction' => 'nullable|string|max:255',
            'school_id' => 'nullable|exists:schools,id',
            'program_ids' => 'nullable|array',
            'program_ids.*' => 'exists:programs,id',
            'course' => 'nullable|string|max:255',
            'work_type_id' => 'nullable|exists:work_types,id',
            'workplace_name' => 'nullable|string|max:255',
            'status' => 'nullable|boolean',
            'hr_invited_lecture_educations' => 'nullable|array',
            'hr_invited_lecture_educations.*.academic_degree_id' => 'nullable|exists:academic_degrees,id',
            'hr_invited_lecture_educations.*.qualification' => 'nullable|string|max:255',
            'hr_invited_lecture_educations.*.country' => 'nullable|string|max:255',
            'hr_invited_lecture_attachments' => 'nullable|array',
            'hr_invited_lecture_attachments.*' => 'nullable',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'identity_number' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'contract_start' => 'nullable|date',
            'contract_end' => 'nullable|date',
        ];
    }
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
