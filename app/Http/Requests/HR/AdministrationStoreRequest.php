<?php

namespace App\Http\Requests\HR;

use Illuminate\Foundation\Http\FormRequest;

class AdministrationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'father_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'administration_position_id' => 'nullable|exists:administration_positions,id',
            'type_of_position' => 'nullable|boolean',
            'gender' => 'nullable|boolean',
            'date_of_birth' => 'nullable',
            'age' => 'nullable|integer',
            'identity_number' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'email' => 'required|string|email:dns',
            'family_state' => 'nullable|string|max:255',
            'user_id' => 'required|exists:users,id',
            'school_id' => 'nullable|exists:schools,id',
//            'administration_item_id' => 'nullable|exists:schools,id',
            'hr_administration_educations' => 'nullable|array',
            'hr_administration_educations.*.academic_degree_id' => 'nullable|exists:academic_degrees,id',
            'hr_administration_educations.*.country' => 'nullable|string|max:255',
            'hr_administration_educations.*.qualification' => 'nullable|string|max:255',
            'hr_administration_educations.*.position' => 'nullable|string|max:255',
            'appointment' => 'nullable|boolean',
            'command_number' => 'nullable|string|max:255',
            'vacancy_command_number' => 'nullable|string|max:255',
            'vacancy_command_number_file' => 'nullable',
            'vacancy_command_number_date' => 'nullable',
            'command_date' => 'nullable',
            'appointment_number' => 'nullable|string|max:255',
            'appointment_command_number_file' => 'nullable',
            'appointment_command_number' => 'nullable|string|max:255',
            'appointment_command_number_date' => 'nullable',
            'contract_start' => 'nullable',
            'contract_end' => 'nullable',
            'contract_period' => 'nullable',
            'vacation' => 'nullable',
            'day_off' => 'nullable',
            'status' => 'nullable|boolean',
            'educational_staff' => 'nullable|boolean',
            'hr_administration_files' => 'nullable|array',
            'hr_administration_files.*' => 'nullable'
        ];
    }

    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
