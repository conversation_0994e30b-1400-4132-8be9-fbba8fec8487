<?php

namespace App\Http\Requests\HR;

use Illuminate\Foundation\Http\FormRequest;

class AcademicLectureStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'email' => 'required|email:dns',
            'identity_number' => 'required|string|max:255',
            'date_of_birth' => 'required|date',
            'phone' => 'required|string|max:255',
            'lecturer_position_id' => 'nullable|exists:lecturer_positions,id',
            'grant' => 'nullable|string|max:255',
            'affiliated' => 'nullable|boolean',
            'lecturer_category_id' => 'nullable',
            'salary' => 'nullable|integer',
            'paid_hours' => 'nullable|integer',
            'unpaid_hours' => 'nullable|integer',
            'direction' => 'nullable|string|max:255',
            'school_id' => 'nullable|exists:schools,id',
            'appointment' => 'nullable|string|max:255',
            'vacancy_command_number' => 'nullable|string|max:255',
            'vacancy_command_number_file' => 'nullable',
            'vacancy_command_number_date' => 'nullable',
            'appointment_command_number' => 'nullable|string|max:255',
            'appointment_command_number_file' => 'nullable',
            'appointment_command_number_date' => 'nullable',
            'contract_start' => 'nullable',
            'contract_end' => 'nullable',
            'contract_period' => 'nullable',
            'status' => 'nullable|boolean',
            'scopus_g' => 'nullable|boolean',
            'scopus_h' => 'nullable|boolean',
            'web_of_science_g' => 'nullable|boolean',
            'web_of_science_h' => 'nullable|boolean',
            'google_scholar_g' => 'nullable|boolean',
            'google_scholar_h' => 'nullable|boolean',
            'hr_academic_lecture_attachments' => 'nullable|array',
            'hr_academic_lecture_educations' => 'nullable|array',
//            'hr_academic_lecture_educations.*.academic_degree_id' => 'nullable|exists:academic_degrees,id',
//            'hr_academic_lecture_educations.*.qualification' => 'nullable|string|max:255',
//            'hr_academic_lecture_educations.*.country' => 'nullable|string|max:255',
            'father_name' => 'nullable|string|max:255',
            'gender' => 'nullable|boolean',
            'age' => 'nullable|integer',
            'family_state' => 'nullable|boolean',
            'user_id' => 'required|exists:users,id'
        ];
    }

    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        return response()->json($validator->errors(), 422)->throwResponse();
    }
}
