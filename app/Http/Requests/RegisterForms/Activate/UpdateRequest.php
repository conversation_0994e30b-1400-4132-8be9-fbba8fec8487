<?php

namespace App\Http\Requests\RegisterForms\Activate;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'learn_year_id' => [
                'required',
                Rule::unique('register_form_activates', 'learn_year_id')
                    ->ignore($this->route('register_form_activate'))
            ],
            'start_date' => 'required|date|date_format:m/d/Y',
            'end_date' => 'required|date|date_format:m/d/Y',
        ];
    }
}
