<?php

namespace App\Http\Requests\RegisterForms\Proffession;

use App\Models\RegisterForms\RegisterFormActivate;
use App\Models\RegisterForms\RegisterFormInfo;
use App\Services\RegisterFormService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Date;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
//        RegisterFormService::restrictedAccessForm($this->url, 4);
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'photo' => 'required|image',
            'identity_number_copy' => 'required|file',
            'program_id' => 'required|exists:programs,id',
            'flow_id' => 'required|exists:learn_years,id',
            'school' => 'required|string',
            'school_finish_date' => 'required|integer|min:1980|max:' . Date::now()->year - 1,
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'first_name_en' => 'nullable|string',
            'last_name_en' => 'nullable|string',
            'identity_number' => [
                'required',
                'regex:/^[0-9]+$/',
                'size:11'
            ],
//            'identity_number' => [
//                'required',
//                'size:11',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\ProffesionRegister'
//                        )->whereIdentityNumber($value)->exists()
//                    ) {
//                        $fail('ამ პირადობით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
//            'phone' => [
//                'required',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\ProffesionRegister'
//                        )->wherePhone($value)->exists()
//                    ) {
//                        $fail('ამ ტელეფონით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
            'phone' => 'required|integer',
            'email' => 'required|email',
//            'email' => [
//                'required',
//                'email',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\ProffesionRegister'
//                        )->whereEmail($value)->exists()
//                    ) {
//                        $fail('ამ მეილით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
            'gender' => 'required|boolean',
            'date_of_birth' => 'date|date_format:d-m-Y',
            'educations' => 'sometimes|array',
            'educations.*.university' => 'required|string',
            'educations.*.faculty' => 'required|string',
            'educations.*.academic_degree_id' => 'required|exists:academic_degrees,id',
            'educations.*.start_date' => 'required|integer|min:1970|max:' . \Date::now()->year - 1,
            'educations.*.end_date' => 'required|integer|min:1970|max:' . \Date::now()->year,
        ];
    }
}
