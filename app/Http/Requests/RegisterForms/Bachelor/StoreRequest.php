<?php

namespace App\Http\Requests\RegisterForms\Bachelor;

use App\Models\RegisterForms\RegisterFormActivate;
use App\Models\RegisterForms\RegisterFormInfo;
use App\Services\RegisterFormService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
//        RegisterFormService::restrictedAccessForm($this->url, 1);
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'parent_phone' => 'required|string',
            'photo' => 'required|image|mimes:jpg,png,jpeg|max:2048',
            'identity_number_copy' => 'required|file|max:2048',
            'program_id' => 'required|exists:programs,id',
            'identity_number' => [
                'required',
                'size:11',
                'unique:register_form_infos,identity_number',
                function ($attribute, $value, $fail) {

                    if (
                        RegisterFormInfo::where(
                            'registerable_type',
                            '=',
                            'App\Models\RegisterForms\BachelorRegister'
                        )->whereIdentityNumber($value)->exists()
                    ) {
                        $fail('ამ პირადობით ამ პროგრამაზე უკვე რეგისტრირებულია სტუდენტი');
                    }
                },
            ],
            'phone' => [
                'required',
                function ($attribute, $value, $fail) {

                    if (
                        RegisterFormInfo::where(
                            'registerable_type',
                            '=',
                            'App\Models\RegisterForms\BachelorRegister'
                        )->wherePhone($value)->exists()
                    ) {
                        $fail('ამ ტელეფონით ამ პროგრამაზე უკვე რეგისტრირებულია სტუდენტი');
                    }
                }
            ],
            'email' => [
                'required',
                'email',
                function ($attribute, $value, $fail) {

                    if (
                        RegisterFormInfo::where(
                            'registerable_type',
                            '=',
                            'App\Models\RegisterForms\MasterRegister'
                        )->whereEmail($value)->exists()
                    ) {
                        $fail('ამ მეილით ამ პროგრამაზე უკვე რეგისტრირებულია სტუდენტი');
                    }
                },
            ],
            'gender' => 'required|boolean',
            'date_of_birth' => [
                'required',
                'date',
                'date_format:d-m-Y',
                function ($attribute, $value, $fail) {
                    if (now()->diffInYears($value) < 17) {
                        $fail('ბაკალავრიატზე რეგისტრაცია შეუძლია მინიმუმ 17 წლის ადამიანს');
                    }
                },
            ],
            'first_name_en' => 'required',
            'last_name_en' => 'required',
            'school_document' => 'required|file|max:2048',
            'payment_document' => 'required|file|max:2048',
            'english_level_id' => 'required|exists:english_levels,id',
            'address' => 'required',
            'school' => 'required|string'
        ];
        if ($this->gender) {
            $rules['military_accounting'] = 'required|file|mimes:pdf,jpg,png,doc,docx|max:2048';
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'parent_phone.required' => 'ოჯახის წევრის ტელეფონის ნომრის მითითება სავალდებულოა.',
            'photo.required' => 'ფოტოს ატვირთვა სავალდებულოა.',
            'photo.image' => 'ატვირთული ფაილი უნდა იყოს ფოტო.',
            'photo.mimes' => 'ფოტოს დასაშვები ფორმატებია: JPG, PNG, JPEG.',
            'photo.max' => 'ასატვირთი ფოტოს მაქსიმალური ზომა არ უნდა იყოს 2MB-ზე მეტი',
            'identity_number_copy.required' => 'პირადობის მოწმობის ასლის ატვირთვა სავალდებულოა.',
            'identity_number_copy.file' => 'პირადობის მოწმობის ასლის ასატვირთი ფაილის ფორმატებია: JPG,PNG,PDF.',
            'identity_number_copy.max' => 'ასატვირთი პირადობის მოწმობის ასლის მაქსიმალური ზომა არ უნდა იყოს 2MB-ზე მეტი',
            'program_id.required' => 'პროგრამის მითითება სავალდებულოა.',
            'program_id.exists' => 'არასწორი პროგრამა.',
            'identity_number.required' => 'პირადი ნომრის მითითება სავალდებულოა.',
            'identity_number.size' => 'The identity number must be 11 characters long.',
            'identity_number.unique' => 'A student with this identity number is already registered for this program.',
            'phone.required' => 'ტელეფონის ნომრის მითითება სავალდებულოა.',
            'email.required' => 'ელ.ფოსტის მითითება სავალდებულოა.',
            'email.email' => 'ელ.ფოსტის მისამართი უნდა იყოს სწორი ფორმატით.',
            'gender.required' => 'სქესის მითითება სავალდებულოა.',
            'gender.boolean' => 'სქესის მითითება სავალდებულოა.',
            'date_of_birth.required' => 'დაბადების თარიღის მითითება სავალდებულოა.',
            'date_of_birth.date' => 'The date of birth must be a valid date.',
            'date_of_birth.date_format' => 'The date of birth must be in the format dd-mm-yyyy.',
            'date_of_birth.before' => 'Registration is only allowed for individuals aged 17 or older.',
            'first_name_en.required' => 'ინგლისურად სახელის მითითება სავალდებულოა.',
            'last_name_en.required' => 'ინგლისურად გვარის მითითება სავალდებულოა.',
            'school_document.required' => 'ატესტატის ატვირთვა სავალდებულოა.',
            'school_document.file' => 'ატესტატის ასატვირთი ფაილის ფორმატებია: JPG,PNG,PDF.',
            'school_document.max' => 'ასატვირთი ატესტატის მაქსიმალური ზომა არის 2MB.',
            'payment_document.required' => 'გადახდის დამადასტურებელი დოკუმენტის ატვირთვა სავალდებულოა.',
            'payment_document.file' => 'გადახდის დამადასტურებელი დოკუმენტის ასატვირთი ფაილის ფორმატებია: JPG,PNG,PDF.',
            'payment_document.max' => 'გადახდის დამადასტურებელი დოკუმენტის მაქსიმალური ზომა არის 2MB.',
            'english_level_id.required' => 'ინგლისურის დონის მითთება სავალდებულოა.',
            'english_level_id.exists' => 'ინგლისურის დონის მითთება სავალდებულოა.',
            'address.required' => 'მისამართის მითითება სავალდებულოა.',
            'school.required' => 'მიუთითეთ სკოლა რომელიც დაამთავრეთ.',
            'military_accounting.required' => 'სამხედრო ბარათის ატვირთვა სავალდებულოა.',
            'military_accounting.file' => 'სამხედრო ბარათის ასატვირთი ფაილის ფორმატებია: JPG,PNG,PDF.',
            'military_accounting.mimes' => 'სამხედრო ბარათის ასატვირთი ფაილის ფორმატებია: JPG,PNG,PDF.',
            'military_accounting.max' => 'სამხედრო ბარათის ასლის მაქსიმალური ზომა არის 2MB.',

        ];
    }
}
