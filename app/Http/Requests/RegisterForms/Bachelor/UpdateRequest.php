<?php

namespace App\Http\Requests\RegisterForms\Bachelor;

use App\Models\RegisterForms\BachelorRegister;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'parent_phone' => 'required|string',
            'photo' => 'sometimes|image',
            'identity_number_copy' => 'sometimes|image',
            'school' => 'required|string',
            'military_accounting' => 'sometimes|image',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'identity_number' => [
                'required',
                'min:11',
                'max:11',
                Rule::unique('register_form_infos', 'identity_number')
                    ->ignore(BachelorRegister::with(['registerFormInfo'])
                        ->find($this->bachelor_register)
                        ->registerFormInfo->id)
            ],
            'phone' => [
                'required',
                Rule::unique('register_form_infos', 'phone')
                    ->ignore(BachelorRegister::with(['registerFormInfo'])
                        ->find($this->bachelor_register)
                        ->registerFormInfo->id)
            ],
            'email' => [
                'required',
                'email',
                Rule::unique('register_form_infos', 'email')
                    ->ignore(BachelorRegister::with(['registerFormInfo'])
                        ->find($this->bachelor_register)
                        ->registerFormInfo->id)
            ],
            'gender' => 'required|boolean',
            'date_of_birth' => 'date|date_format:d-m-Y'];
    }
}
