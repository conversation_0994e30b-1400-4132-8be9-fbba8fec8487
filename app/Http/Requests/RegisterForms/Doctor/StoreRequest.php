<?php

namespace App\Http\Requests\RegisterForms\Doctor;

use App\Models\RegisterForms\RegisterFormActivate;
use App\Models\RegisterForms\RegisterFormInfo;
use App\Services\RegisterFormService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
//        RegisterFormService::restrictedAccessForm($this->url,3);

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'photo' => 'required|image',
            'cv' => 'required|mimes:pdf',
            'about_university' => 'required|string',
            'identity_number_copy' => 'required|file',
            //'school' => 'required|string',
            'program_id' => 'required|exists:programs,id',
            'flow_id' => 'required|exists:learn_years,id',
            'english_level_id' => 'required|exists:english_levels,id',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'identity_number' => [
                'required',
                'regex:/^[0-9]+$/',
                'size:11'
            ],
            'phone' => 'required|integer',
//            'identity_number' => [
//                'required',
//                'size:11',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\DoctorRegister'
//                        )->whereIdentityNumber($value)->exists()
//                    ) {
//                        $fail('ამ პირადობით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                },
//            ],
//            'phone' => [
//                'required',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\DoctorRegister'
//                        )->wherePhone($value)->exists()
//                    ) {
//                        $fail('ამ ტელეფონით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                },
//            ],
//            'email' => [
//                'required',
//                'email',
//                function ($attribute, $value, $fail) {
//
//                    if (
//                        RegisterFormInfo::where(
//                            'registerable_type',
//                            '=',
//                            'App\Models\RegisterForms\DoctorRegister'
//                        )->whereEmail($value)->exists()
//                    ) {
//                        $fail('ამ მეილით ამ პროგრამაზე უკვე რეგისტრირებულია აპლიკანტი');
//                    }
//                }
//            ],
            'email' => 'required|email',
            'gender' => 'required|boolean',
            'date_of_birth' => 'date|date_format:d-m-Y',
            'certificates' => 'sometimes|array',
            'certificates.*' => 'sometimes|file',
            'address' => 'required|string',
            'educations' => 'required|array',
            'educations.*.university' => 'required|string',
            'educations.*.faculty' => 'required|string',
            'educations.*.start_date' => 'required|integer|min:1970|max:' . \Date::now()->year - 1,
            'educations.*.end_date' => 'required|integer|min:1970|max:' . \Date::now()->year,
            'infos' => 'sometimes|array',
            'infos.*.title' => 'sometimes|string',
            'recommendations' => 'sometimes|array',
            'recommendations.*.person' => 'sometimes|string',
            'recommendations.*.phone' => 'sometimes|string',
        ];
    }
}
