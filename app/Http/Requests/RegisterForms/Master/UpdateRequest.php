<?php

namespace App\Http\Requests\RegisterForms\Master;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'parent_phone' => 'required',
            'photo' => 'sometimes|image',
            'identity_number_copy' => 'sometimes|image',
            'school' => 'required|string',
            'card_number' => 'required',
            'address' => 'required|string',
            'program_id' => 'required|exists:programs,id',
            'motivation_letter' => 'sometimes|image',
            'marks_paper' => 'sometimes|image',
            'english_level_id' => 'required|exists:english_levels,id',
            'langs.*.language' => 'required|string',
            'level' => 'required|string',
            'certificate' => 'boolean',
            'certificates' => 'sometimes|array',
            'certificates.*' => 'sometimes|image',
            'recommendations' => 'sometimes|array',
            'recommendations.*.person' => 'sometimes|string',
            'recommendations.*.phone' => 'sometimes|string',        ];
    }
}
