<?php

namespace App\Http\Requests\RegisterForms\Training;

use App\Models\RegisterForms\RegisterFormActivate;
use App\Models\RegisterForms\RegisterFormInfo;
use App\Services\RegisterFormService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
//        RegisterFormService::restrictedAccessForm($this->url,5);
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
//            'photo' => 'nullable|mimes:png,jpg,jpeg',
//            'cv' => 'nullable|mimes:pdf,jpg,jpeg,png,doc,docx',
            'identity_number_copy' => 'required|mimes:doc,docx,pdf,png,jpg,jpeg',
            'program_id' => 'required|exists:programs,id',
            'english_level_id' => 'nullable|exists:english_levels,id',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'first_name_en' => 'required|string',
            'last_name_en' => 'required|string',
            'identity_number' => [
                'required',
                'regex:/^[0-9]+$/',
                'size:11'
            ],
            'phone' => 'required|integer',
            'email' => 'required|email',
            'gender' => 'required|boolean',
            'date_of_birth' => [
                'required',
                'date',
                'date_format:d-m-Y',
                function ($attribute, $value, $fail) {
                    if (now()->diffInYears($value) < 18) {
                        $fail('ტრენინგ კურსებზე რეგისტრაცია შეუძლია მინ 18 წლის ადამიანს');
                    }
                },
            ],
            'certificates' => 'sometimes|array',
            'certificates.*' => 'sometimes|file',
            'comment' => 'nullable|string',
            'city' => 'required',
            'street' => 'required',
            'city_of_birth' => 'required',
//            'programs' => 'required|array',
//            'programs.*.program_id' => 'required|integer|digits_between:1,3',
//            'programs.*.level_id' => 'required|integer|digits_between:1,3',
            'educations' => 'required|array',
            'educations.*.university' => 'nullable|string',
            'educations.*.faculty' => 'nullable|string',
            'educations.*.academic_degree_id' => 'nullable|exists:academic_degrees,id',
            'educations.*.start_date' => 'nullable|integer|min:1970|max:' . \Date::now()->year - 1,
            'educations.*.end_date' => 'nullable|integer|min:1970|max:' . \Date::now()->year,
            'employment_status' => 'required|boolean',
            'works' => 'nullable|array',
        ];

        if ($this->input('employment_status') == true) {
            $worksRules = [
                'organization' => 'required|string',
                'position' => 'required|string',
                'employment_field' => 'required|integer|digits_between:1,4',
                'start_date' => 'required|integer|min:1970|max:' . \Date::now()->year,
                'end_date' => 'nullable'
            ];

            foreach ($this->input('works', []) as $key => $value) {
                $rules["works.$key.organization"] = $worksRules['organization'];
                $rules["works.$key.position"] = $worksRules['position'];
                $rules["works.$key.employment_field"] = $worksRules['employment_field'];
                $rules["works.$key.start_date"] = $worksRules['start_date'];
                $rules["works.$key.end_date"] = $worksRules['end_date'];
            }
        }

        $rules['infos'] = 'sometimes|array';
        $rules['infos.*.title'] = 'sometimes|string';

        return $rules;
    }
}
