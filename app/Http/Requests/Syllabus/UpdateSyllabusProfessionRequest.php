<?php

namespace App\Http\Requests\Syllabus;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSyllabusProfessionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'semester_id' => 'required|exists:semesters,id',
            'status_id' => 'required|exists:statuses,id',
            'learn_year_id' => 'required|exists:learn_years,id',
            'academic_degree_id' => 'required|exists:academic_degrees,id',
            'is_profession' => 'required|boolean',
            'name' => 'required|string|max:255',
            'code' => 'required',
            'credits' => 'required|numeric',
            'contact_hours' => 'required|integer',
            'lecture_hours' => 'required|integer',
            'total_hours' => 'required|integer',
            'independent_work_hours' => 'nullable|integer',
            'lecturers' => 'required|array',
            'lecturers.*.id' => 'required',
            'lecturers.*.start_time' => 'required',
            'lecturers.*.end_time' => 'required',
            'lecturers.*.week_day' => 'required',
            'lecturers.*.lecturer_id' => 'required',
            'exams' => 'required|array',
            'exams.*' => 'required|array',
            'exams.*.id' => 'required',
            'exams.*.parent_id' => 'nullable|integer',
            'exams.*.calculation_type' => 'nullable',
            'exams.*.score' => 'nullable|integer',
            'exams.*.min_score' => 'nullable|integer',
            'exams.*.description' => 'nullable|string',
            'exams.*.description_en' => 'nullable|string',
        ];

        $this->merge([
            'mid_and_final_exam_hours' => $this->input('mid_and_final_exam_hours', 0),
            //'is_profession' => $this->input('is_profession', 1),
//            'syllabus_type_id' => $this->input('syllabus_type_id', 2),
            'syllabus_type_id' => 2,
        ]);

        return $rules;
    }
}
