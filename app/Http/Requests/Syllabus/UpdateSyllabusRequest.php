<?php

namespace App\Http\Requests\Syllabus;

use Illuminate\Foundation\Http\FormRequest;
use Validator;

class UpdateSyllabusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $id = $this->input('id');
        $rules = [
            'learn_year_id' => 'required_if:is_profession,0|exists:learn_years,id',
            'name' => 'required|string|max:255',
            'academic_degree_id' => 'required|exists:academic_degrees,id',
            'status_id' => 'required_if:is_profession,0|exists:statuses,id',
//            'semester_id' => 'required|integer',
            //'code' => 'required',
            'credits' => 'nullable|numeric',
            'contact_hours' => 'nullable|integer',
            'lecture_hours' => 'nullable|integer',
            'seminar_hours' => 'nullable|integer',
            'mid_and_final_exam_hours' => 'nullable|integer',
            'independent_work_hours' => 'nullable|integer',
            'total_hours' => 'nullable|integer',
            'lecturers' => 'required|array',
            'lecturers.*.id' => 'required',
            'lecturers.*.start_time' => 'required',
            'lecturers.*.end_time' => 'required',
            'lecturers.*.week_day' => 'required',
            'lecturers.*.lecturer_id' => 'required',
            'goal' => 'required_if:is_profession,0|string',
            'prerequisites_ids.*' => 'exists:syllabi,id|integer',
            'method_ids' => 'required_if:is_profession,0|array',
            'method_ids.*' => 'required|exists:methods,id',
            // weeks' validations
            'weeks' => 'required_if:is_profession,0|array',
            'weeks.*' => 'required|array',
            'weeks.*.number' => 'required|integer',
            'weeks.*.title' => 'required|string',
            'weeks.*.main_literature' => 'required|string',
            'weeks.*.secondary_literature' => 'nullable|string',
            'weeks.*.title_en' => 'nullable|string',
            'weeks.*.main_literature_en' => 'nullable|string',
            'weeks.*.secondary_literature_en' => 'nullable|string',
            'assessing_system' => 'nullable|string',
            'final_exam_prerequisite' => 'nullable',
            // exams' validations old
//            'exams' => 'required|array',
//            'exams.*' => 'required|array',
//            'exams.*.title' => 'required|string',
//            'exams.*.score' => 'required|integer',
//            'exams.*.number_of_children' => 'nullable|integer',
//            'exams.*.description' => 'required|string',
//            'exams.*.min_score' => 'nullable|integer',
        //new
            'exams' => 'required|array',
            'exams.*' => 'required|array',
            'exams.*.id' => 'required|exists:assessment_components,id',
            'exams.*.parent_id' => 'nullable|integer',
            'exams.*.calculation_type' => 'nullable',
            'exams.*.score' => 'required_if:is_profession,0|integer',
            'exams.*.min_score' => 'nullable|integer',
            'exams.*.description' => 'required|string',
            'exams.*.description_en' => 'nullable|string',

            'main_literature' => 'required_if:is_profession,0|string',
            'additional_literature' => 'required_if:is_profession,0|string',
            'retake_missed_assignment' => 'nullable|string',

            'academic_honesty' => 'nullable|string',
            //'learning_outcome_ids.*' => 'exists:learning_outcomes,id|integer',
            'learning_outcome_knowledge' => 'nullable|string',
            'learning_outcome_skill' => 'nullable|string',
            'learning_outcome_responsibility' => 'nullable|string',
            'additional_information' => 'nullable|string',
            'exam_rules' => 'nullable|string',
            'exam_percent' => 'nullable|integer',
            'name_en' => 'nullable|string',
            'goal_en' => 'nullable|string',
            'assessing_system_en' => 'nullable|string',
            'final_exam_prerequisite_en' => 'nullable|string',
            'retake_missed_assignment_en' => 'nullable|string',
            'main_literature_en' => 'nullable|string',
            'additional_literature_en' => 'nullable|string',
            'academic_honesty_en' => 'nullable|string',
            'additional_information_en' => 'nullable|string',
            'learning_outcome_knowledge_en' => 'nullable|string',
            'learning_outcome_skill_en' => 'nullable|string',
            'learning_outcome_responsibility_en' => 'nullable|string',
            'exam_rules_en' => 'nullable|string',
            'is_profession' => 'nullable|boolean'
        ];
        $totalScore = 0;

        foreach ($this->input('exams') as $exam) {
            $totalScore += $exam['score'];
        }

        if ($totalScore < 100) {
            $rules['total_score_validation'] = 'required_if:is_profession,0';
        }

        return $rules;
    }
}
