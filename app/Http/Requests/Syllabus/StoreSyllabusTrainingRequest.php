<?php

namespace App\Http\Requests\Syllabus;

use App\Models\Syllabus\SyllabusType;
use Illuminate\Foundation\Http\FormRequest;

class StoreSyllabusTrainingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $syllabusPointType = SyllabusType::POINTS;
        $syllabusPassType = SyllabusType::PASS;
        $rules = [
            'learn_year_id' => 'required|exists:learn_years,id',
            'name' => 'required|string|max:255',
            'academic_degree_id' => 'required|exists:academic_degrees,id',
            'code' => 'required|unique:syllabi',
            'credits' => 'required|numeric',
            'contact_hours' => 'required|integer',
            'lecture_hours' => 'required|integer',
            'total_hours' => 'required|integer',
            'lecturers' => 'required|array',
            'lecturers.*.id' => 'required',
            'lecturers.*.start_time' => 'required',
            'lecturers.*.end_time' => 'required',
            'lecturers.*.week_day' => 'required',
            'lecturers.*.lecturer_id' => 'required',
            'exams' => 'required|array',
            'exams.*' => 'required|array',
            'exams.*.id' => 'required|exists:assessment_components,id',
            'exams.*.parent_id' => 'nullable|integer',
            'exams.*.calculation_type' => 'nullable',
            'exams.*.score' => 'nullable|integer',
            'exams.*.min_score' => 'nullable|integer',
            'exams.*.description' => 'nullable|string',
            'exams.*.description_en' => 'nullable|string',
            'name_en' => 'nullable|string',
            'syllabus_type_id' => "required|in:{$syllabusPointType},{$syllabusPassType}",
        ];
        if ($this->input('syllabus_type_id') != 2) {
            $totalScore = 0;

        foreach ($this->input('exams') ?? [] as $exam) {
            $totalScore += $exam['score'];
        }

        if ($totalScore < 100) {
            $rules['total_score_validation'] = 'required';
        }
    }

        return $rules;
    }
}
