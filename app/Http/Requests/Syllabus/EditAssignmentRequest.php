<?php

namespace App\Http\Requests\Syllabus;

use Illuminate\Foundation\Http\FormRequest;

class EditAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'id' => 'required',
            'assessment_component_id' => 'required|exists:assessment_components,id',
            'parent_id' => 'integer',
            'syllabus_id' => 'required|integer',
            'score' => 'required|integer',
            'description' => 'required|string|nullable',
        ];
    }
}
