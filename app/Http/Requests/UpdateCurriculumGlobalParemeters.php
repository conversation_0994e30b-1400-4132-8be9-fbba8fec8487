<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCurriculumGlobalParemeters extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'start_date' => 'required|date_format:d/m/Y',
            'end_date' => 'required|date_format:d/m/Y',
            'lectures_count' => 'required|numeric',
            'student_flow_ids' => 'array',
            'student_flow_ids.*' => 'exists:students,learn_year_id',
            'allowed_amount_of_students' => 'nullable|numeric',
            'minimum_amount_of_students' => 'nullable|numeric',
            'registration_start_date' => 'nullable|date|date_format:d-m-Y H:i',
            'registration_end_date' => 'nullable|date|date_format:d-m-Y H:i',
        ];
    }
}
