<?php

namespace App\Http\Requests\Role;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        return [
            'title' => [
                'required',
                Rule::unique('roles', 'title')->ignore($this->route('role'))
            ],
            'permissions' => 'required|array',
            'permissions.*' => 'required|exists:permissions,id',
        ];
    }

    public function messages()
    {
        return (new StoreRequest)->messages();
    }
}
