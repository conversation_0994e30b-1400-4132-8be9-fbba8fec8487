<?php

namespace App\Http\Requests\Role;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => [
                'required',
                Rule::unique('roles', 'title')
            ],
            'permissions' => 'required|array',
            'permissions.*' => 'required|exists:permissions,id',
        ];
    }

    public function messages()
    {
        return [
            'title.required' => 'შეავსე როლის სახელის ველი',
            'permissions.required' => 'მიუთითე ერთი ნებართვა მაინც',
            'permissions.*.exists' => 'ასეთი ნებართვა არ არსებობს'
        ];
    }
}
