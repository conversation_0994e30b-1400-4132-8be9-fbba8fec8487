<?php

namespace App\Http\Requests\Elbook;

use App\Models\ElBook\ElBook;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'title' => [
                'required',
                'string',
                Rule::unique('el_books', 'title')->ignore($this->el_book)
            ],
            'author' => 'nullable|string',
            'subject' => 'nullable|string',
            'lecturer_id' => 'nullable|exists:lecturers,id',
            'topics' => 'sometimes|array',
            'topic.*' => 'sometimes|exists:topics,id',
            'published_date' => 'nullable|date|date_format:d-m-Y',
            'deleted_files' => 'sometimes|array',
            'deleted_files.*' => 'sometimes|exists:el_book_files,path',
            'files' => 'sometimes|array',
            'files.*' => 'sometimes|mimes:pdf'
        ];
        return $rules;
    }

    public function messages()
    {
        return (new StoreRequest())->messages();
    }
}
