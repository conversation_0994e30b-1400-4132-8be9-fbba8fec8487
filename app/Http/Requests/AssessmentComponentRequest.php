<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class AssessmentComponentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'type_id' => 'required|in:1,2,3',
            'name_ka' => 'required|string',
            'name_en' => 'required|string',
            'is_parent' => 'required|in:0,1',
        ];
    }

    public function messages()
    {
        return [
            'name_ka.required' => 'შეავსე სახელის ველი',
            'name_en.unique' => 'Name must be unique',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return response()->json($validator->errors(),422)->throwResponse();
    }
}
