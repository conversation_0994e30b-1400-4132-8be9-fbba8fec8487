<?php

namespace App\Http\Requests;

use App\Models\Reestry\Student\Student;
use App\Models\UserProgram;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FinanceSchedulerEditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'scheduler_id' => [
                'required',
                Rule::exists('finance_schedulers', 'id')
                ->where(function ($scheduler){
                    $userId = auth()->id();
                    $userProgramIds = UserProgram::whereUserId($userId)->pluck('program_id')->toArray();
                    $userIds = Student::whereIn('program_id', $userProgramIds)->pluck('user_id')->toArray();
                    $scheduler->whereIn('user_id', $userIds);
                })
            ],
            'status_id' => 'integer|in:1,2,3',
            'is_active' => 'required|boolean',
            'comment' => 'string',
        ];
    }
}
