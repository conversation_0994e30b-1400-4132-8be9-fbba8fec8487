<?php

namespace App\Http\Requests\Message;

use App\Rules\Maxsize;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Validator;

class SendMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'schools' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],

            'group_ids' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'group_ids.*' => 'exists:curriculum_student_group,student_group_id',


            'syllabus_ids' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'syllabus_ids.*' => 'required|exists:syllabi,id',


            'schools.*' => 'required|exists:schools,id',
            'student_groups' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'student_groups.*' => 'required|exists:student_groups,id',
            'programs' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'programs.*' => 'required|exists:programs,id',
            'flows' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                        'schools',
                        'student_groups',
                        'programs',
                        'users',
                        'syllabus_ids',
                        'group_ids',
                        'flows'
                    ])) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'flows.*' => 'required|exists:learn_years,id',
            'body' => 'required|string',
            'main_message_id' => 'sometimes|exists:messages,id',
            'attachments' => [
                'sometimes',
                'array',
                new Maxsize
            ],
            'attachments.*' => 'required|file|mimes:doc,docx,xls,xlsx,ppt,pptx,pdf,jpg,jpeg,png,zip',
            'users' => [
                'sometimes',
                'array',
                function ($attribute, $value, $fail) {
                    if (!$this->hasAny([
                            'schools',
                            'student_groups',
                            'programs',
                            'users',
                            'syllabus_ids',
                            'group_ids',
                            'flows'
                        ]) &&
                        !$this->has('main_message_id')
                    ) {
                        $fail('მიუთითე ადრესატი');
                    }
                }
            ],
            'users.*' => [
                'required',
                Rule::exists('users', 'id')->whereNot('id', Auth::id())
            ]
        ];
        if ($this->has('main_message_id')) {
            $rules['title'] = [
                'nullable',
            ];
        } else {
            $rules['title'] = [
                'required',
                'string',
                'max:255',
            ];
        }
        return $rules;
    }

    protected function failedValidation(Validator|\Illuminate\Contracts\Validation\Validator $validator)
    {
        $errors = $validator->errors();
        $customErrors = [];

        if ($errors->has('attachments.*')) {
            $customErrors['attachments'] = 'The attachments must be a file of type: doc, docx, xls, xlsx, ppt, pptx, pdf, jpg, jpeg, png, zip.';
        }

        if ($errors->has('title')) {
            $customErrors['title'] = 'There is an error with the title field.';
        }

        if ($errors->has('body')) {
            $customErrors['body'] = 'There is an error with the body field.';
        }

        if ($errors->has('users.*')) {
            $customErrors['users'] = 'There is an error with the users field.';
        }

        if (!empty($customErrors)) {
            throw new HttpResponseException(response()->json([
                'errors' => $customErrors
            ], 422));
        }
    }
}
