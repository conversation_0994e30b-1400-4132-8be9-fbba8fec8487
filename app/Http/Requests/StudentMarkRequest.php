<?php

namespace App\Http\Requests;

use App\Models\Assignment;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Validator;

class StudentMarkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'assignment_id' => 'required|exists:assignments,id',
            'student_id' => 'required|exists:students,id',
            'is_percent' => 'required|boolean',
            'last_checkout_status_id' => 'integer'
        ];

        if ($this->is_percent) {
            $rules['point'] = 'required|numeric|min:0|max:100';
        } else {
            $rules['point'] = 'required|numeric|min:0|max:' . Assignment::find($this->assignment_id)->score;
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'assignment_id.required' => 'The assignment ID is required.',
            'assignment_id.exists' => 'The selected assignment does not exist.',
            'student_id.required' => 'The student ID is required.',
            'student_id.exists' => 'The selected student does not exist.',
            'is_percent.required' => 'The is_percent field is required.',
            'is_percent.boolean' => 'The is_percent field must be a boolean value.',
            'point.required' => 'The point field is required.',
            'point.numeric' => 'The point field must be a number.',
            'point.min' => 'შეტანილი ქულა მინიმუმ :min უნდა იყოს!',
            'point.max' => 'შეტანილი ქულა არ უნდა აღემატებოდეს :max ქულას!',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator|\Illuminate\Contracts\Validation\Validator $validator)
    {
        $errorResponse = [
            'errors' => $validator->errors(),
        ];

        throw new HttpResponseException(response()->json($errorResponse, 422));
    }
}
