<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCurriculumRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'lectures' => 'required|array',
            'lectures.*.id' => 'required|exists:lectures,id',
            'lectures.*.start_time' => 'required|date_format:H:i',
            'lectures.*.end_time' => 'required|date_format:H:i',
            'lectures.*.week_day' => 'required|min:1|max:7',
            'lectures.*.lecture_date' => 'required|date_format:Y-m-d',
            'lectures.*.lecture_number' => 'required|integer',
            'lectures.*.auditorium_id' => 'required|exists:auditoria,id',
            'lectures.*.is_lecture' => 'required|boolean',
            'lectures.*.lecturer_id' => 'required|exists:lecturers,id',
            'lectures.*.payment_per_hour' => 'required|numeric',
            'lectures.*.lecturer_accounting_code' => 'required|string|max:255',
        ];
    }
}
