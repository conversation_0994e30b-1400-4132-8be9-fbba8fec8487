<?php

namespace App\Http\Controllers\API\V1\LecturerPage;

use App\Http\Controllers\Controller;
use App\Models\Reestry\Lecturer\Lecturer;
use Auth;
use Illuminate\Http\Request;
use Validator;

class LecturerProfileController extends Controller
{
    public function index()
    {
        $data = Lecturer::where('user_id',Auth::id())->firstOrFail();
        $mappedData = [
            'name' => $data->first_name,
            'surname' => $data->last_name,
            'email' => $data->email,
            'phone' => $data->phone,
            'birthday' => $data->date_of_birth,
            'address' => $data->address,
            'photo' => $data->photo,
        ];
        return response($mappedData);
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|max:255',
            'address' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = Lecturer::where('user_id', Auth::id())->firstOrFail();
        $user->phone = $request->input('phone');
        $user->address = $request->input('address');
        $user->save();

        return response($user);
    }
}
