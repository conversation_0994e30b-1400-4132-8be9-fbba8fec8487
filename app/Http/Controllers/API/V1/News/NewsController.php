<?php

namespace App\Http\Controllers\API\V1\News;

use App\Http\Controllers\Controller;
use App\Models\News\News;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Storage;

class NewsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:news.index'])->only(['index']);
        $this->middleware(['permission:news.store'])->only(['store']);
        //$this->middleware(['permission:news.show'])->only(['show']);
        $this->middleware(['permission:news.update'])->only(['update']);
        $this->middleware(['permission:news.destroy'])->only(['destroy']);
    }

    public function fetchNewsForMain()
    {
        $data = News::OrderBy('id', 'desc')->take(7)->get();
        return response($data, Response::HTTP_OK);
    }

    public function fetchNews()
    {
        $data = News::OrderBy('id', 'desc')->paginate(16);
        return response($data, Response::HTTP_OK);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $data['news'] = News::paginate(10);
        return response()->json($data, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string'
        ]);

        $news = new News;
        $news->title = $request->input('title');
        $news->description = $request->input('description');

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $path = $request->file('image')->storeAs('public/news/images', $filename);
            $news->image = $filename;
        }

        $news->save();

        return response($news, Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show(int $id)
    {
        $data = News::findOrFail($id);
        return response($data, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string'
        ]);

        $news = News::find($id);
        $news->title = $request->input('title');
        $news->description = $request->input('description');

        if ($request->hasFile('image')) {
            if ($news->image) {
                Storage::delete('public/news/images/' . $news->image);
            }

            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $path = $request->file('image')->storeAs('public/news/images', $filename);
            $news->image = $filename;
        }

        $news->save();

        return response($news, Response::HTTP_CREATED);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $news = News::find($id);
        if ($news->image) {
            Storage::delete('public/news/images/' . $news->image);
        }
        $news->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
