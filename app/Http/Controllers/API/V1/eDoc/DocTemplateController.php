<?php

namespace App\Http\Controllers\API\V1\eDoc;

use App\Http\Controllers\Controller;
use App\Models\eDoc\EdocTemplate;
use App\Models\User\UserType;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DocTemplateController extends Controller
{
    public function __construct()
    {
        //$this->middleware(['permission:edoc-templates.index'])->only(['index']);
        $this->middleware(['permission:edoc-templates.store'])->only(['store']);
        $this->middleware(['permission:edoc-templates.show'])->only(['show']);
        $this->middleware(['permission:edoc-templates.update'])->only(['update']);
        $this->middleware(['permission:edoc-templates.destroy'])->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $templates = EdocTemplate::OrderByDesc('id')->paginate(20);
        $userTypes = UserType::pluck('id', 'title');

        $data = [
            'templates' => $templates->toArray(),
            'userTypes' => $userTypes,
        ];

        return response()->json($data, Response::HTTP_OK);
    }

    public function listForStudents()
    {
        $templates = EdocTemplate::where('user_type_id', 3)->whereIsActive(true)->get();
        $data = [
            'templates' => $templates,
        ];

        return response()->json($data, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'index' => 'nullable|string|max:255',
            'text' => 'nullable|string',
            'automatic' => 'required|boolean',
            'lang' => 'required|boolean',
            'user_type_id' => 'required|exists:user_types,id',
            'signature' => 'required|integer',
            'description' => 'nullable|string'
        ]);

        $data = new EdocTemplate();
        $data->name = $request->input('name');
        $data->index = $request->input('index');
        $data->text = $request->input('text');
        $data->automatic = $request->input('automatic');
        $data->lang = $request->input('lang');
        $data->user_type_id = $request->input('user_type_id');
        $data->signature = $request->input('signature');
        $data->description = $request->input('description');
        $data->save();

        return response($data, Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return response([
            'templates' => EdocTemplate::findOrFail($id),
            'userTypes' => UserType::all()
        ], Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'index' => 'nullable|string|max:255',
            'text' => 'nullable|string',
            'automatic' => 'required|boolean',
            'lang' => 'required|boolean',
            'user_type_id' => 'required|exists:user_types,id',
            'signature' => 'required|integer',
            'description' => 'nullable|string'
        ]);

        $data = EdocTemplate::find($id);
        $data->name = $request->input('name');
        $data->index = $request->input('index');
        $data->text = $request->input('text');
        $data->automatic = $request->input('automatic');
        $data->lang = $request->input('lang');
        $data->user_type_id = $request->input('user_type_id');
        $data->signature = $request->input('signature');
        $data->description = $request->input('description');
        $data->save();

        return response($data, Response::HTTP_CREATED);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = EdocTemplate::find($id);
        $data->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
