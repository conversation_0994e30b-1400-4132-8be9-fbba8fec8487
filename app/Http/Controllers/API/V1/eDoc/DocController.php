<?php

namespace App\Http\Controllers\API\V1\eDoc;

use App\Filters\Doc\AdminInboxFilter;
use App\Filters\Doc\AdminSentFilter;
use App\Filters\eDocStudentFilter;
use App\Filters\Student\StudentFilter;
use App\Http\Controllers\API\V1\FinanceController;
use App\Http\Controllers\Controller;
use App\Http\Requests\FinanceSchedulerEditRequest;
use App\Http\Requests\FinanceScheduleRequest;
use App\Models\eDoc\eDoc;
use App\Models\eDoc\EdocTemplate;
use App\Models\Finance\FinanceScheduler;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use Auth;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Mpdf\Mpdf;
use Str;

class DocController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:edoc-inbox.index'])->only(['adminInbox']);
        $this->middleware(['permission:edoc-sent.index'])->only(['adminSent']);
        //$this->middleware(['permission:edoc.store'])->only(['store']);
        $this->middleware(['permission:edoc.update'])->only(['update']);
        $this->middleware(['permission:edoc.view'])->only(['view']);
        //$this->middleware(['permission:edoc.exportPDF'])->only(['exportPDF']);
    }

    public function adminInbox(AdminInboxFilter $filter, $id = null)
    {
        if ($id) {
            $data = eDoc::find($id);
            $data->delete();

            return response(null, Response::HTTP_NO_CONTENT);
        } else {
            $eDocs['edoc-inbox'] = eDoc::query()
                ->where('created', 0)
                ->filter($filter)
                ->adminProgramScope()
                ->with('user:id,name', 'template:id,name')
                ->orderByDesc('id')
                ->paginate(20);

            return response()->json($eDocs);
        }
    }

//    public function adminInbox($id = null)
//    {
//        if ($id) {
//            $data = eDoc::find($id);
//            $data->delete();
//
//            return response(null, Response::HTTP_NO_CONTENT);
//        } else {
//            $eDocs = eDoc::where('created', 0)
//                ->with('user', 'template')->orderByDesc('id')->paginate(20);
//            $formattedEDocs = $eDocs->map(function ($eDoc) {
//
//                return [
//                    'id' => $eDoc->id,
//                    'document_number' => $eDoc->document_number,
//                    'created_by' => $eDoc->created_by,
//                    'created' => $eDoc->created,
//                    'text' => $eDoc->text,
//                    'opened_at' => $eDoc->opened_at,
//                    'comment' => $eDoc->comment,
//                    'user' => optional($eDoc->user, function ($user) {
//                        return [
//                            'id' => $user->id,
//                            'name' => $user->name,
//                        ];
//                    }),
//                    'edoc_template' => optional($eDoc->template, function ($template) {
//                        return [
//                            'id' => $template->id,
//                            'name' => $template->name,
//                        ];
//                    }),
//                ];
//            });
//
//            return response()->json($formattedEDocs);
//        }
//    }

    public function adminSent(AdminSentFilter $filter, Request $request, $id = null)
    {
        if ($id) {
            $data = eDoc::find($id);
            $data->delete();

            return response(null, Response::HTTP_NO_CONTENT);
        } else {
            $user_id = auth()->user()->id ?? 1;
            $eDocs['edocs'] = eDoc::query()
                ->where('created_by', $user_id)
                ->filter($filter)
                ->adminProgramScope()
                ->with('user:id,name', 'template:id,name')
                ->orderByDesc('id')
                ->paginate(20);

            return response()->json($eDocs);
        }
    }

//    public function adminSent($id = null)
//    {
//        if ($id) {
//            $data = eDoc::find($id);
//            $data->delete();
//
//            return response(null, Response::HTTP_NO_CONTENT);
//        } else {
//            if (isset(auth()->user()->id)) {
//                $user_id = auth()->user()->id;
//            } else {
//                $user_id = 1;
//            }
//            $eDocs = eDoc::where('created_by', '=', $user_id)
//                ->with('user', 'template')->orderByDesc('id')->paginate(20);
//            $formattedEDocs = $eDocs->map(function ($eDoc) {
//
//                return [
//                    'id' => $eDoc->id,
//                    'document_number' => $eDoc->document_number,
//                    'created_by' => $eDoc->created_by,
//                    'created' => $eDoc->created,
//                    'text' => $eDoc->text,
//                    'opened_at' => $eDoc->opened_at,
//                    'comment' => $eDoc->comment,
//                    'user' => optional($eDoc->user, function ($user) {
//                        return [
//                            'id' => $user->id,
//                            'name' => $user->name,
//                        ];
//                    }),
//                    'edoc_template' => optional($eDoc->template, function ($template) {
//                        return [
//                            'id' => $template->id,
//                            'name' => $template->name,
//                        ];
//                    }),
//                ];
//            });
//
//            return response()->json($formattedEDocs);
//        }
//    }

    public function studentSchedulers(StudentFilter $filter, Request $request, $id = null): \Illuminate\Http\JsonResponse
    {
        $schedulers = FinanceScheduler::query()->with(['calendars',
            'student' => function ($q) use ($filter) {
                $q->filter($filter);
            },
            'student.program' => function ($q) {
                $q->select('id', 'name_ka', 'name_en');
            }
        ])->whereHas('student', function ($q) use ($request) {
            $q->where(function ($query) use ($request) {
                $query
                    ->whereRaw("CONCAT(students.name, ' ', students.surname) LIKE ?", ["%$request->keyword"])
                    ->orWhereRaw("CONCAT(students.surname, ' ', students.name) LIKE ?", ["%$request->keyword%"]);
            });
        })
            ->adminProgramScope();

        $schedulers = $id ? $schedulers->find($id) : $schedulers->OrderBy('id', 'DESC')->paginate(30);

        return response()->json([
            'finance-statement' => $schedulers ?? [],
        ], $schedulers ? 200 : 404);
    }

    public function updateStudentScheduler(FinanceSchedulerEditRequest $request): \Illuminate\Http\JsonResponse
    {
        $scheduler = FinanceScheduler::query()
            ->with('calendars')
            ->adminProgramScope()
            ->find($request->post('scheduler_id'));

        if ($scheduler) {
            $scheduler->update($request->validated());
            if ($request->exists('status_id') && $request->post('status_id') == 3) // rejected status
            {
                $scheduler->calendars()->delete();
            }
        }

        return response()->json([
            'data' => $scheduler ?? [],
        ], $scheduler ? 200 : 404);
    }

    public function openTime($id)
    {
        $eDoc = eDoc::findOrFail($id);
        $eDoc->opened_at = Carbon::now();
        $eDoc->save();
        return response($eDoc, Response::HTTP_OK);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'document_number' => 'nullable|string',
            'user_id' => 'sometimes|integer|exists:users,id',
            'edoc_template_id' => 'required|integer',
            'created_by' => 'nullable|integer',
            'created' => 'nullable|boolean',
            'text' => 'nullable|string',
            'opened_at' => 'nullable|date_format:d/m/Y H:i:s',
            'comment' => 'nullable|string',
            'stamp' => 'sometimes|boolean',
        ]);
        $stamp = $validatedData['stamp'] ?? false;
        //call finances controller for individual graphic
        if ($request->has('deadlines')) {
            $allNull = true;
            foreach ($request->deadlines as $deadline) {
                if ($deadline['start_date'] == null || $deadline['end_date'] == null || $deadline['amount'] == null) {
                    $allNull = false;
                    break;
                }
            }
            if ($allNull) {
                if ($validatedData['edoc_template_id'] == 1) {
                    $financeRequest = new FinanceScheduleRequest();
                    $financeRequest->deadlines = $request->deadlines;
                    $financeRequest->user_id = $request->user_id;
                    $finances = new FinanceController();
                    $finances->requestScheduler($financeRequest);
                }
            } else {
                $response = [
                    'message' => 'Validation failed.',
                ];
                return response()->json($response, 422);

            }
        }
        //

        if (isset($validatedData['user_id'])) {
            $user_id = $validatedData['user_id'];
        } else {
            $user_id = auth()->user()->id;
        }
        if (isset($validatedData['comment'])) {
            $comment = $validatedData['comment'];
        } else {
            $comment = null;
        }
        if (isset($validatedData['opened_at'])) {
            $opened_at = $validatedData['opened_at'];
        } else {
            $opened_at = null;
        }
        $template = EdocTemplate::findOrFail($validatedData['edoc_template_id']);

        $last_eDoc = eDoc::orderBy('id', 'desc')->first();
//        $last_eDoc = eDoc::where('edoc_template_id', $validatedData['edoc_template_id'])->orderBy('id', 'desc')->first(); //old

        $last_document_number = $last_eDoc ? $last_eDoc->document_number : $template->index;

        $document_number_parts = explode('-', $last_document_number);
        $document_number_prefix = $document_number_parts[0];

        $document_number_suffix_parts = explode('/', $document_number_parts[1] ?? '');
        $document_number_suffix = end($document_number_suffix_parts);

        $new_document_number_suffix = str_pad($document_number_suffix + 1, strlen($document_number_suffix), '0', STR_PAD_LEFT);

        $document_number_suffix_parts[count($document_number_suffix_parts) - 1] = $new_document_number_suffix;
        $new_document_number = implode('/', $document_number_suffix_parts);

        $document_number = $document_number_prefix . '-' . $new_document_number;

        $check = eDoc::where('user_id', auth()->user()->id)
            ->where('edoc_template_id', $validatedData['edoc_template_id'])
            ->whereDate('created_at', '=', Carbon::now()->toDateString())
            ->where('stamp', $stamp)
            ->first();
        if ($check) {
            return self::exportPDF($check->id);
        } else {
            $eDoc = new eDoc;
            $eDoc->document_number = $document_number;
            $eDoc->user_id = $user_id;
            $eDoc->edoc_template_id = $validatedData['edoc_template_id'];
            if ($template->automatic == 1) {
                $eDoc->created_by = auth()->user()->id;
                $eDoc->created = 1;
                $eDoc->status = 2;
            } else {
                $eDoc->created_by = null;
                $eDoc->created = 0;
                $eDoc->status = 1;
            }
            $eDoc->text = $template->text;
            $eDoc->opened_at = $opened_at;
            $eDoc->comment = $comment;
            $eDoc->stamp = $stamp;
            $eDoc->save();
        }
        $user = \App\Models\User\User::where('id', auth()->user()->id)->first();
        if ($user->user_type_id == 1) {
            return response($eDoc, Response::HTTP_CREATED);
        } else {
            if ($template->automatic == 1) {
                return self::exportPDF($eDoc->id);
            } else {
                return response($eDoc, Response::HTTP_CREATED);
            }
        }
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'text' => 'nullable|string',
            'comment' => 'nullable|string',
            'status' => 'required|integer',
            'stamp' => 'required|boolean'
        ]);
        $eDoc = eDoc::findOrFail($id);
        $eDoc->created_by = auth()->user()->id;
        $eDoc->created = 1;
        $eDoc->text = $validatedData['text'];
        $eDoc->opened_at = Carbon::now();
        $eDoc->comment = $validatedData['comment'];
        $eDoc->status = $validatedData['status'];
        $eDoc->stamp = $validatedData['stamp'];
        $eDoc->save();

        return response($eDoc, Response::HTTP_OK);
    }

    public function view($id)
    {
        $eDoc = eDoc::with('user', 'template')->findOrFail($id);

        $formattedEDoc = [
            'id' => $eDoc->id,
            'document_number' => $eDoc->document_number,
            'created_by' => $eDoc->created_by,
            'created' => $eDoc->created,
            'text' => $eDoc->text,
            'opened_at' => $eDoc->opened_at,
            'comment' => $eDoc->comment,
            'status' => $eDoc->status,
            'stamp' => $eDoc->stamp,
            'user' => optional($eDoc->user, function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                ];
            }),
            'edoc_template' => optional($eDoc->template, function ($template) {
                return [
                    'id' => $template->id,
                    'name' => $template->name,
                ];
            }),
        ];

        return response()->json($formattedEDoc);
    }

    public function delete($id)
    {
        $data = eDoc::find($id);
        $data->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportPDF($id)
    {
        $eDoc = eDoc::findOrFail($id);
        $eDocArray = $eDoc->toArray();
        $student = Student::with('program', 'school')->whereUserId($eDoc['user_id'])->first();
        $histories = StudentSyllabusHistory::studentHistories($student->id);
        //start transcript code, check
        if ($eDoc->template->file_name == 'transcript' or $eDoc->template->file_name == 'transcript_eng') {
            $mpdf = new Mpdf([
                'format' => 'A4',
                'margin_left' => 10,
                'margin_right' => 10,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_header' => 9,
                'margin_footer' => 9,
            ]);
            if ($eDoc->template->lang == 1) { //check document language
                $footer = '<div class="footer" style="width: 100%;">
        <div class="col-footer" style="float: left; width:20%">
            <p>www.gipa.ge</p>
            <p><EMAIL></p>
        </div>
        <div class="col-footer" style="float:left; width: 50%;">
            <p>&nbsp;</p>
             <p style="margin-left: 60%;">{PAGENO} / {nb}</p>
            <p>&nbsp;</p>
        </div>
        <div class="col-footer" style="text-align: right; width: 30%">
           <p>Brosset Str. 2; 0108, Tbilisi,</p>
            <p>Georgia</p>
            <p>Identification Number:</p>
            <p>204429341</p>
        </div>
    </div>';
            } else { //its for georgian version
                $footer = '<div class="footer" style="width: 100%;">
        <div class="col-footer" style="float: left; width:20%">
            <p>www.gipa.ge</p>
            <p><EMAIL></p>
        </div>
        <div class="col-footer" style="float:left; width: 50%;">
            <p>&nbsp;</p>
             <p style="margin-left: 60%;">{PAGENO} / {nb}</p>
            <p>&nbsp;</p>
        </div>
        <div class="col-footer" style="text-align: right; width: 30%">
            <p>ბროსეს ქ. 2; 0108, თბილისი,</p>
            <p>საქართველო</p>
            <p>საიდენტიფიკაციო კოდი:</p>
            <p>204429341</p>
        </div>
    </div>';
            }
            $mpdf->SetHTMLFooter($footer);
            $cssFilePath = public_path('assets/transcript_styles.css');
            $cssContent = file_get_contents($cssFilePath);
            $html = '<style>' . $cssContent . '</style>';
            $html .= view('edoc.' . $eDoc->template->file_name, compact('eDocArray', 'student', 'histories'))->render();
            $mpdf->WriteHTML($html);
            $filename = 'GIPA-eDoc-' . $eDoc->id . '-' . Str::random(3) . '.pdf';

            $pdfFilePath = storage_path('app/public/' . $filename);
            $mpdf->Output($pdfFilePath, 'F'); // 'F' parameter for saving to a file

            return response()->json(['message' => 'PDF saved', 'filename' => $filename]);
        } //end transcript code
        $pdf = PDF::loadView('edoc.' . $eDoc->template->file_name, ['eDoc' => $eDocArray, 'student' => $student]);
        $filename = 'GIPA-eDoc-' . $eDoc->id . '-' . Str::random(3) . '.pdf';
        $pdf->save($filename, 'public');

        return response()->json(['message' => 'PDF saved successfully', 'filename' => $filename]);
    }

    public function student(eDocStudentFilter $filter)
    {
        return eDoc::filter($filter)->with('template')->where('user_id', Auth::id())->orderByDesc('id')->paginate(20);
    }
}
