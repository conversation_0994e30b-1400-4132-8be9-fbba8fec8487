<?php

namespace App\Http\Controllers\API\V1\Imports;

use App\Http\Controllers\Controller;
use App\Imports\AdministrationImport;
use App\Imports\BachelorsTempImport;
use App\Imports\LecturersImport;
use App\Imports\StudentsImport;
use App\Imports\TempUserImport;
use App\Imports\UsersImport;
use App\Models\User\UserType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
set_time_limit(300);
class ExcelImportController extends Controller
{
    use Importable;

    public function importBachelors(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|mimes:xlsx',
            'program_id' => 'required|exists:programs,id',
            'flow_id' => 'required|exists:learn_years,id'
        ]);
        $program_id = $request->program_id;
        $flow_id = $request->flow_id;
        $file = $request->file('file');
        $import = new BachelorsTempImport($program_id, $flow_id);

        try {
            Excel::import($import, $file);

            $skippedRows = $import->getSkippedRows();
            $importedRowCount = $import->getImportedRowCount();

            return new JsonResponse([
                'success' => true,
                'message' => 'ფაილი წარმატებით აიტვირთა!',
                'imported' => $importedRowCount,
                'exist_users_not_imported' => $skippedRows
            ]);
        } catch (ValidationException $e) {
            $failures = $e->failures();

            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                'errors' => $failures
            ]);
        }
    }

    public function importStudents(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|mimes:xlsx',
            'program_id' => 'required|exists:programs,id',
            'flow_id' => 'required|exists:learn_years,id'
        ]);
        $program_id = $request->program_id;
        $flow_id = $request->flow_id;
        $school_id = $request->school_id;
        $file = $request->file('file');
        $importUsers = new UsersImport(UserType::STUDENT);
        try {
            Excel::import($importUsers, $file);

            $skippedUserRows = $importUsers->getSkippedRows();
            $importedUserRowCount = $importUsers->getImportedRowCount();
        } catch (ValidationException $e) {
            $failures = $e->failures();

            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                'errors' => $failures
            ]);
        }
        if (!empty($skippedUserRows) && $importedUserRowCount = 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთულ ფაილში არსებობენ ისეთი მომხმარებლები რომლებიც უკვე რეგისტრირებულები არიან პორტალზე.',
                'imported' => $importedUserRowCount,
                'exist_users_not_imported' => $skippedUserRows,
            ]);
        } else {

            $import = new StudentsImport($program_id, $flow_id, $school_id);
            try {
                Excel::import($import, $file);
                $skippedLecturerRows = $import->getSkippedRows();
                $importedLecturerRowCount = $import->getImportedRowCount();

                return new JsonResponse([
                    'success' => true,
                    'message' => 'ფაილი წარმატებით აიტვირთა!',
                    'imported_users' => $importedUserRowCount,
                    'imported' => $importedLecturerRowCount,
                    'exist_users_not_imported' => $skippedLecturerRows
                ]);

            } catch (ValidationException $e) {
                $failures = $e->failures();
                return new JsonResponse([
                    'success' => false,
                    'message' => 'ატვირთულ ფაილში არსებობენ ისეთი მომხმარებლები რომლებიც უკვე რეგისტრირებულები არიან პორტალზე.',
                    'errors' => $failures
                ]);
            }
        }
    }

    public function importLecturers(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|mimes:xlsx'
        ]);

        $file = $request->file('file');
        $importUsers = new UsersImport(UserType::LECTURER);

        try {
            Excel::import($importUsers, $file);

            $skippedUserRows = $importUsers->getSkippedRows();
            $importedUserRowCount = $importUsers->getImportedRowCount();
        } catch (ValidationException $e) {
            $failures = $e->failures();

            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                'errors' => $failures
            ]);
        }

        if (!empty($skippedUserRows) && $importedUserRowCount = 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთულ ფაილში არსებობენ ისეთი მომხმარებლები რომლებიც უკვე რეგისტრირებულები არიან პორტალზე.',
                'imported' => $importedUserRowCount,
                'exist_users_not_imported' => $skippedUserRows,
            ]);
        } else {

            $import = new LecturersImport();

            try {
                Excel::import($import, $file);

                $skippedLecturerRows = $import->getSkippedRows();
                $importedLecturerRowCount = $import->getImportedRowCount();

                return new JsonResponse([
                    'success' => true,
                    'message' => 'ფაილი წარმატებით აიტვირთა!',
                    'imported_users' => $importedUserRowCount,
                    'imported' => $importedLecturerRowCount,
                    'exist_users_not_imported' => $skippedLecturerRows
                ]);

            } catch (ValidationException $e) {
                $failures = $e->failures();
                return new JsonResponse([
                    'success' => false,
                    'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                    'errors' => $failures
                ]);
            }
        }
    }

    public function importAdministration(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|mimes:xlsx'
        ]);

        $file = $request->file('file');
        $importUsers = new UsersImport(UserType::ADMINISTRATION);

        try {
            Excel::import($importUsers, $file);

            $skippedUserRows = $importUsers->getSkippedRows();
            $importedUserRowCount = $importUsers->getImportedRowCount();
        } catch (ValidationException $e) {
            $failures = $e->failures();

            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                'errors' => $failures
            ]);
        }

        if (!empty($skippedUserRows) && $importedUserRowCount = 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთულ ფაილში არსებობენ ისეთი მომხმარებლები რომლებიც უკვე რეგისტრირებულები არიან პორტალზე.',
                'imported' => $importedUserRowCount,
                'exist_users_not_imported' => $skippedUserRows,
            ]);
        } else {

            $import = new AdministrationImport();

            try {
                Excel::import($import, $file);

                $skippedLecturerRows = $import->getSkippedRows();
                $importedLecturerRowCount = $import->getImportedRowCount();

                return new JsonResponse([
                    'success' => true,
                    'message' => 'ფაილი წარმატებით აიტვირთა!',
                    'imported_users' => $importedUserRowCount,
                    'imported' => $importedLecturerRowCount,
                    'exist_users_not_imported' => $skippedLecturerRows
                ]);

            } catch (ValidationException $e) {
                $failures = $e->failures();
                return new JsonResponse([
                    'success' => false,
                    'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                    'errors' => $failures
                ]);
            }
        }
    }

    public function importTempUsers(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|mimes:xlsx'
        ]);
        $file = $request->file('file');
        $import = new TempUserImport();

        try {
            Excel::import($import, $file);

            $skippedRows = $import->getSkippedRows();
            $importedRowCount = $import->getImportedRowCount();

            return new JsonResponse([
                'success' => true,
                'message' => 'ფაილი წარმატებით აიტვირთა!',
                'imported' => $importedRowCount,
                'exist_users_not_imported' => $skippedRows
            ]);
        } catch (ValidationException $e) {
            $failures = $e->failures();

            return new JsonResponse([
                'success' => false,
                'message' => 'ატვირთული ექსელის ფაილი დაზიანებულია ან შეიცავს დაუშვებელ სიმბოლოებს.',
                'errors' => $failures
            ]);
        }
    }


}
