<?php

namespace App\Http\Controllers\API\V1\Survey;

use App\Exports\SurveyExport;
use App\Http\Controllers\Controller;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Survey\Survey;
use App\Models\Survey\SurveyActivation;
use App\Models\Survey\SurveyAnswer;
use App\Models\Survey\SurveyQuestion;
use App\Models\Survey\SurveyQuestionType;
use App\Models\SyllabusStudentGuest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Maatwebsite\Excel\Facades\Excel;
use Pdf;
use PhpOffice\PhpSpreadsheet\Exception;

class SurveyController extends Controller
{
    public function __construct()
    {
//        $this->middleware(['permission:survey.index'])->only(['index']);
//        $this->middleware(['permission:survey.store'])->only(['store']);
//        $this->middleware(['permission:survey.show'])->only(['show']);
//        $this->middleware(['permission:survey.edit'])->only(['edit']);
//        $this->middleware(['permission:survey.update'])->only(['update']);
//        $this->middleware(['permission:survey.destroy'])->only(['destroy']);
//        $this->middleware(['permission:survey.analyse'])->only(['analysis']);
    }

    public function index(): JsonResponse
    {
        $surveys = Survey::paginate(20);
        $questionTypes = SurveyQuestionType::all();
        $data = [
            'surveys' => $surveys->toArray(),
            'questionTypes' => $questionTypes,
        ];
        return response()->json($data);
    }

    public function store(Request $request): JsonResponse
    {
        $validatedSurveyData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|integer',
        ]);

        $survey = Survey::create($validatedSurveyData);

        $validatedQuestionData = $request->validate([
            'questions.*.name' => 'required|string|max:255',
            'questions.*.survey_question_type_id' => 'required|exists:survey_question_types,id',
            'questions.*.question_required' => 'nullable|boolean',
            'questions.*.comment_required' => 'nullable|boolean',
        ]);

        foreach ($validatedQuestionData['questions'] as $questionData) {
            $surveyQuestion = new SurveyQuestion($questionData);
            $surveyQuestion->survey_id = $survey->id;
            $surveyQuestion->save();
        }

        return response()->json($survey, 201);
    }

    public function show($survey): JsonResponse
    {
        $survey = Survey::with('questions')->findOrFail($survey);
        $questionTypes = SurveyQuestionType::all();

        return response()->json([
            'survey' => $survey,
            'questionTypes' => $questionTypes,
        ]);
    }

    public function edit(Survey $survey): JsonResponse
    {
        $survey = Survey::with('questions')->find($survey->id);
        $questionTypes = SurveyQuestionType::all();

        return response()->json([
            'survey' => $survey,
            'questionTypes' => $questionTypes,
        ]);
    }

    public function update(Request $request, Survey $survey): JsonResponse
    {
        $validatedSurveyData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|integer',
        ]);

        $survey->update($validatedSurveyData);

        $validatedQuestionData = $request->validate([
            'questions.*.id' => 'nullable|exists:survey_questions,id',
            'questions.*.name' => 'required|string|max:255',
            'questions.*.survey_question_type_id' => 'required|exists:survey_question_types,id',
            'questions.*.question_required' => 'nullable|boolean',
            'questions.*.comment_required' => 'nullable|boolean',
            'questions.*._delete' => 'nullable|boolean',
        ]);
        if ($request->input('deleted_questions')) {
            foreach ($request->input('deleted_questions') as $deletedQuestion) {
                if ($deletedQuestion['_delete']) {
                    SurveyQuestion::where('id', $deletedQuestion['id'])->delete();
                }
            }
        }
        foreach ($validatedQuestionData['questions'] as $questionData) {
            if (isset($questionData['id'])) {
                $surveyQuestion = SurveyQuestion::findOrFail($questionData['id']);
                $surveyQuestion->update($questionData);
            } else {
                $surveyQuestion = new SurveyQuestion($questionData);
                $surveyQuestion->survey_id = $survey->id;
                $surveyQuestion->save();
            }
        }

        return response()->json($survey);
    }

    public function destroy(Survey $survey): JsonResponse
    {
        $surveyQuestions = SurveyQuestion::where('survey_id', $survey->id)->get();
        foreach ($surveyQuestions as $question) {
            $question->delete();
        }

        $survey->delete();
        return response()->json(null, 204);
    }

    public function insert(Request $request)
    {
        $validatedData = $request->validate([
            'survey_activation_id' => 'required|exists:survey_activations,id',
            'questions' => 'required|array',
            'questions.*.survey_question_id' => 'required|exists:survey_questions,id',
            'questions.*.answer_int' => 'nullable|integer',
            'questions.*.answer_string' => 'nullable|string',
            'questions.*.comment' => 'nullable|string',
            'semester_id' => 'required|exists:flows,id'
        ]);

        $surveyActivation = SurveyActivation::find($validatedData['survey_activation_id']);
        $surveyActivation->answered = 1;
        $surveyActivation->learn_semester = $validatedData['semester_id'];
        $surveyActivation->save();

        foreach ($validatedData['questions'] as $question) {
            SurveyAnswer::query()->updateOrCreate([
                'survey_activation_id' => $validatedData['survey_activation_id'],
                'survey_question_id' => $question['survey_question_id'],
            ],[
                'answer_int' => $question['answer_int'],
                'answer_string' => $question['answer_string'],
                'comment' => $question['comment'],
            ]);
        }

        return response()->json(['message' => 'გმადლობთ შეფასებისთვის!']);
    }


    /**
     * @throws Exception
     *
     * survey_id
     * scool ids
     *
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function analysis(Request $request)
    {
        $request = $request->validate([
            'survey_id' => 'required|exists:surveys,id',
            'syllabus_id' => 'required|exists:syllabi,id',
            'lecturer_id' => 'required|exists:lecturers,id',
            'response_type' => 'integer', // 1: excel 2: pdf
        ]);

        $surveyActivation = SurveyActivation::query()
            ->with(['lecturer', 'syllabus', 'user.student'])
            ->where('survey_id', $request['survey_id'])
            ->where('syllabus_id', $request['syllabus_id'])
            ->where('lecturer_id', $request['lecturer_id'])
            ->get();

        $survey = Survey::query()
            ->with(['questions', 'questions.answer' => function ($answer) use ($surveyActivation) {
                $answer->whereIn('survey_activation_id',
                    $surveyActivation->pluck('id')->whereNotNull()
                );
            }])
            ->find($request['survey_id']);

        if (!$surveyActivation) {
            return response()->json(['message' => 'არ მოიძებნა'], 404);
        }

        $firstSurveyActivation = $surveyActivation->first();
        $maleCount = $surveyActivation->where('user.student.sex', 1)->whereNotNull('user.student.sex')->count() ?? 0;
        $femaleCount = $surveyActivation->where('user.student.sex', 0)->whereNotNull('user.student.sex')->count() ?? 0;
        $totalUserCount = $maleCount + $femaleCount;

        $response = [
            'lecturerName' => $firstSurveyActivation?->lecturer?->first_name . ' ' . $firstSurveyActivation?->lecturer?->last_name ?? null,
            'syllabusName' => $firstSurveyActivation->syllabus->name ?? null,
            'learnYear' => Flow::query()->find($firstSurveyActivation?->learn_semester)->name ?? null,
            'school' => $firstSurveyActivation->syllabus->learnYear->program->name_ka ?? null,
            'program' => $firstSurveyActivation->syllabus->learnYear->program->school->name_ka ?? null,
            'userStatistic' => [
                'total_user_count' => $totalUserCount,
                'male' => $totalUserCount > 0 ? round((($maleCount / $totalUserCount) * 100), 2) : 0,
                'female' => $totalUserCount > 0 ? round((($femaleCount / $totalUserCount) * 100), 2) : 0,
            ],
            'survey' => $survey,
        ];

        if (($request['response_type'] ?? null) == 1) {
            return Excel::download(new SurveyExport($survey, $response), 'survey-export-' . now() . '.xlsx');
        } elseif (($request['response_type'] ?? null) == 2) {
            $pdf = PDF::loadView('survey.analytics', $response);

            return $pdf->download('example.pdf');
        }

        return view('survey.analytics')->with($response);
    }

    public function setActivation(Request $request): JsonResponse
    {
        $request = $request->validate([
            'survey_id' => 'required|exists:surveys,id',
            'syllabus_id' => 'required|exists:syllabi,id',
            'status' => 'required|boolean',
            'school_ids' => 'array',
            'school_ids.*' => 'exists:schools,id'
        ]);

        if ($request['school_ids'] ?? null) {
            $programUserIds = Program::query()
                ->with('learnYears.students')
                ->whereIn('school_id', $request['school_ids'])
                ->get()
                ->pluck('learnYears.*.students.*.user_id')
                ->flatten()
                ->map(function ($userId) use ($request) {
                    return [
                        'user_id' => $userId,
                        'survey_id' => $request['survey_id'],
                        'status' => $request['status']
                    ];
                });
            SurveyActivation::query()->insert($programUserIds->toArray());
            return response()->json([
                'status' => 200,
                'success' => true
            ]);
        }

        $surveyId = $request['survey_id'];
        $syllabusId = $request['syllabus_id'];
        $statusId = $request['status'];

        $surveyActivation = SurveyActivation::query()
            ->where('syllabus_id', $syllabusId)
            ->get();

        if ($surveyActivation->isNotEmpty())
        {
            SurveyActivation::where('syllabus_id', $syllabusId)->update(['status' => $statusId]);
            return response()->json([
                'status' => 200,
                'success' => true,
            ]);
        }

        $curriculaIds = Curriculum::query()
            ->where('syllabus_id', $syllabusId)
            ->pluck('id');

        $curriculumLecturerIds = CurriculumLecture::query()
            ->whereIn('curriculum_id', $curriculaIds)
            ->pluck('id');

        $curriculumLecturerTimes = CurriculumLectureTime::query()
            ->select(['id', 'lecturer_id','curriculum_lecture_id'])
            ->with('groups')
            ->whereIn('curriculum_lecture_id', $curriculumLecturerIds)
            ->get();

        $groupIds = $curriculumLecturerTimes->pluck('groups.*.student_group_id')->flatten()->all();

        $students = Student::query()
            ->select(['id', 'group_id','name','surname'])
            ->orderByDesc('surname')
            ->whereIn('group_id', $groupIds)
            ->whereIn('status_id', [1,9])
            ->get('id');

        $guests = SyllabusStudentGuest::query()
            ->select(['id', 'syllabus_id', 'student_group_id', 'student_id'])
            ->where('syllabus_id', $syllabusId)
            ->whereIn('student_group_id', $groupIds)
            ->get('student_id');

        $curriculumLecturerTimes = $curriculumLecturerTimes->groupBy('lecturer_id');

        foreach ($curriculumLecturerTimes as $lecturerId => $times) {
            $insertData = [];
            $userIds = [];
            foreach ($times as $item) {
                $studentIdsHistory = StudentSyllabusHistory::query()
                    ->select(['id', 'syllabus_id', 'student_id'])
                    ->where('syllabus_id', $syllabusId)
                    ->pluck('student_id');

                if ($item->groups->isEmpty()) {
                    $studentIds = $studentIdsHistory;

                } else {
                    $groupIds = $item->groups->pluck('student_group_id');
                    $studentIdsFromStudents = $students->whereIn('group_id', $groupIds)->pluck('id');
                    $studentIdsFromGuest = $guests->whereIn('student_group_id', $groupIds)->pluck('student_id');
                    $studentIds = collect([...$studentIdsFromStudents, ...$studentIdsFromGuest])->unique()->values();
                    $studentIds = $studentIds->intersect($studentIdsHistory);
                }

                $newUserIds = Student::query()
                    ->select(['id', 'user_id'])
                    ->whereIn('id', $studentIds)
                    ->pluck('user_id');

                $userIds = collect([...$userIds, ...$newUserIds])->unique()->values();

            }
            foreach ($userIds as $userId) {
                $insertData[] = [
                    'user_id' => $userId,
                    'survey_id' => $surveyId,
                    'syllabus_id' => $syllabusId,
                    'lecturer_id' => $lecturerId,
                    'status' => $statusId,
                    'learn_semester' => Setting::where('key', 'current_semester')->pluck('value')->first(),
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }
            SurveyActivation::query()->insert($insertData);
        }

        return response()->json([
            'status' => 200,
            'success' => true
        ]);
    }

    public function surveysForSubjects(Request $request)
    {
        $request = $request->validate([
            'syllabus_id' => 'required|exists:syllabi,id'
        ]);

        $surveys = Survey::all();

        $surveyActivation = SurveyActivation::query()
            ->select(['id', 'survey_id', 'status', 'answered'])
            ->with('survey')
            ->where('syllabus_id', $request['syllabus_id'])
            ->get();
        $surveys = $surveys->map(function ($survey) use ($surveyActivation) {
            $status = $surveyActivation->where('survey_id', $survey->id)->first()->status ?? null;
            return array_merge($survey->toArray(), ['status' => $status ?? 0]);
        });

        return response()->json([
            'surveys' => $surveys
        ]);
    }

}
