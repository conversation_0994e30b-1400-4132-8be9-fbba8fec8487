<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Filters\ProgramFilter;
use App\Filters\Student\ApplicantsFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterForms\Proffession\StoreRequest;
use App\Http\Resources\ProffesionResource;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\Proffesion\ProffesionRegisterEducation;
use App\Models\RegisterForms\ProffesionRegister;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use function request;

class ProffesionRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:hse.index'])->only(['index']);
        $this->middleware(['permission:hse.destroy'])->only(['destroy']);
    }

    const fileNames = [
        'photo',
        'identity_number_copy',
    ];

    public function index(ApplicantsFilter $filter)
    {
        $programFilter = new ProgramFilter();

        $paginator = ProffesionRegister::filter($filter)
            ->with(['program', 'registerFormInfo', 'educations'])
            ->paginate(30);

        $data = $paginator->toArray();

        $additionalData = [
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'programs' => Program::filter($programFilter)->where('academic_degree_id', '=', 4)
                ->select(['id', 'name_ka'])
                ->get(),
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }

    public function store(StoreRequest $request, ImageService $imageService): ProffesionResource
    {
        $validated = $request->validated();
        $identityNumber = $request->identity_number;
        //$validated['program_id'] = 1;
        foreach (self::fileNames as $fileName) {
            $validated[$fileName] = $imageService->upload(
                $request->{$fileName},
                'proffesion-register/'
                . $identityNumber .
                '/' . $fileName
            );
        }
        $proffesionRegister = ProffesionRegister::create($validated);
        $proffesionRegister->registerFormInfo()->create($request->validated());
        if ($request->has('educations')) {
            foreach ($request->educations as $education) {
                ProffesionRegisterEducation::create([
                    'proffesion_register_id' => $proffesionRegister->id,
                    'faculty' => $education['faculty'],
                    'academic_degree_id' => $education['academic_degree_id'],
                    'start_date' => $education['start_date'],
                    'end_date' => $education['end_date'],
                    'university' => $education['university']
                ]);
            }
        }
        return new ProffesionResource($proffesionRegister->load([
            'program',
            'registerFormInfo',
            'educations'
        ]));
    }

    public function show($proffesionRegister)
    {
//        return new ProffesionResource(ProffesionRegister
//            ::findOrFail($proffesionRegister)
//            ->load([
//                'program',
//                'registerFormInfo'
//            ]));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\RegisterForms\ProffesionRegister $proffesionRegister
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProffesionRegister $proffesionRegister)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\RegisterForms\ProffesionRegister $profesionRegister
     * @return \Illuminate\Http\Response
     */
    public function destroy(int $professionRegister)
    {
        $professionRegister = ProfessionRegister::findOrFail($professionRegister);

        if ($professionRegister->registerFormInfo) {
            $professionRegister->registerFormInfo->delete();
        }

        $professionRegister->educations->each(function ($education) {
            $education->delete();
        });

        $professionRegister->delete();
        return response(null, ResponseAlias::HTTP_NO_CONTENT);
    }

}
