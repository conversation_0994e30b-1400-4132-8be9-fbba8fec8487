<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Exports\CompareSyllabiHoursExport;
use App\Exports\TccExport;
use App\Filters\ProgramFilter;
use App\Filters\Student\ApplicantsFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterForms\Training\StoreRequest;
use App\Http\Resources\TrainingResource;
use App\Mail\TrainingRegisters\SuccessRegistrationMail;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\Training\TrainingCertificate;
use App\Models\RegisterForms\Training\TrainingRegisterInfo;
use App\Models\RegisterForms\TrainingRegister;
use App\Services\ImageService;
use App\Services\TrainingRegisterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class TrainingRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:tcc.index'])->only(['index']);
        $this->middleware(['permission:tcc.destroy'])->only(['destroy']);
    }

    const fileNames = [
        'photo',
        'identity_number_copy',
        'cv'
    ];

    public function index(ApplicantsFilter $filter)
    {
        $programFilter = new ProgramFilter();

        $paginator = TrainingRegister::filter($filter)
            ->with([
                'registerFormInfo',
                'certificates',
                'englishLevel',
                'program',
                'works',
                'address',
                'programs',
                'educations',
                'works'
            ])
            ->paginate(30);

        $data = $paginator->toArray();

        $additionalData = [
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'programs' => Program::filter($programFilter)->where('academic_degree_id', '=', 5)
                ->select(['id', 'name_ka'])
                ->get(),
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }


    public function store(StoreRequest            $request,
                          ImageService            $imageService,
                          TrainingRegisterService $trainingRegisterService)
    {

        $validated = $request->validated();
        $identityNumber = $request->identity_number;

        foreach (self::fileNames as $fileName) {
            $validated[$fileName] = $imageService->upload(
                $request->{$fileName},
                'training-register/' . $identityNumber . '/' . $fileName
            );
        }

        $validated['flow_id'] = LearnYear::query()
            ->where('active', true)
            ->where ('program_id', $validated['program_id'])
            ->first()
            ?->id
        ;

        $trainingRegister = TrainingRegister::create($validated);
        $trainingRegister->registerFormInfo()->create($request->validated());
        if ($request->has('infos')) {
            foreach ($request->infos as $info) {
                TrainingRegisterInfo::create([
                    'training_register_id' => $trainingRegister->id,
                    'title' => $info['title']
                ]);
            }
        }
        if ($request->has('certificates')) {
            foreach ($request->certificates as $certificate) {
                TrainingCertificate::create([
                    'training_register_id' => $trainingRegister->id,
                    'title' => $imageService->upload(
                        $certificate,
                        '/training-register/' .
                        $identityNumber .
                        '/certificates')
                ]);
            }
        }
        $trainingRegisterId = $trainingRegister->id;
        $trainingRegisterService->storeAddresses(
            $trainingRegister->id,
            $request->only(['city', 'street', 'city_of_birth'])
        );

        foreach ($request->programs as $program) {
            $trainingRegisterService->storePrograms(
                $trainingRegisterId,
                $program
            );
        }

        foreach ($request->educations as $education) {
            $trainingRegisterService->storeEducations(
                $trainingRegisterId,
                $education
            );
        }
        if ($request->has('works')) {
            foreach ($request->works as $work) {
                $trainingRegisterService->storeWorks(
                    $trainingRegisterId,
                    $work
                );
            }
        }

        Mail::to($request->email)->send(new SuccessRegistrationMail);

        return new TrainingResource($trainingRegister
            ->load([
                'registerFormInfo',
                'certificates',
                'englishLevel',
                'program',
                'works',
                'address',
                'programs',
                'educations',
                'works',
                'infos'
            ]));
    }


    public function show($trainingRegister)
    {
//        return new TrainingResource(TrainingRegister::findOrFail($trainingRegister)
//            ->load([
//                'registerFormInfo',
//                'certificates',
//                'englishLevel',
//                'program',
//                'works',
//                'address',
//                'programs',
//                'educations',
//                'works'
//            ]));
    }


    public function update(Request $request, TrainingRegister $trainingRegister)
    {
        //
    }

    public function destroy(int $trainingRegister)
    {
        $trainingRegister = TrainingRegister::findOrFail($trainingRegister);

        if ($trainingRegister->registerFormInfo) {
            $trainingRegister->registerFormInfo->delete();
        }

        $trainingRegister->certificates->each(function ($certificate) {
            $certificate->delete();
        });

        $trainingRegister->programs->each(function ($program) {
            $program->delete();
        });

        $trainingRegister->educations->each(function ($education) {
            $education->delete();
        });

        $trainingRegister->infos->each(function ($info) {
            $info->delete();
        });

        $trainingRegister->works->each(function ($work) {
            $work->delete();
        });

        $trainingRegister->delete();
        return response(null, ResponseAlias::HTTP_NO_CONTENT);
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function tccExport(ApplicantsFilter $filter): string
    {
        $data = TrainingRegister::filter($filter)
            ->with([
                'registerFormInfo',
                'program',
            ])
            ->get();

        $fileName = 'tcc-' . date('d-m-Y') . '.xlsx';
        Excel::store(
            new TccExport($data),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

}
