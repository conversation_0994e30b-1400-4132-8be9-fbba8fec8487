<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\GetProgramRequest;
use App\Http\Requests\ProgramListRequest;
use App\Http\Requests\RegisterForms\Activate\StoreRequest;
use App\Http\Requests\RegisterForms\Activate\UpdateRequest;
use App\Http\Resources\RegisterFormActivateResource;
use App\Models\Reestry\Program\Program;
use App\Models\RegisterForms\RegisterFormActivate;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

class RegisterFormActivateController extends Controller
{

    public function index(): AnonymousResourceCollection
    {
        return RegisterFormActivateResource::collection(
            RegisterFormActivate::with(['learnYear.program.academicDegree'])
                ->paginate(request()->page ?? 16)
        );
    }

    public function store(StoreRequest $request): RegisterFormActivateResource
    {
        $validated = $request->validated();
        $validated['url'] = Str::random();
        $registerFormActivate = RegisterFormActivate::create($validated);
        return new RegisterFormActivateResource($registerFormActivate);
    }

    public function show($registerFormActivate): RegisterFormActivateResource
    {
        return new RegisterFormActivateResource(
            RegisterFormActivate::with(['learnYear'])
                ->find($registerFormActivate)
        );
    }

    public function update(UpdateRequest $request, $registerFormActivate): RegisterFormActivateResource
    {
        $registerFormActivateModel = RegisterFormActivate::findOrFail($registerFormActivate);
        $registerFormActivateModel->update($request->validated());
        return new RegisterFormActivateResource($registerFormActivateModel);
    }

    public function destroy($registerFormActivate): Response
    {
        RegisterFormActivate::findOrFail($registerFormActivate)
            ->delete();
        return \response(null, Response::HTTP_NO_CONTENT);
    }

    public function programByAcademicDegree(Request $request,$academicDegree): Response
    {
        if ($request->input('registration_form')) {
            return response(
                Program
                    ::whereAcademicDegreeId($academicDegree)
                    ->where('registration_form', true)
                    ->get()
            );
        }
        return response(
            Program
                ::whereAcademicDegreeId($academicDegree)
                ->get()
        );
    }
}
