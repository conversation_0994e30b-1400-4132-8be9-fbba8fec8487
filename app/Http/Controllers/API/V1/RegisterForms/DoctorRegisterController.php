<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Filters\ProgramFilter;
use App\Filters\Student\ApplicantsFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterForms\Doctor\StoreRequest;
use App\Http\Resources\DoctorRegisterResource;
use App\Models\EnglishLevel;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\Doctor\DoctorCertificate;
use App\Models\RegisterForms\Doctor\DoctorRecommendation;
use App\Models\RegisterForms\DoctorRegister;
use App\Services\DoctorRegisterService;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use function request;

class DoctorRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:phd.index'])->only(['index']);
        $this->middleware(['permission:phd.destroy'])->only(['destroy']);
    }

    const fileNames = [
        'photo',
        'identity_number_copy',
        'cv'
    ];

    public function index(ApplicantsFilter $filter)
    {
        $paginator = DoctorRegister::filter($filter)
            ->with([
                'registerFormInfo',
                'program',
                'englishLevel',
                'certificates',
                'educations',
                'infos',
                'doctorRecommendations'
            ])
            ->paginate(30);

        $data = $paginator->toArray();
        $programFilter = new ProgramFilter();
        $additionalData = [
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'programs' => Program::filter($programFilter)->where('academic_degree_id', '=', 3)
                ->select(['id', 'name_ka'])
                ->get(),
            'englishLevels' => EnglishLevel::pluck('title', 'id'),
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }

    public function store(StoreRequest $request, ImageService $imageService, DoctorRegisterService $doctorRegisterService)
    {
        $validated = $request->validated();
        $identityNumber = $request->identity_number;
        foreach (self::fileNames as $fileName) {
            $validated[$fileName] = $imageService
                ->upload($request->{$fileName},
                    'doctor-register/' .
                    $identityNumber . '/' .
                    $fileName);
        }
        $doctorRegister = DoctorRegister::create($validated);
        $doctorRegister->registerFormInfo()->create($validated);
        if ($request->has('certificates')) {
            foreach ($request->certificates as $certificate) {
                DoctorCertificate::create([
                    'doctor_register_id' => $doctorRegister->id,
                    'title' => $imageService->upload(
                        $certificate,
                        '/doctor-register/' .
                        $identityNumber .
                        '/certificates')
                ]);
            }
        }
        if ($request->has('recommendations')) {
            foreach ($request->recommendations as $recommendation) {
                DoctorRecommendation::create([
                    'doctor_register_id' => $doctorRegister->id,
                    'person' => $recommendation['person'],
                    'phone' => $recommendation['phone']
                ]);
            }
        }
        foreach ($request->educations as $education) {
            $doctorRegisterService->storeEducations($doctorRegister->id, $education);
        }
        if ($request->has('infos')) {
            foreach ($request->infos as $info) {
                $doctorRegisterService->storeInfo($doctorRegister->id, $info);
            }
        }
        return new DoctorRegisterResource($doctorRegister->load([
            'program',
            'englishLevel',
            'registerFormInfo',
            'certificates',
            'educations',
            'infos',
            'doctorRecommendations'
        ]));
    }

    public function show($doctorRegister): DoctorRegisterResource
    {
//        return (new DoctorRegisterResource(
//            DoctorRegister::with([
//                'registerFormInfo',
//                'program',
//                'englishLevel',
//                'certificates',
//                'educations',
//                'infos',
//                'doctorRecommendations'
//            ])->find($doctorRegister)));
    }


    public function update(Request $request, int $doctorRegister)
    {
        //
    }

    public function destroy(int $doctorRegister)
    {
        $doctorRegister = DoctorRegister::findOrFail($doctorRegister);

        if ($doctorRegister->registerFormInfo) {
            $doctorRegister->registerFormInfo->delete();
        }

        $doctorRegister->certificates->each(function ($certificate) {
            $certificate->delete();
        });

        $doctorRegister->doctorRecommendations->each(function ($recommendation) {
            $recommendation->delete();
        });

        $doctorRegister->educations->each(function ($education) {
            $education->delete();
        });

        $doctorRegister->infos->each(function ($info) {
            $info->delete();
        });

        $doctorRegister->delete();
        return response(null, ResponseAlias::HTTP_NO_CONTENT);
    }

}
