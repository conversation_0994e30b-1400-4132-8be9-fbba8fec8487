<?php

namespace App\Http\Controllers\API\V1\RegisterForms;

use App\Exports\BachelorEmailsExport;
use App\Exports\BachelorExport;
use App\Exports\Student\MasterRegisterExport;
use App\Filters\ProgramFilter;
use App\Filters\Student\ApplicantsFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Http\Requests\RegisterForms\Bachelor\StoreRequest;
use App\Http\Requests\RegisterForms\Bachelor\UpdateRequest;
use App\Http\Resources\BachelorRegisterResource;
use App\Models\EnglishLevel;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\BachelorRegister;
use App\Models\RegisterForms\BachelorsTemp;
use App\Models\RegisterForms\MasterRegister;
use App\Services\ImageService;
use Carbon\Carbon;
use Excel;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Str;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use function response;

class BachelorRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:bachelor.index'])->only(['index']);
        $this->middleware(['permission:bachelor.destroy'])->only(['destroy']);
    }

    private const attachmentsName = [
        'photo',
        'identity_number_copy',
        'military_accounting',
        'school_document',
        'payment_document'
    ];

    public function checkBachelor(Request $request)
    {
        $validatedData = $request->validate([
            'personal_id' => [
                'required',
                'regex:/^[0-9]+$/'
            ]
        ]);
        $personalId = $request->input('personal_id');
        $bachelor = BachelorsTemp::where('personal_id', $personalId)->first();

        if (!$bachelor) {
            return response()->json(['message' => 'მოცემული პირადი ნომრით ვერ მოიძებნა ჩარიცხული სტუდენტი!'], 404);
        }

        if ($bachelor->is_registered) {
            return response()->json(['message' => 'მოცემული პირადი ნომრით სტუდენტი უკვე რეგისტრირებულია!'], 400);
        }

        $bachelor['program'] = $bachelor->program->name_ka;
        $bachelor['flow'] = $bachelor->flow->name;
        unset($bachelor['program_id'], $bachelor['flow_id']);
        return response($bachelor);
    }

    public function index(ApplicantsFilter $filter)
    {
        $paginator = BachelorRegister::filter($filter)
            ->where('status', 0)
            ->with(['registerFormInfo', 'program'])
            ->paginate(30);

        $data = $paginator->toArray();
        $programFilter = new ProgramFilter();
        $additionalData = [
            'englishLevels' => EnglishLevel::pluck('title', 'id'),
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'programs' => Program::filter($programFilter)->where('academic_degree_id', '=', 1)
                ->select(['id', 'name_ka'])
                ->get()
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }

    public function store(StoreRequest $request, ImageService $imageService): BachelorRegisterResource
    {
        $validated = $request->validated();
        $temp_bachelors = BachelorsTemp::where('personal_id', $validated['identity_number'])->first();
        $validated['first_name'] = $temp_bachelors->first_name;
        $validated['last_name'] = $temp_bachelors->last_name;
        $validated['program_id'] = $temp_bachelors->program_id;
        $validated['flow_id'] = $temp_bachelors->flow_id;
        $identityNumber = $request->identity_number;
        foreach (self::attachmentsName as $attachment) {
            $validated[$attachment] = $imageService
                ->upload(
                    $request->{$attachment},
                    'register-forms/bachelor/' .
                    $identityNumber . '/' .
                    $attachment
                );
        }
        $bachelorRegister = BachelorRegister::create($validated);
        $bachelorRegister->registerFormInfo()->create($validated);
        BachelorsTemp::where('personal_id', $validated['identity_number'])
            ->where('flow_id', $validated['flow_id'])
            ->update(['is_registered' => 1]);
        return new BachelorRegisterResource($bachelorRegister->load([
            'registerFormInfo',
            'program',
            'englishLevel'
        ]));
    }

    public function show($bachelorRegister): BachelorRegisterResource
    {
//        return new BachelorRegisterResource(BachelorRegister::with(['registerFormInfo'])
//            ->findOrFail($bachelorRegister));
    }

    public function update(UpdateRequest $request, $bachelorRegister, ImageService $imageService): BachelorRegisterResource
    {
//        $attachmentsArray = [
//            'photo',
//            'identity_number_copy',
//            'military_accounting'
//        ];
//        $identityNumber = $request->identity_number;
//        $validated = $request->validated();
//        foreach ($attachmentsArray as $attachment) {
//            if ($request->has($attachment)) {
//                $validated[$attachment] = $imageService
//                    ->upload(
//                        $request->{$attachment},
//                        'register-forms/bachelor/' . $attachment
//                        . '/' .
//                        $identityNumber
//                    );
//            }
//        }
//        $bachelorRegister = BachelorRegister::findOrFail($bachelorRegister);
//        $bachelorRegister->update($validated);
//        $bachelorRegister->registerFormInfo->update($validated);
//        return new BachelorRegisterResource($bachelorRegister->load(['registerFormInfo']));
    }

    public function destroy(int $bachelorRegister): Response
    {
        $bachelorRegister = BachelorRegister::findOrFail($bachelorRegister);
        if ($bachelorRegister->registerFormInfo->delete()) {
            $bachelorRegister->registerFormInfo->delete();
        }
        $bachelorRegister->delete();
        return response(null, ResponseAlias::HTTP_NO_CONTENT);
    }


    public function exportExcel(ExcelRequest $request, ApplicantsFilter $filter): string
    {
        $columns = json_decode($request->columns);

        $students = BachelorRegister::filter($filter)
            ->where('status', 0)
            ->with(['registerFormInfo', 'program'])
            ->get();

        $fileName = 'students-' . Str::random(8) . '.xlsx';
        Excel::store(
            new BachelorExport($students),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

    public function exportDataForEmails()
    {
        $applicants = BachelorRegister::with([
            'registerFormInfo',
            'program:id,name_ka',
        ])
            ->where('created_at', '>', Carbon::parse('2024-09-02'))
            ->orderByDesc('id')->get();

        $fileName = 'bachelor-applicants-' . date('d-m-Y') . '.xlsx';
        \Maatwebsite\Excel\Facades\Excel::store(
            new BachelorEmailsExport($applicants),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }
}
