<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use function response;

class CommonController extends Controller
{

    /**
     * @param Request $request
     * @return BinaryFileResponse
     * Ready : Administration , Lecturer , Academic Degree , Auditorium , Campus , Direction , Student , StudentGroup
     */

    public function downloadFile(Request $request): BinaryFileResponse
    {
        ob_end_clean();
        return response()->download('storage/'. $request->filename)->deleteFileAfterSend(true);
    }

    public function downloadPDF(Request $request): BinaryFileResponse
    {
        ob_end_clean();
        return response()->download('storage/'. $request->filename)->deleteFileAfterSend(true);
    }

}
