<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\ProgramExport;
use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Program\StoreRequest;
use App\Http\Requests\Reestry\Program\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ProgramController extends Controller
{
    public function __construct()
    {
//        $this->middleware(['permission:programs.index'])->only(['index']);
        $this->middleware(['permission:programs.store'])->only(['store']);
        $this->middleware(['permission:programs.show'])->only(['show']);
        $this->middleware(['permission:programs.update'])->only(['update']);
        $this->middleware(['permission:programs.destroy'])->only(['destroy']);
        $this->middleware(['permission:programs.export'])->only(['exportExcel']);
    }

    public function index(ProgramFilter $filter): Response
    {
        return response([
            'programs' => Program::filter($filter)->with([
                'school',
                'academicDegree',
            ])
                ->orderBy('name_ka')
                ->paginate(50),
            'school' => School::filter(new SchoolFilter(request()))->get()->pluck('name_ka', 'id'),
            'academicDegrees' => AcademicDegree::pluck('name_ka', 'id')
        ]);
    }

    public function store(StoreRequest $request): Response
    {
        $program = Program::create($request->validated());
        return response($program->load(['school', 'academicDegree']), Response::HTTP_CREATED);
    }

    public function show(int $program): Response
    {
        return response(
            Program::with([
                'school',
                'academicDegree'
            ])->findOrFail($program)
        );
    }

    public function update(UpdateRequest $request, int $program): Response
    {
        $program = Program::findOrFail($program);
        $program->update($request->validated());
        return response($program->load(['school', 'academicDegree']));
    }

    public function destroy($program): Response
    {
        Program::findOrFail($program)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, ProgramFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $programs = Program::filter($filter)->with(['school', 'academicDegree'])->get();
        $fileName = 'program-' . Str::random(8) . '.xlsx';

        Excel::store(
            new ProgramExport($columns, $programs),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }
}
