<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\LecturerExport;
use App\Exports\LecturerFinanceExport;
use App\Filters\LecturerFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Lecturer\StoreRequest;
use App\Http\Requests\Reestry\Lecturer\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Http\Requests\UpdateCurriculumGlobalParemeters;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Direction;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Survey\SurveyActivation;
use App\Models\User\User;
use App\Models\User\UserType;
use App\Services\DateService;
use App\Services\ImageService;
use App\Services\LecturerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Log;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class LecturerController extends Controller
{

    public function __construct()
    {
        $this->middleware(['permission:lecturers.index'])->only(['index']);
        $this->middleware(['permission:lecturers.store'])->only(['store']);
        $this->middleware(['permission:lecturers.show'])->only(['show']);
        $this->middleware(['permission:lecturers.update'])->only(['update']);
        $this->middleware(['permission:lecturers.destroy'])->only(['destroy']);
        $this->middleware(['permission:lecturers.export'])->only(['exportExcel']);
    }

    public function lecturerFinanceExport(Request $request)
    {
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;
        $programId= $request->program_id;
        $schoolId = $request->school_id;

        $lectures = \App\Models\Lectures\Lecture::query()
            ->with([
                'lecturer',
                'curricula.lecture',
                'syllabus.learnYear.program',
                'syllabus.learnYear.program.school',
                'syllabus' => function($q){
                    $q->select('id', 'learn_year_id', 'name');
                },
            ])
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where('lecture_date', '>=', $dateFrom);
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                $query->where('lecture_date', '<=', $dateTo);
            })
            ->whereHas('syllabus.learnYear.program', function ($program) use ($programId, $schoolId){
                $program
                    ->when($programId, function ($query, $programId) {
                        return $query->where('id', $programId);
                    })
                    ->when($schoolId, function ($query, $schoolId) {
                        return $query->where('school_id', $schoolId);
                    })
                ;
            })
            ->get()
        ;

        return Excel::download(new LecturerFinanceExport($lectures), 'lecturer-finance-'. \Illuminate\Support\Carbon::now().'.xlsx');
    }

    public function index(LecturerFilter $filter): Response
    {
        return response([
            'lecturers' => Lecturer::filter($filter)
                ->with([
                    'academicDegree',
                    'directions'
                ])
                ->OrderByDesc('id')
                ->paginate(30),
            'academicDegrees' => AcademicDegree::pluck('name_ka', 'id'),
            'directions' => Direction::pluck('name_ka', 'id')]);
    }

    public function store(StoreRequest $request, ImageService $imageService): Response
    {
        $user = [
            'email' => $request->email,
            'name' => $request->first_name . ' ' . $request->last_name,
            'password' => Hash::make('gipa2023'),
            'user_type_id' => UserType::LECTURER
        ];
        $createdUser = User::create($user);
        $pathName = '/lecturers/' . $request->identity_number . '/profile';
        $validated = $request->validated();
        if ($request->has('photo')) {
            $photo = $imageService->upload(file: $request->photo, path: $pathName);
            $validated['photo'] = $photo;
        }
        if ($request->has('cv')) {
            $cv = $imageService->upload(file: $request->cv, path: $pathName);
            $validated['cv'] = $cv;
        }
        if ($request->has('date_of_birth')) {
            $validated['date_of_birth'] = Carbon::createFromFormat('d/m/Y', $validated['date_of_birth'])->format('Y-m-d');
        }
        if (!isset($validated['card_number'])) {
            $validated['card_number'] = 0;
        }
        if (!isset($validated['address'])) {
            $validated['address'] = 0;
        }
        if (!isset($validated['academic_degree_id'])) {
            $validated['academic_degree_id'] = 1;
        }
        $validated['user_id'] = $createdUser->id;
        unset($validated['directions_id']);
        $lecturer = Lecturer::create($validated);
        $lecturer->directions()->sync($request->directions_id, []);
        return response($lecturer->load([
            'academicDegree',
            'directions'
        ]), Response::HTTP_CREATED);
    }

    public function show(int $lecturer): Response
    {
        return response(
            Lecturer::with(['academicDegree', 'directions'])
                ->findOrFail($lecturer)
        );
    }

    public function update(UpdateRequest $request, int $lecturer, ImageService $imageService): Response
    {
        $lecturer = Lecturer::findOrFail($lecturer);
        $pathName = '/lecturers/' . $request->identity_number . '/profile';
        $photo = $request->hasFile('photo')
            ? $imageService->upload(file: $request->photo, path: $pathName)
            : $lecturer->photo;
        $cv = $request->hasFile('cv')
            ? $imageService->upload(file: $request->cv, path: $pathName)
            : $lecturer->cv;
        $validated = $request->validated();
        if ($request->has('date_of_birth')) {
            $validated['date_of_birth'] = Carbon::createFromFormat('d/m/Y', $validated['date_of_birth'])->format('Y-m-d');
        }
        if (!isset($validated['card_number'])) {
            $validated['card_number'] = 0;
        }
        if (!isset($validated['address'])) {
            $validated['address'] = 0;
        }
        if (!isset($validated['academic_degree_id'])) {
            $validated['academic_degree_id'] = 1;
        }
        $validated['cv'] = $cv;
        $validated['photo'] = $photo;
        unset($validated['directions_id']);

        if ($request->has('email')) {
            $user = User::where('id', $lecturer->user_id)->first();
            if ($user) {
                $user->email = $request->email;
                $user->save();
            }
        }

        $lecturer->update($validated);
        $lecturer->directions()->sync($request->directions_id, []);
        return response($lecturer->load([
            'academicDegree',
            'directions'
        ]));
    }

    public
    function destroy(int $lecturer): Response
    {
        $lecturer = Lecturer::with('user')->findOrFail($lecturer);
        $lecturer->user->delete();
        $lecturer->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, LecturerFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $lecturers = Lecturer::filter($filter)
            ->with(['academicDegree'])
            ->get();
        $fileName = 'lecturers-' . Str::random() . '.xlsx';
        Excel::store(
            new LecturerExport($columns, $lecturers),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

    public function lecturerSubjects(LecturerService $lecturerService): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'subjects' => $lecturerService->lecturerSubjects(),
            'lecturerId' => Lecturer::whereUserId(\Auth::id())
                ->first()?->id
        ]);
    }

    public function todayLectures(LecturerService $lecturerService): JsonResponse
    {
        $lectures = $lecturerService->todayLectures();
        return response()->json($lectures);
    }

    public function weekLectures(LecturerService $lecturerService): JsonResponse
    {
        $lectures = $lecturerService->weekSchedule();
        return response()->json($lectures);
    }

    public function semesterLectures(Request $request)
    {
        $semesterID = $request->semester_id;
        if (!$semesterID || $semesterID === 'undefined' || !is_numeric($semesterID)) {
            $setting = Setting::where('key', 'current_semester')->first();
            if (!$setting) {
                return [
                    'code' => 400,
                    'message' => 'No current semester defined in settings!'
                ];
            }
            $semesterID = $setting->value;
        }
        $lecturer = Lecturer::whereUserId(\Auth::id())
            ->first();
        if (!$lecturer) {
            return [
                'code' => 404,
                'message' => 'Lecturer not found'
            ];
        }
        $lecturerId = $lecturer->id;
        $lectures = Lecture::with(['syllabus:id,name,learn_year_id,academic_degree_id,is_profession,syllabus_type_id' => [
            'curriculum:id,syllabus_id,flow_id' => [
                'flow:id,program_id' => [
                    'program:id,name_ka,name_en,school_id,academic_degree_id' => [
                        'school:id,name_ka,name_en',
                        'academicDegree:id,name_ka,name_en,url'
                    ]
                ]
            ]
        ],
            'auditorium:id,name'
        ])
           // ->whereHas('syllabus.curriculum')
            ->whereHas('syllabus.curriculum', function ($query) use ($semesterID) {
                return $query->where('flow_id', $semesterID);
            })
            ->where('lecturer_id', '=', $lecturerId)
            ->get()
            ->map(function ($lecture) use ($lecturerId) {

                $lectureFlow = $lecture->syllabus->learnYear;
                $lecture['survey_id'] = SurveyActivation::query()
                    ->where('syllabus_id', $lecture->syllabus_id)
                    ->where('lecturer_id', $lecturerId)
                    ->first()
                    ->survey_id
                    ?? null
                ;
                $lecture['subject'] = $lecture->syllabus->name;
                $lecture['subject_en'] = $lecture->syllabus->name_en;
                $lecture['syllabus_id'] = $lecture->syllabus_id;
                $lecture['program'] = $lectureFlow->program->name_ka;
                $lecture['school'] = $lectureFlow->program->school->name_ka;
                $lecture['academicDegree'] = $lectureFlow->program->academicDegree->name_ka;
                $lecture['academicDegreeUrl'] = $lectureFlow->program->academicDegree->url;
                $lecture['lectureType'] = $lecture->is_lecture ? 'lecture' : 'seminar';
                $lecture['is_profession'] = $lecture->syllabus->is_profession;
                $lecture['academic_degree_id'] = $lecture->syllabus->academic_degree_id;
                $lecture['syllabus_type_id'] = $lecture->syllabus->syllabus_type_id;

                $filteredStudentGroupIds = $lecture->syllabus->curriculum->lecture->times->filter(function ($time) use ($lecture) {
                    return $time->week_day == $lecture->week_day && $time->start_time == $lecture->start_time;
                })->flatMap(function ($time) {
                    return $time->studentGroups->pluck('id');
                });
                $lecture['studentGroups'] = StudentGroup::whereIn('id', $filteredStudentGroupIds)
                    ->pluck('name_ka', 'id');
                /**
                 * Unset additional columns from lecture object.
                 */

                unset(
                    $lecture->syllabus,
                    $lecture->auditorium->id,
                    $lecture->payment_per_hour,
                    $lecture->is_current,
                    //$lecture->syllabus_id,
                    $lecture->lecturer_id,
                    $lecture->week_day,
                    $lecture->auditorium_id,
                    $lecture->created_at,
                    $lecture->updated_at,
                    $lecture->id,
                    $lecture->is_lecture,
                );
                $auditoriumName = $lecture->auditorium->name;
                unset($lecture->auditorium);
                $lecture['auditorium'] = $auditoriumName;
                return $lecture;
            })
            ->groupBy('syllabus_id');
        return [
            'code' => 200,
            'data' => $lectures
        ];
    }

    public function lecturerLectures(): JsonResponse
    {
        $lecturerId = Lecturer::whereUserId(\Auth::id())
            ->first()->id;
        $currentFlowId = Setting::where('key', '=', 'current_semester')
            ->first()->value;
        $currentSyllabusIds = Curriculum::where('flow_id', $currentFlowId)
            ->pluck('syllabus_id')->toArray();
        $lectures = Lecture::select('id', 'syllabus_id', 'lecture_date', 'start_time', 'end_time')
            ->where('lecturer_id', '=', $lecturerId)
            ->whereIn('syllabus_id', $currentSyllabusIds)
            ->with('syllabus:id,name')
            ->get();
        return response()->json([
            'success' => true,
            'data' => $lectures
        ]);
    }

    public function syllabusLectures(int $syllabusId, LecturerService $lecturerService): JsonResponse
    {
        $lectureIds = request()->get('lecture_ids');
        if (!is_null($lectureIds)) {
            $syllabusLectures = $lecturerService->syllabusLectures($syllabusId, $lectureIds);
        } else {
            $syllabusLectures = $lecturerService->syllabusLectures($syllabusId);
        }
        return response()->json($syllabusLectures);
    }

    public function updateSyllabusGlobalParameters(int $syllabusId, UpdateCurriculumGlobalParemeters $request)
    {
        $curriculum = Curriculum::whereSyllabusId($syllabusId)->first();
        $curriculum->start_date = Carbon::createFromFormat('d/m/Y', $request->start_date);
        $curriculum->end_date = Carbon::createFromFormat('d/m/Y', $request->end_date);
        if ($request->has('minimum_amount_of_students') && $request->minimum_amount_of_students) {
            $curriculum->minimum_amount_of_students = $request->minimum_amount_of_students;
        }
        if ($request->has('allowed_amount_of_students') && $request->allowed_amount_of_students) {
            $curriculum->allowed_amount_of_students = $request->allowed_amount_of_students;
        }
        try {
            if ($request->has('registration_start_date') && $request->registration_start_date) {
                $startDate = Carbon::createFromFormat('d-m-Y H:i', $request->registration_start_date);
                $curriculum->registration_start_date = $startDate->format('Y-m-d H:i:s');
            }
            if ($request->has('registration_end_date') && $request->registration_end_date) {
                $endDate = Carbon::createFromFormat('d-m-Y H:i', $request->registration_end_date);
                $curriculum->registration_end_date = $endDate->format('Y-m-d H:i:s');
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
        if ($request->has('student_flow_ids')) {
            $curriculum->student_flow_ids = $request->student_flow_ids;
        } else {
            $curriculum->student_flow_ids = NULL;
        }
        $curriculum->save();
        $curriculumLecture = CurriculumLecture::whereCurriculumId($curriculum->id)->first();
        $curriculumLecture->lectures_count = $request->lectures_count;
        $curriculumLecture->save();
        return [
            'code' => 200,
            'parameters' => $curriculum
        ];
    }
}
