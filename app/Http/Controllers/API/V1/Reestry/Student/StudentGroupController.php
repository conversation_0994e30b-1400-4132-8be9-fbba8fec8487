<?php

namespace App\Http\Controllers\API\V1\Reestry\Student;

use App\Exports\Student\StudentGroupExport;
use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Filters\Student\StudentGroupFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Http\Requests\Reestry\Student\Group\StoreRequest;
use App\Http\Requests\Reestry\Student\Group\UpdateRequest;
use App\Http\Requests\Reestry\Student\StudentAttachGroupRequest;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\StudentGroup;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class StudentGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:student-groups.index'])->only(['index']);
        $this->middleware(['permission:student-groups.store'])->only(['store']);
        $this->middleware(['permission:student-groups.show'])->only(['show']);
        $this->middleware(['permission:student-groups.update'])->only(['update']);
        $this->middleware(['permission:student-groups.destroy'])->only(['destroy']);
        $this->middleware(['permission:student-groups.export'])->only(['exportExcel']);
    }

    /**
     * @OA\Get(
     * path="/student-groups",
     * tags={"Student groups"},
     * summary="List of student groups",
     * description="List of student groups",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="program_id",
     *                         type="integer",
     *                         description="Program relation",
     *                     ),
     *
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "program" : "related object"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(StudentGroupFilter $filter): Response
    {
        $programFilter = (new ProgramFilter(request()));
        $schoolFilter = (new SchoolFilter(request()));
        return response([
            'studentGroups' => StudentGroup::filter($filter)->with(['program'])
                ->OrderByDesc('id')
                ->paginate(200),
            'programs' => Program::filter($programFilter)->pluck('name_ka', 'id'),
            'schools' => School::filter($schoolFilter)->pluck('name_ka', 'id')]);
    }

    /**
     * @OA\Post(
     * path="/student-groups",
     * tags={"Student groups"},
     * summary="Add student group",
     * description="Add student group",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreStudentGroupRequest",
     *               required={"name_en","name_ka","program_id"},
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="program_id", type="integer")
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Student group created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $studentGroup = StudentGroup::create($request->validated());
        return response($studentGroup->load(['program']), Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/student-groups/{id}",
     * tags={"Student groups"},
     * summary="Show student group",
     * description="Show student group",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="program_id",
     *                         type="object",
     *                         description="String",
     *                     ),
     *
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "program" : "related object"
     * }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show(int $studentGroup): Response
    {
        return response(StudentGroup::with(['program'])
            ->findOrFail($studentGroup)
        );
    }

    /**
     * @OA\Put(
     * path="/student-groups/{id}",
     * tags={"Student groups"},
     * summary="Update student groups",
     * description="Update student groups",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *     )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateSchoolRequest",
     *               required={"name_en","name_ka","program_id"},
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="program_id", type="integer")
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Student group updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, int $studentGroup): Response
    {
        $studentGroup = StudentGroup::findOrFail($studentGroup);
        $studentGroup->update($request->validated());
        return response($studentGroup->load(['program']));
    }

    /**
     * @OA\Delete(
     * path="/student-groups/{id}",
     * tags={"Student groups"},
     * summary="Delete student group",
     * description="Delete student group",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete student group",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy(int $studentGroup): Response
    {
        StudentGroup::findOrFail($studentGroup)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, StudentGroupFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $studentGroups = StudentGroup::filter($filter)->with(['program'])->get();
        $fileName = 'student-group-' . Str::random(8) . '.xlsx';
        Excel::store(
            new StudentGroupExport($columns, $studentGroups),
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }


    public function attachStudents(StudentAttachGroupRequest $request): Response
    {
        $studentGroup = StudentGroup::find($request->id);
        $studentGroup->students()
            ->sync($request->student_ids);
        return response($studentGroup->load(['students']));
    }
}
