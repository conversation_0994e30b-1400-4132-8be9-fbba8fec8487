<?php

namespace App\Http\Controllers\API\V1\Reestry\Student;

use App\Http\Controllers\Controller;
use App\Models\Reestry\Student\Student;
use App\Models\Syllabus\Syllabus;
use App\Services\StudentService;
use Illuminate\Http\JsonResponse;

class StudentLectureController extends Controller
{

    public function index()
    {
        $student = Student::where('user_id', auth()->id())->firstOrFail();
        $lectures = $student->lectures()->where('is_current', 1)->load(['syllabus', 'lecturer', 'auditorium']);
        return response($lectures);
    }
}
