<?php

namespace App\Http\Controllers\API\V1\Reestry\Student;

use App\Exports\MinorExport;
use App\Exports\Student\FinanceStatusLogExport;
use App\Exports\Student\StudentExport;
use App\Exports\Student\StudentExportByProgram;
use App\Exports\Student\SyllabusPointsExport;
use App\Filters\LearnYearFilter;
use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Filters\Student\StudentFilter;
use App\Filters\Student\StudentGroupFilter;
use App\Filters\Student\StudentRecordFilter;
use App\Filters\StudentFinanceStatusLogFilter;
use App\Filters\Syllabus\SyllabusFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Http\Requests\Reestry\Student\StoreRequest;
use App\Http\Requests\Reestry\Student\UpdateRequest;
use App\Http\Requests\SetStudentSyllabusRequest;
use App\Imports\StudentTemplateImport;
use App\Models\Reestry\Student\StudentStatusHistory;
use App\Models\RegisterForms\RegisterFormInfo;
use App\Models\StudentMinorLog;
use App\Modls\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Curriculum\CurriculumStudentGroup;
use App\Models\Finance\Finance;
use App\Models\FinanceStudentStatusLog;
use App\Models\Lectures\Lecture;
use App\Models\Lectures\StudentAttendance;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentBasicsOfEnrollment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentLearnYearHistory;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Syllabus\Syllabus;
use App\Models\SyllabusStudentGuest;
use App\Models\User\User;
use App\Models\User\UserType;
use App\Services\ImageService;
use App\Services\StudentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use function collect;
use function response;

class StudentController extends Controller
{

    const filteredDataParams = ['school_id', 'program_id', 'group_id', 'learn_year_id'];
    const fileTypes = ['cv_file_name', 'diploma_file_name', 'transcript_file_name', 'motivation_article_file_name', 'photo'];

    public function __construct()
    {
        $this->middleware(['permission:students.index'])->only(['index']);
        $this->middleware(['permission:students.store'])->only(['store']);
        $this->middleware(['permission:students.show'])->only(['show']);
        $this->middleware(['permission:students.update'])->only(['update']);
        $this->middleware(['permission:students.destroy'])->only(['destroy']);
        $this->middleware(['permission:students.export'])->only(['exportExcel']);
    }

    public function importStudentTemplate(Request $request)
    {
        $request = $request->validate([
            'file' => 'required|mimes:xlsx,xls'
        ]);

        Excel::import(new StudentTemplateImport(), $request['file']);

        return response()->json(['success' => true, 'status' => 200]);
    }

    public function index(StudentFilter $filter, Request $request): Response
    {
        $programFilter = (new ProgramFilter(request()));
        $schoolFilter = (new SchoolFilter(request()));
        $learnYearFilter = (new LearnYearFilter(request()));
        $studentGroupFilter = (new StudentGroupFilter(request()));
        $sortKey = $request->key;
        $sortOrder = $request->order;
        //TODO:: სტუდენტს რაც აქვს GPA აქ გავაყოლოთ
        return response([
            'students' => Student::filter($filter)->with(['user', 'status',
                'basicOfEnrollment', 'studentGroup', 'school', 'program', 'learnYear'])
                ->when($sortKey and $sortOrder, function ($student) use($sortKey, $sortOrder){
                    $student->orderBy($sortKey, $sortOrder);
                })
                ->OrderByDesc('id')
                ->paginate(30),
            'status' => StudentStatusList::pluck('name_ka', 'id'),
            'basicOfEnrollments' => StudentBasicsOfEnrollment::pluck('name', 'id'),
            'studentGroups' => StudentGroup::filter($studentGroupFilter)->pluck('name_ka', 'id'),
            'school' => School::filter($schoolFilter)->pluck('name_ka', 'id'),
            'program' => Program::filter($programFilter)->pluck('name_ka', 'id'),
            'learnYear' => LearnYear::filter($learnYearFilter)->pluck('name', 'id'),
            'genders' => ['მდედრობითი', 'მამრობითი'],
            'mobility' => ['არა', 'კი'],
        ]);
    }

    public function store(StoreRequest $request, ImageService $imageService): Response
    {

        $user = [
            'email' => $request->email,
            'name' => $request->name . ' ' . $request->surname,
            'password' => Hash::make('gipa2024'),
            'user_type_id' => UserType::STUDENT
        ];
        $createdUser = User::create($user);
        $data = $request->all();
        if ($request->has('diploma_taken')) {
            $data['diploma_taken_date'] = Carbon::createFromFormat('d/m/Y', $data['diploma_taken_date']);
        }

        if (isset($data['birthday'])) {
            $data['birthday'] = Carbon::createFromFormat('d/m/Y', $data['birthday']);
        }
        if (isset($data['enrollment_date'])) {
            $data['enrollment_date'] = Carbon::createFromFormat('d/m/Y', $data['enrollment_date']);
        }
        if (!isset($data['basics_of_enrollement_id'])) {
            $data['basics_of_enrollement_id'] = 1;
        }
        if (!isset($data['learn_year_id'])) {
            $data['learn_year_id'] = 0;
        }
        $data['user_id'] = $createdUser->id;
        $data['email'] = $createdUser->email;
        foreach (self::fileTypes as $fileType) {
            if ($request->has($fileType)) {
                $data[$fileType] = $imageService->upload($request->{$fileType},
                    '/students/' . $data['personal_id'] . '/profile');
            }
        }
        $student = Student::create($data);
        StudentLearnYearHistory::create([
            'student_id' => $student->id,
            'learn_year_id' => $student->learn_year_id,
        ]);
        return response($student->load(['status', 'basicOfEnrollment',
            'school', 'learnYear', 'user', 'studentGroup', 'program']), Response::HTTP_CREATED);
    }

    public
    function show($student): Response
    {
        Student::with(['school.programs', 'learnYear', 'studentGroup'])
            ->findOrFail($student);
        return response($student);
    }

    public function update(UpdateRequest $request, $student, ImageService $imageService): Response
    {
        $data = $request->all();

        $student = Student::findOrFail($student);
        if ($student->status_id == 9 and $request->status_id != 9)
        {
            return response('არ გაქვთ უფლება შეცვალოთ სტატუსი იმ შემთხვევაში თუ სტატუსი ფინანსური დავალიანების გამო აქვს შეჩერებული','422');
        }
        if ($student->learn_year_id !== $request->learn_year_id) {
            StudentLearnYearHistory::create([
                'student_id' => $student->id,
                'learn_year_id' => $student->learn_year_id,
            ]);
        }
        foreach (self::fileTypes as $key => $fileType) {
            if ($request->hasFile($fileType)) {
                $data[$fileType] = $imageService->upload($request->{$fileType},
                    '/students/' . $data['personal_id'] . '/profile');
                if (Storage::exists('/students/' . $data['personal_id'] .
                    '/profile' . '/' . $student->{$fileType})) {
                    Storage::delete('/students/' . $data['personal_id'] .
                        '/profile/' . $student->{$fileType});
                }
            }
        }
        if ($request->has('diploma_taken')) {
            $data['diploma_taken_date'] = Carbon::createFromFormat('d/m/Y', $data['diploma_taken_date']);
        }
        if ($request->has('diploma_number')) {
            $data['diploma_number'] = $request->diploma_number;
        }
        if (isset($data['birthday'])) {
            $data['birthday'] = Carbon::createFromFormat('d/m/Y', $data['birthday']);
        }
        if (isset($data['enrollment_date'])) {
            $data['enrollment_date'] = Carbon::createFromFormat('d/m/Y', $data['enrollment_date']);
        }
        if (!isset($data['basics_of_enrollement_id'])) {
            $data['basics_of_enrollement_id'] = 1;
        }
        if (!isset($data['learn_year_id'])) {
            $data['learn_year_id'] = 0;
        }
        if ($request->has('email')) {
            $user = User::where('id', $student->user_id)->first();
            if ($user) {
                $user->email = $request->email;
                $user->save();
            }
        }
        if ($student->status_id != $request->status_id)
        {
            StudentStatusHistory::query()->create([
                'student_id' => $student->id,
                'status_id' => $request->status_id
            ]);
        }
        $student->update($data);
        return response($student->load(['status', 'basicOfEnrollment',
            'school', 'learnYear', 'user', 'studentGroup', 'program']));
    }

    public function destroy($student): Response
    {
        $student = Student::with('user')->findOrFail($student);
        $student->user->delete();
        $student->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, StudentFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $students = Student::filter($filter)->with(['user', 'status', 'basicOfEnrollment',
            'studentGroup', 'school', 'program', 'learnYear'])->get();
        $fileName = 'students-' . Str::random(8) . '.xlsx';
        Excel::store(
            new StudentExport($columns, $students),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

    public function exportExcelByProgram(Request $request,$programID): BinaryFileResponse|JsonResponse
    {
        $logs = Student::whereProgramId($programID)->with(['program','status'])
            ->orderBy('surname')
            ->get();

        return $request->export == 1 ? Excel::download(new StudentExportByProgram($logs), 'students.xlsx') : response()->json($logs);
    }

    public function getFilteredData(Request $request, StudentRecordFilter $filters): Response
    {
        $response = [];
        $query = School::filter($filters)->with(['programs.studentGroups', 'programs.learnYears']);
        $schools = $query->get();
        $programs = collect([]);
        $studentGroups = collect([]);
        $learnYears = collect([]);
        if ($schools) {
            foreach ($schools as $school) {
                foreach ($school->programs as $program) {
                    $programs[$program->id] = $program->name_ka;
                    foreach ($program->studentGroups as $studentGroup) {
                        $studentGroups[$studentGroup->id] = $studentGroup->name_ka;
                    }
                    foreach ($program->learnYears->where('program_id', $program->id) as $learnYear) {
                        $learnYears[$learnYear->id] = $learnYear->name . '-' . $program->name_ka;
                    }
                }
            }
        }

        foreach (self::filteredDataParams as $filteredDataParam) {
            if (!$request->has($filteredDataParam)) {
                $response[$filteredDataParam] = match ($filteredDataParam) {
                    'school_id' => $query->pluck('name_ka', 'id'),
                    'program_id' => $programs,
                    'group_id' => $studentGroups,
                    'learn_year_id' => $learnYears
                };
            }
        }

        return response(json_encode($response));
    }

    public function studentSubjects(Request $request, StudentService $studentService): JsonResponse
    {

        $studentSubjects = $studentService->studentSubjects($request->semester_id);
        return response()->json($studentSubjects);
    }

    public function todayLectures(StudentService $studentService): JsonResponse
    {
        $todayLectures = $studentService->todayLectures();
        return response()->json($todayLectures);
    }

    public function weekSchedule(StudentService $studentService): JsonResponse
    {
        $weekLectures = $studentService->weekSchedule();
        return response()->json($weekLectures);
    }

    public function passedSubjects(StudentService $studentService): JsonResponse
    {
        $passedSubjects = $studentService->passedSubjects();
        return response()->json($passedSubjects);
    }

    public function missedLectures(
        int            $syllabusId,
        StudentService $studentService
    ): JsonResponse
    {
        $missedLectures = $studentService->missedLectures($syllabusId);
        return response()->json($missedLectures);
    }

    public function showSyllabus(Syllabus $syllabus): JsonResponse
    {
        $syllabus->load(['learnYear',
            'status',
            'semester',
            'lecturers',
            'lecturerContactTimes' => function ($query) use ($syllabus) {
                $query->where('syllabus_id', $syllabus)->with('lecturer');
            },
            'weeks',
            'assignments',
            'prerequisites',
            'academicDegree',
            'learningOutcomes',
            'methods']);
        return response()->json($syllabus);
    }

    public function studentSyllabusMissedLectures(int $syllabusId): JsonResponse
    {
        $lectureIds = Lecture::whereSyllabusId($syllabusId)
            ->pluck('id')->toArray();
        $studentId = Student::whereUserId(auth()->id())->first()->id;
        $missedLectures = StudentAttendance::whereStudentId($studentId)
            ->whereIn('lecture_id', $lectureIds)
            ->with('lecture:id,lecture_date')
            ->get();
        return response()->json([
            'success' => true,
            'data' => $missedLectures
        ]);
    }

    public function studentLectures()
    {
        $studentId = Student::whereUserId(3)->first()->id;
        $currentFlowId = Setting::where('key', '=', 'current_semester')
            ->first()->value;
        $currentSyllabusIds = StudentSyllabusHistory::whereStudentId($studentId)
            ->whereHas('syllabus.curriculum', function ($query) use ($currentFlowId) {
                return $query->where('flow_id', $currentFlowId);
            })->pluck('syllabus_id')->toArray();
        $lectures = Lecture::select('id', 'syllabus_id', 'lecture_date', 'start_time', 'end_time')
            ->whereIn('syllabus_id', $currentSyllabusIds)
            ->with('syllabus:id,name')
            ->get();
        return response()->json([
            'success' => true,
            'lectures' => $lectures
        ]);
    }

    public function searchStudents(Request $request): JsonResponse
    {
        if (mb_strlen($request->keyword, 'UTF-8') < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Please enter at least 3 characters for your search.',
                'data' => []
            ]);
        }

        $studentsInSyllabus = StudentSyllabusHistory::whereSyllabusId($request->syllabus_id)
            ->pluck('student_id')->toArray();

        $students = Student::with(['program:id,name_ka,name_en'])
            ->select(
                'id',
                'name',
                'surname',
                'personal_id',
                'program_id',
                'photo',
                'course'
            )
            ->whereNotIn('id', $studentsInSyllabus)
            ->where(function ($query) use ($request) {
                $query->where('name', 'like', $request->keyword . '%')
                    ->orWhere('surname', 'like', $request->keyword . '%')
                    ->orWhere('personal_id', 'like', $request->keyword . '%');
            })
            ->get();

        return response()->json([
            'success' => true,
            'data' => $students
        ]);
    }


    public function setStudentsInSyllabus(SetStudentSyllabusRequest $request)
    {
        $existingStudentIds = [];
        $newlyInsertedCount = 0;
        $curriculumLectureTimeIds = $request->curriculum_lecture_time_ids;
        $syllabusType = Syllabus::findOrFail($request->syllabus_id)->status_id;
        if ($syllabusType == 1) {
            foreach ($curriculumLectureTimeIds as $curriculumLectureTimeId) {
                $studentGroupId = CurriculumStudentGroup::where('curriculum_lecture_time_id', $curriculumLectureTimeId)->first()?->student_group_id;
                $curriculumLectureTime = CurriculumLectureTime::findOrFail($curriculumLectureTimeId);
                foreach ($request->student_ids as $studentId) {

                    $lectures = Lecture::where('syllabus_id', $request->syllabus_id)
                        ->where('week_day', $curriculumLectureTime->week_day)
                        ->where('start_time', $curriculumLectureTime->start_time)
                        ->where('end_time', $curriculumLectureTime->end_time)
                        ->get();
                    foreach ($lectures as $lecture) {
                        $lecture->students()->syncWithoutDetaching([$studentId]);
                    }

                    $existingEntry = StudentSyllabusHistory::where([
                        'student_id' => $studentId,
                        'syllabus_id' => $request->syllabus_id
                    ])->exists();

                    if (!$existingEntry) {
                        StudentSyllabusHistory::create([
                            'student_id' => $studentId,
                            'syllabus_id' => $request->syllabus_id
                        ]);

                        $newlyInsertedCount++;
                        SyllabusStudentGuest::create([
                            'syllabus_id' => $request->syllabus_id,
                            'student_group_id' => $studentGroupId,
                            'student_id' => $studentId
                        ]);
                    } else {
                        $existingStudentIds[] = $studentId;
                    }
                }
            }
        } else {
            foreach ($request->student_ids as $studentId) {
                $existingEntry = StudentSyllabusHistory::where([
                    'student_id' => $studentId,
                    'syllabus_id' => $request->syllabus_id
                ])->exists();

                if (!$existingEntry) {
                    StudentSyllabusHistory::create([
                        'student_id' => $studentId,
                        'syllabus_id' => $request->syllabus_id
                    ]);

                    $lectures = Lecture::where('syllabus_id', $request->syllabus_id)
                        ->get();
                    foreach ($lectures as $lecture) {
                        $lecture->students()->syncWithoutDetaching([$studentId]);
                    }
                    $newlyInsertedCount++;
                } else {
                    $existingStudentIds[] = $studentId;
                }
            }
        }

        $response = ['message' => ''];

        if ($newlyInsertedCount > 0) {
            $response['message'] .= $newlyInsertedCount . ' სტუდენტი(ბი) ჩაემატნენ უწყისში. ';
        }

        if (!empty($existingStudentIds)) {
            $response['message'] .= 'ზოგიერთი სტუდენტი უკვე არსებობს მიმდინარე უწყისში.';
            $response['existing_student_ids'] = $existingStudentIds;
        }

        return response()->json($response);
    }

    public function syllabi(SyllabusFilter $filter): array
    {
        $student = Student::whereUserId(\Auth::id())
            ->first();
        if (!$student) {
            return [
                'code' => 404,
                'message' => 'Student not found!'
            ];
        }
        $flow = $student->learn_year_id;
        $data = Syllabus::filter($filter)
            ->select([
                'id', 'name', 'name_en', 'semester_id', 'code', 'credits',
                'contact_hours', 'lecture_hours', 'total_hours', 'independent_work_hours',
                'seminar_hours', 'status_id', 'learn_year_id', 'academic_degree_id',
                'is_external'
            ])
            ->with([
                'semester:id,name',
                'lecturers:id,first_name,last_name',
                'learnYear:id,name',
                'status:id,name_ka,name_en',
                'prerequisites:id,name,name_en,code',
                'academicDegree:id,name_ka,name_en',
            ])->where('learn_year_id', $flow)->orderBy('semester_id')->orderBy('status_id')->get();
        return [
            'code' => 200,
            'syllabi' => $data
        ];
    }

    public function viewLecturer(Lecturer $id)
    {
        return response()->json($id);
    }

    public function viewStudentSubjects(Request $request, int $id, StudentService $studentService): JsonResponse
    {

        $studentSubjects = $studentService->studentSubjects($request->semester_id, $id);
        return response()->json($studentSubjects);
    }

    public function viewStudentMissedLectures(
        int            $id,
        int            $syllabusId,
        StudentService $studentService
    ): JsonResponse
    {
        $missedLectures = $studentService->missedLectures($syllabusId, $id);
        return response()->json($missedLectures);
    }

    public function allStudentFinanceStatusLog(StudentFinanceStatusLogFilter $filter, Request $request): BinaryFileResponse|JsonResponse
    {
        $logs = FinanceStudentStatusLog::filter($filter)->with(['student.program','student' => function($student){
                $student->select('id', 'name', 'surname', 'personal_id', 'status_id', 'user_id', 'program_id');
            }])
            ->orderByDesc('id')
            ->get()
            ->unique('student_id');

        return $request->export == 1 ? Excel::download(new FinanceStatusLogExport($logs), 'finance-status-update.xlsx') : response()->json($logs);
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function studentSyllabusPoints(Request $request)
    {
        $request = $request->validate([
            'learn_year_id' => 'required',
            'flow_id' => 'required',
        ]);

        $fileName = 'student-syllabus-points' . Str::random(8) . '.xlsx';
        Excel::store(
            new SyllabusPointsExport($request['learn_year_id'], $request['flow_id']),
            $fileName,
            config('excel.storage_registry')
        );

        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

    public function calculateGpa(Request $request)
    {
        $request->validate([
            'program_id' => 'required|exists:programs,id'
        ]);

        Student::query()
            ->select(['id', 'program_id'])
            ->with(['syllabi.student' => function($student){
                $student->select(['id','program_id']);
            }, 'syllabi.syllabus' => function($syllabus){
                $syllabus->select(['id','credits']);
            },'syllabi' => function($history){
                $history->where('is_passed', 1)
                        ->where('is_closed', 1);
            }])
            ->whereHas('syllabi', function ($syllabi) {
                return $syllabi->where('is_passed', 1);
            })
            ->whereHas('syllabi.syllabus')
            ->where('program_id', $request->post('program_id'))
            ->get()
            ->map(function ($student){
                $histories = $student->syllabi->map(function ($history){
                    $point = $history->point;
                    $weight = 0;
                    if ($point >= 90.5){
                        $weight = 4;
                    } elseif ($point >= 80.5) {
                        $weight = 3.2;
                    } elseif ($point >= 70.5) {
                        $weight = 2.4;
                    } elseif ($point >= 60.5) {
                        $weight = 1.6;
                    } elseif ($point >= 50.5) {
                        $weight = 0.8;
                    }

                    $credit = null;
                    try {
                        $credit = $history->credits ?? $history->syllabus->credits ?? 0;
                        if ($credit === 0 && ($history->credits === null && $history->syllabus->credits === null)) {
                            \Log::warning('Null credits detected', [
                                'student_id' => $history->student->id,
                                'syllabus_id' => $history->syllabus->id ?? 'N/A',
                                'history_id' => $history->id
                            ]);
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error accessing credits', [
                            'student_id' => $history->student->id,
                            'syllabus_id' => $history->syllabus->id ?? 'N/A',
                            'history_id' => $history->id,
                            'error' => $e->getMessage()
                        ]);
                        $credit = 0;
                    }

                    return [
                        'weighted_credit' => $weight * $credit,
                        'credit' => $credit
                    ];
                });

                $totalCredits = $histories->sum('credit');
                $gpa = $totalCredits > 0 ? round($histories->sum('weighted_credit') / $totalCredits, 3) : 0;
                $gpaFormatted = number_format($gpa, 3, '.', '');
                $student->update(['gpa' => $gpaFormatted]);
            });

        return response()->json([
            'status' => 200,
            'message' => 'GPA-ის გამოთვლა წარმატებით შესრულებუდა',
            'success' => true
        ]);
    }

    public function syllabusHistories(Request $request): JsonResponse
    {
        $histories = StudentSyllabusHistory::query()
            ->with(['syllabus' => function($syllabus){
                $syllabus->select(['id', 'semester_id', 'name', 'name_en', 'credits', 'code']);
            }])
            ->join('syllabi', 'student_syllabus_history.syllabus_id', '=', 'syllabi.id')
            ->whereHas('syllabus')
            ->where('is_closed', 1)
            ->where('student_id', Student::query()->where('user_id', auth()->id())->first()->id)
            ->orderBy('syllabi.semester_id')
            ->get()
//            ->groupBy(function ($item) {
//                return $item->syllabus->semester_id;
//            })
//            ->map(function ($history){
//                return $history->map(function($map){
//                    $point = round($map->point);
//                    if ($point >= 91) {
//                        $pointMarker = 'A';
//                    }elseif($point>=81){
//                        $pointMarker = 'B';
//                    }elseif($point>=71){
//                        $pointMarker = 'C';
//                    }elseif($point>=61){
//                        $pointMarker = 'D';
//                    }elseif($point>=51){
//                        $pointMarker = 'E';
//                    }else{
//                        $pointMarker = 'F';
//                    }
//
//                    return [
//                        'semester' => $map->syllabus->semester_id,
//                        'code' => $map->syllabus->code,
//                        'name' => $map->syllabus->name,
//                        'name_en' => $map->syllabus->name_en ?? '',
//                        'credit' => $map->credits ?? $map->syllabus->credits,
//                        'point' => $map->point,
//                        'point_marker' => $pointMarker
//                    ];
//                });
//            })
        ;

        return response()->json([
            'status' => 200,
            'success' => true,
            'data' => $histories
        ]);
    }

    public function studentAgreement($id)
    {
        $data = RegisterFormInfo::where('registerable_id',$id)->with('registerable')->first();
        return view('agreement.student', compact('data'));
    }

    public function enrolledStudentAgreement($id)
    {
        $data = Student::findOrFail($id);
        return view('agreement.enrolled_student', compact('data'));
    }

    public function minors(Request $request)
    {
        $getLearnYearIds = $request->input('learn_year_ids');
        $learnYearIds = $getLearnYearIds
            ? explode(',', $request->input('learn_year_ids'))
            : $getLearnYearIds
        ;

        $students = Student::query()
            ->when($learnYearIds, function ($query, $value) {
                $query->whereIn('learn_year_id', $value)->where('learn_year_id', '!=', 0);
            })
            ->whereDate('birthday', '>=', Carbon::now()->subYears(18))
            ->get()
        ;

        return view('student.minor', compact('students'));
    }

    public function downloadDiploma($lang, Request $request)
    {
//        $lang -> geo / eng
        $student = Student::query()->find($request->student_id);


        $fileName = $student->name.'-'.$student->surname."-diploma-supplement-$lang.doc";

        $headers = array(
            "Content-type"=>"text/html",
            "Content-Disposition"=>"attachment;Filename=$fileName"
        );

//    return view('diploma.diploma', ['student' => $student]);

        $blade = $lang == 'geo' ? 'diploma.diploma' : 'diploma.engDiploma';

        $html = view($blade, compact('student'));

        return response()->make($html,200, $headers);
    }

    public function voteMinor(Request $request)
    {
        $now = Carbon::now();
        $targetDate = Carbon::create(2025, 6, 21); // 2025-06-18

        if ($now->lessThan($targetDate)) {
            $minorId = $request->minor_id;
            $flowId = Setting::query()->where('key', 'current_semester')->first()->value;

            $student = Student::query()->where('user_id', auth()->id())->first();

            $student->update(['minor_id' => $minorId]);

            StudentMinorLog::query()->updateOrCreate([
                'student_id' => $student->id,
                'flow_id' => $flowId
            ],[
                'minor_id' => $minorId,
            ]);

            return response()->json(['status' => 200, 'success' => true]);

        } else {
            return response()->json(['status' => 200, 'success' => false, 'error' => 'დრო გავიდა']);
        }

    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function minorLogs(Request $request)
    {
        $isExport = $request->is_export;

        $minors = StudentMinorLog::query()
            ->with('student')
            ->when($request->flow_id, function ($query, $value){
                $query->where('flow_id', $value);
            })
            ->when($request->keyword, function ($query, $keyword) {
                $query
                    ->whereHas('student', function ($query) use ($keyword) {
                        $query
                            ->where('name', 'like', '%' . $keyword . '%')
                            ->orWhere('surname', 'like', '%' . $keyword . '%')
                            ->orWhere('personal_id', 'like', '%' . $keyword . '%')
                        ;
                    })
                    ->orWhere('minor_id', $keyword)
                ;

            })
        ;

        if ($isExport)
        {
            return Excel::download(new MinorExport($minors->get()), 'minor-export.xlsx');
        }

        //saxeli gvari piradoba mimatuleba semestri da tarigi
        $paginated = $minors->paginate(10);
        $paginated->getCollection()->transform(function ($item) {
            $minorId = $item->minor_id;

            $minorName = match($minorId) {
                1 => 'ფსიქოლოგია',
                2 => 'საერთაშორისო ურთიერთობები',
                3 => 'საჯარო მმართველობა',
                default => 'უცნობი',
            };

            return [
                'id' => $item->id,
                'first_name' => $item->student->name,
                'last_name' => $item->student->surname,
                'personal_id' => $item->student->personal_id,
                'minor' => $minorName,
                'flow' => $item?->flow?->name ?? '',
                'created_at' => $item->created_at,
            ];
        });
        return $paginated;
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function minorExport(Request $request)
    {
        $minors = StudentMinorLog::query()
            ->when($request->flow_id, function ($query, $value){
                $query->where('flow_id', $value);
            })
            ->with('flow', 'student')
            ->get()
        ;

        return Excel::download(new MinorExport($minors), 'minor-export.xlsx');
    }
}
