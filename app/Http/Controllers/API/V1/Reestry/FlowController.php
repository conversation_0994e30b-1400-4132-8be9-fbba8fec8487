<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Filters\FlowFilter;
use App\Filters\ProgramFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Flow\StoreRequest;
use App\Http\Requests\Reestry\Flow\UpdateRequest;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Program\Program;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class FlowController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:learn-years.index'])->only(['index']);
        $this->middleware(['permission:learn-years.store'])->only(['store']);
        $this->middleware(['permission:learn-years.show'])->only(['show']);
        $this->middleware(['permission:learn-years.update'])->only(['update']);
        $this->middleware(['permission:learn-years.destroy'])->only(['destroy']);
    }

    public function index(): Response
    {
        return response([
            'learnYears' => Flow::OrderByDesc('id')->paginate(30),
            'programs' => Program::filter(new ProgramFilter(request()))->pluck('name_ka', 'id')
        ]);
    }

    public function store(StoreRequest $request): Response
    {
        $program = Flow::create($request->validated());
        return response($program);
    }

    public function show(int $flow): Response
    {
        return response(
            Flow::with(['program'])->findOrFail($flow)
        );
    }

    public function update(UpdateRequest $request, int $flow): Response
    {
        $flow = Flow::find($flow);
        $flow->update($request->validated());
        return response($flow->load(['program']));
    }

    public function destroy(int $flow): Response
    {
        Flow::find($flow)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

}
