<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\SchoolExport;
use App\Filters\SchoolFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\School\StoreRequest;
use App\Http\Requests\Reestry\School\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Campus;
use App\Models\Reestry\School;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class SchoolController extends Controller
{
    public function __construct()
    {
        //$this->middleware(['permission:schools.index'])->only(['index']);
        $this->middleware(['permission:schools.store'])->only(['store']);
        $this->middleware(['permission:schools.show'])->only(['show']);
        $this->middleware(['permission:schools.update'])->only(['update']);
        $this->middleware(['permission:schools.destroy'])->only(['destroy']);
        $this->middleware(['permission:schools.export'])->only(['exportExcel']);
    }

    /**
     * @OA\Get(
     * path="/schools",
     * tags={"Schools"},
     * summary="List of schools",
     * description="List of schools",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *
     *                     @OA\Property(
     *                         property="campus_id",
     *                         type="integer",
     *                         description="related object",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "address_en" : "test address en",
     *                        "address_ka" : "test address ka",
     *                        "campus_id" : 1,
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(SchoolFilter $filter): Response
    {
        return response([
            'schools' => School::filter($filter)->with(['campus:id,name_ka,name_en'])
                ->OrderBy('name_ka')
                ->paginate(30),
            'campuses' => Campus::pluck('name_ka', 'id')]);
    }

    /**
     * @OA\Post(
     * path="/schools",
     * tags={"Schools"},
     * summary="Add School",
     * description="Add School",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreProgramRequest",
     *               required={"name_en","name_ka","campus_id"},
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="campus_id", type="integer"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Program created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */


    public function store(StoreRequest $request): Response
    {
        $school = School::create($request->validated());
        return response($school->load(['campus']), Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/schools/{id}",
     * tags={"Schools"},
     * summary="Show school",
     * description="Show school",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="campus_id",
     *                         type="integer",
     *                         description="Campus relation",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "campus" : "related object",
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show(int $school): Response
    {
        return response(
            School::with(['campus:id,name_ka,name_en'])
                ->findOrFail($school)
        );
    }

    /**
     * @OA\Put(
     * path="/schools/{id}",
     * tags={"Schools"},
     * summary="Update School",
     * description="Update School",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *   )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateSchoolRequest",
     *               required={"name_en","name_ka","campus_id"},
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="campus_id", type="integer")
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="School updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, int $school): Response
    {
        $school = School::findOrFail($school);
        $school->update($request->validated());
        return response($school->load(['campus']));
    }

    /**
     * @OA\Delete(
     * path="/schools/{id}",
     * tags={"Schools"},
     * summary="Delete school",
     * description="Delete school",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete school",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy(int $school): Response
    {
        School::findOrFail($school)->deleteOrFail();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, SchoolFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $schools = School::filter($filter)->with(['campus'])->get();
        $fileName = 'school-' . Str::random(8) . '.xlsx';
        Excel::store(
            new SchoolExport($columns, $schools),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }
}
