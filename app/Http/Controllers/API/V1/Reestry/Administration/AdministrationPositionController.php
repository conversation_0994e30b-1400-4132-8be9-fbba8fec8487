<?php

namespace App\Http\Controllers\API\V1\Reestry\Administration;

use App\Exports\Administration\AdministrationPositionExport;
use App\Filters\Administration\AdministrationPositionFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\AdministrationPosition\StoreRequest;
use App\Http\Requests\Reestry\AdministrationPosition\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Administration\AdministrationPosition;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class AdministrationPositionController extends Controller
{


    /**
     * @OA\Get(
     * path="/administration-positions",
     * tags={"Administration Positions"},
     * summary="List of administration positions",
     * description="List of administrator positions",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */
    public function __construct()
    {
        $this->middleware(['permission:administration-positions.index'])->only(['index']);
        $this->middleware(['permission:administration-positions.store'])->only(['store']);
        $this->middleware(['permission:administration-positions.show'])->only(['show']);
        $this->middleware(['permission:administration-positions.update'])->only(['update']);
        $this->middleware(['permission:administration-positions.destroy'])->only(['destroy']);
        $this->middleware(['permission:administration-positions.export'])->only(['exportExcel']);
    }
    public function index(AdministrationPositionFilter $filter): Response
    {
        return response(AdministrationPosition::filter($filter)
            ->paginate(30), Response::HTTP_OK);
    }

    /**
     * @OA\Post(
     * path="/administration-positions",
     * tags={"Administration Positions"},
     * summary="Add Administration Position",
     * description="Add Administration Position",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreCampusRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Administration Position created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $administationPosition = AdministrationPosition::create($request->validated());
        return response($administationPosition, Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/administration-positions/{id}",
     * tags={"Administration Positions"},
     * summary="Show administration position",
     * description="Show administration position",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show(int $administrationPosition): Response
    {
        return response(
            AdministrationPosition::findOrFail($administrationPosition),
            Response::HTTP_OK
        );
    }

    /**
     * @OA\Put(
     * path="/administration-positions/{id}",
     * tags={"Administration Positions"},
     * summary="Update administration position",
     * description="Update administration position",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *      )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateCampusRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Administration position updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, int $administrationPosition): Response
    {
        $administrationPosition = AdministrationPosition::findOrFail($administrationPosition);
        $administrationPosition->update($request->validated());
        return response($administrationPosition, Response::HTTP_OK);
    }

    /**
     * @OA\Delete(
     * path="/administration-positions/{id}",
     * tags={"Administration Positions"},
     * summary="Delete administration position",
     * description="Delete administration position",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete administration position",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy(int $administrationPosition): Response
    {
        AdministrationPosition::findOrFail($administrationPosition)
            ->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, AdministrationPositionFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $administrationPositions = AdministrationPosition::filter($filter)->get();
        $fileName = 'administration-position-' . random_int(50000,100000) . config('excel.extension');
        Excel::store(
            new AdministrationPositionExport($columns, $administrationPositions),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }

}
