<?php

namespace App\Http\Controllers\API\V1\Reestry\Administration;

use App\Exports\Administration\AdministrationExport;
use App\Filters\Administration\AdministrationFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Administration\StoreRequest;
use App\Http\Requests\Reestry\Administration\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Administration\AdministrationItem;
use App\Models\Reestry\Administration\AdministrationPosition;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Role;
use App\Models\RoleUser;
use App\Models\User\User;
use App\Models\User\UserType;
use App\Models\UserProgram;
use App\Services\ImageService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class AdministrationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:administrations.index'])->only(['index']);
        $this->middleware(['permission:administrations.store'])->only(['store']);
        $this->middleware(['permission:administrations.show'])->only(['show']);
        $this->middleware(['permission:administrations.update'])->only(['update']);
        $this->middleware(['permission:administrations.destroy'])->only(['destroy']);
        $this->middleware(['permission:administrations.export'])->only(['exportExcel']);
    }
    const photoPath = '/photos/administration';
    const cvPath = '/cvs/administration';

    public function index(AdministrationFilter $filter): Response
    {
        return response([
            'administrations' => Administration::filter($filter)
                ->with([
                    'administrationPosition:id,name_ka',
                    'administrationItem:id,name_ka',
                    'school:id,name_ka',
                    'role',
                    'userPrograms'
                ])
                ->OrderByDesc('id')
                ->paginate(30),
            'administrationPositions' => AdministrationPosition::pluck('name_ka', 'id'),
            'administrationItems' => AdministrationItem::pluck('name_ka', 'id'),
            'schools' => School::pluck('name_ka', 'id'),
            'programs' => Program::pluck('name_ka', 'id'),
            'roles' => Role::pluck('title', 'id')
        ], Response::HTTP_OK);
    }

    public function store(StoreRequest $request, ImageService $imageService): Response
    {
        $user = [
            'email' => $request->email,
            'name' => $request->first_name,
            'password' => bcrypt('gipa2023'),
            'user_type_id' => UserType::ADMINISTRATION
        ];
        $createdUser = User::create($user);
        $pathName = '/administrations/' . $request->identity_number . '/profile';
        $validated = $request->validated();
        if (isset($request->photo)) {
            $photo = $imageService->upload(file: $request->photo, path: $pathName);
            $validated['photo'] = $photo;
        }
        if (isset($request->cv)) {
            $cv = $imageService->upload(file: $request->cv, path: $pathName);
            $validated['cv'] = $cv;
        }
        $validated['user_id'] = $createdUser->id;
        $administration = Administration::create($validated);
        if($request->has('role_id')){
            $roleUser = new RoleUser([
                'user_id' => $createdUser->id,
                'role_id' => $request->role_id,
            ]);
            $roleUser->save();
        }
        else{
            $roleUser = new RoleUser([
                'user_id' => $createdUser->id,
                'role_id' => 7,
            ]);
            $roleUser->save();
        }
        if($request->has('program_ids')){
            $programIds = $request->post('program_ids');
            foreach ($programIds as $programId) {
                UserProgram::create([
                    'user_id' => $createdUser->id,
                    'program_id' => $programId
                ]);
            }
        }
        return response($administration->load(['administrationPosition', 'administrationItem', 'school','role']), Response::HTTP_CREATED);
    }

    public function show(int $administration): Response
    {
        return response(
            Administration::
            with(['administrationPosition', 'administrationItem', 'school','role','userPrograms'])
                ->findOrFail($administration),
            Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, int $administration, ImageService $imageService): Response
    {
        $administration = Administration::findOrFail($administration);
        $pathName = '/administrations/' . $request->identity_number . '/profile';
        $photo = $request->hasFile('photo') ?
            $imageService->upload(file: $request->photo, path: $pathName)
            : $request->photo;
        if ($request->hasFile('photo') && isset($administration->photo)) {
            Storage::delete($administration->photo);
        }
        if ($request->hasFile('cv') && isset($administration->cv)) {
            Storage::delete($administration->cv);
        }
        $cv = $request->hasFile('cv') ? $imageService->upload(file: $request->cv, path: $pathName) : $request->cv;
        $validated = $request->validated();
        $validated['cv'] = $cv;
        $validated['photo'] = $photo;
        if ($request->has('email')) {
            $user = User::where('id', $administration->user_id)->first();
            if ($user) {
                $user->email = $request->email;
                $user->save();
            }
        }
        $administration->update($validated);
        $roleUser = RoleUser::where('user_id', $administration->user_id)->first();
        if ($request->has('role_id')) {
            $roleUser->role_id = $request->role_id;
        } else {
            $roleUser->role_id = 1;
        }
        $roleUser->save();
        if($request->has('program_ids')){
            $programIds = $request->post('program_ids');
            UserProgram::where('user_id', $administration->user_id)->delete();
            foreach ($programIds as $programId) {
                UserProgram::create([
                    'user_id' => $administration->user_id,
                    'program_id' => $programId
                ]);
            }
        }
        return response($administration->load(['administrationPosition', 'administrationItem', 'school','role', 'userPrograms']), Response::HTTP_OK);
    }


    public function destroy(int $administration): Response
    {
        $administration = Administration::findOrFail($administration);
        if (isset($administration->cv)) {
            Storage::delete($administration->cv);
        }
        if (isset($administration->photo)) {
            Storage::delete($administration->photo);
        }
        $user = $administration->user;
        if ($user) {
            $user->delete();
        }
        $administration->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, AdministrationFilter $filters): string
    {
        $columns = json_decode($request->columns);
        $administrations = Administration::filter($filters)
            ->with(['administrationPosition', 'administrationItem', 'school'])->get();
        $fileName = 'administrations-' . random_int(50000, 100000) . '.xlsx';
        Excel::store(
            new AdministrationExport($columns, $administrations),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }


}
