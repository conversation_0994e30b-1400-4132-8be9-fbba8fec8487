<?php

namespace App\Http\Controllers\API\V1\Reestry\Administration;

use App\Exports\AdministrationItemExport;
use App\Filters\Administration\AdministrationItemFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\AdministrationItem\StoreRequest;
use App\Http\Requests\Reestry\AdministrationItem\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Administration\AdministrationItem;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class AdministrationItemController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:administration-items.index'])->only(['index']);
        $this->middleware(['permission:administration-items.store'])->only(['store']);
        $this->middleware(['permission:administration-items.show'])->only(['show']);
        $this->middleware(['permission:administration-items.update'])->only(['update']);
        $this->middleware(['permission:administration-items.destroy'])->only(['destroy']);
        $this->middleware(['permission:administration-items.export'])->only(['exportExcel']);
    }

    /**
     * @OA\Get(
     * path="/administration-items",
     * tags={"Administration Items"},
     * summary="List of administration items",
     * description="List of administrator items",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(AdministrationItemFilter $filter): Response
    {
        return response(AdministrationItem::filter($filter)->paginate(30));
    }

    /**
     * @OA\Post(
     * path="/administration-items",
     * tags={"Administration Items"},
     * summary="Add Administration Item",
     * description="Add Administration Item",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreAdministrationItemRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Administration Item created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $administrationItem = AdministrationItem::create($request->validated());
        return response($administrationItem, Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/administration-items/{id}",
     * tags={"Administration Items"},
     * summary="Show administration item",
     * description="Show administration item",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */


    public function show(int $administrationItem): Response
    {
        return response(AdministrationItem::findOrFail($administrationItem));
    }

    /**
     * @OA\Put(
     * path="/administration-items/{id}",
     * tags={"Administration Items"},
     * summary="Update administration item",
     * description="Update administration item",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *      )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateAdministrationItemRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Administration item updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */


    public function update(UpdateRequest $request, int $administrationItem): Response
    {
        $administrationItem = AdministrationItem::findOrFail($administrationItem);
        $administrationItem->update($request->validated());
        return response($administrationItem);
    }

    /**
     * @OA\Delete(
     * path="/administration-items/{id}",
     * tags={"Administration Items"},
     * summary="Delete administration item",
     * description="Delete administration item",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete administration item",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     */

    public function destroy(int $administrationItem): Response
    {
        AdministrationItem::findOrFail($administrationItem)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, AdministrationItemFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $administrationItems = AdministrationItem::filter($filter)->get();
        $fileName = 'administration-items' . Str::random(8) . '.xlsx';
        Excel::store(
            new AdministrationItemExport($columns, $administrationItems),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }
}
