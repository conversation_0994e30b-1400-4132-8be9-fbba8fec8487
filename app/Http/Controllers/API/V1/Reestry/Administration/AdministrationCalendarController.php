<?php

namespace App\Http\Controllers\API\V1\Reestry\Administration;

use App\Filters\Administration\AdministrationLectureFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Curriculum\LectureUpdateRequest;
use App\Http\Resources\Administration\CalendarEventResource;
use App\Http\Resources\Administration\CalendarLectureResource;
use App\Http\Resources\Administration\CalendarLectureResourceEnglish;
use App\Models\Event;
use App\Models\Lectures\Lecture;
use Carbon\Carbon;
use Illuminate\Http\Response;

class AdministrationCalendarController extends Controller
{
    public function __construct()
    {
        //$this->middleware(['permission:calendar.index'])->only(['index']);
        $this->middleware(['permission:calendar.update'])->only(['updateLecture']);
    }
    public function index(AdministrationLectureFilter $filter)
    {
        $lectures = Lecture::with([
            'syllabus.learnYear.program',
            'lecturer',
            'auditorium.campus',
        ])
            ->filter($filter)
            ->where('is_current', 1)
            ->whereDate('lecture_date', '>=', '2025-03-01')
            ->get();

        return response([
            'lectures' => CalendarLectureResource::collection($lectures),
            'events'   => CalendarEventResource::collection(Event::all()),
        ]);
    }

    public function indexEn(AdministrationLectureFilter $filter)
    {
        $lectures = Lecture::with([
            'syllabus.learnYear.program',
            'lecturer',
            'auditorium.campus',
        ])
            ->filter($filter)
            ->where('is_current', 1)
            ->whereDate('lecture_date', '>=', '2025-03-01')
            ->get();

        return response([
            'lectures' => CalendarLectureResourceEnglish::collection($lectures),
            'events'   => CalendarEventResource::collection(Event::all()),
        ]);
    }

    public function updateLecture(LectureUpdateRequest $request, int $lectureId)
    {
        $request->validated();

        $lecture = Lecture::findOrFail($lectureId);

        if ($request->has('lecturer_id') && $request->get('lecturer_id')) {
            $lecture->lecturer_id = $request->lecturer_id;
        }

        if ($request->has('auditorium_id') && $request->get('auditorium_id')) {
            $lecture->auditorium_id = $request->auditorium_id;
        }

        if ($request->has('start') && $request->get('start')) {
            $lecture->lecture_date = Carbon::createFromFormat('d-m-Y H:i', $request->start)->format('Y-m-d');
            $lecture->start_time   = Carbon::createFromFormat('d-m-Y H:i', $request->start)->format('H:i');
        }

        if ($request->has('end') && $request->get('end')) {
            $lecture->end_time = Carbon::createFromFormat('d-m-Y H:i', $request->end)->format('H:i');
        }

        $lecture->save();

        $lecture->load([
            'syllabus.learnYear.program',
            'lecturer',
            'auditorium.campus',
        ]);

        return response(CalendarLectureResource::make($lecture), Response::HTTP_CREATED);
    }
}
