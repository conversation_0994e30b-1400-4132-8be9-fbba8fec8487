<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\CampusExport;
use App\Filters\CampusFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Campuse\StoreRequest;
use App\Http\Requests\Reestry\Campuse\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Campus;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class CampusController extends Controller
{
    public function __construct()
    {
        //$this->middleware(['permission:campuses.index'])->only(['index']);
        $this->middleware(['permission:campuses.store'])->only(['store']);
        $this->middleware(['permission:campuses.show'])->only(['show']);
        $this->middleware(['permission:campuses.update'])->only(['update']);
        $this->middleware(['permission:campuses.destroy'])->only(['destroy']);
        $this->middleware(['permission:campuses.export'])->only(['exportExcel']);
    }

    /**
     * @OA\Get(
     * path="/campuses",
     * tags={"Campus"},
     * summary="List of campuses",
     * description="List of campuses",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "address_ka" : "address ka",
     *                        "address_en" : "address en"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(CampusFilter $filter): Response
    {
        return response([
            "campuses" => Campus::filter($filter)->OrderByDesc('id')->paginate(30)
        ]);

    }

    /**
     * @OA\Post(
     * path="/campuses",
     * tags={"Campus"},
     * summary="Add Campuse",
     * description="Add Campuse",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreCampusRequest",
     *               required={"name_en","name_ka","address_ka","address_en"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="address_ka", type="string"),
     *               @OA\Property(property="address_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Campus created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $campus = Campus::create($request->validated());
        return response($campus, Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/campuses/{id}",
     * tags={"Campus"},
     * summary="Show campus",
     * description="Show campus",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="address_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka",
     *                        "address_ka" : "address ka",
     *                        "address_en" : "address en"
     *                     }
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */
    public function show(int $campus): Response
    {
        return response(Campus::findOrFail($campus));
    }

    /**
     * @OA\Put(
     * path="/campuses/{id}",
     * tags={"Campus"},
     * summary="Update Campuse",
     * description="Update Campuse",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *      )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateCampusRequest",
     *               required={"name_en","name_ka","address_en","address_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *               @OA\Property(property="address_ka", type="string"),
     *               @OA\Property(property="address_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Campus updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */
    public function update(UpdateRequest $request, int $campus): Response
    {
        $campus = Campus::findOrFail($campus);
        $campus->update($request->validated());
        return response($campus);
    }

    /**
     * @OA\Delete(
     * path="/campuses/{id}",
     * tags={"Campus"},
     * summary="Delete campus",
     * description="Delete campus",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete campus",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy(int $campus): Response
    {
        Campus::findOrFail($campus)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, CampusFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $campuses = Campus::filter($filter)->get();
        $fileName = 'campuses-' . Str::random(8) . '.xlsx';
        Excel::store(
            new CampusExport($columns, $campuses),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }

}
