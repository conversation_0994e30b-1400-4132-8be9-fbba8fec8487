<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\LearnYearExport;
use App\Filters\LearnYearFilter;
use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\LearnYear\StoreRequest;
use App\Http\Requests\Reestry\LearnYear\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Request;
use function config;
use function response;

class LearnYearController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:flows.index'])->only(['index']);
        $this->middleware(['permission:flows.store'])->only(['store']);
        $this->middleware(['permission:flows.show'])->only(['show']);
        $this->middleware(['permission:flows.update'])->only(['update']);
        $this->middleware(['permission:flows.destroy'])->only(['destroy']);
        $this->middleware(['permission:flows.export'])->only(['exportExcel']);
    }

    public function index(LearnYearFilter $filter): Response
    {
        $learnYears = LearnYear::filter($filter)->with(['program.academicDegree'])
            ->OrderByDesc('name')
            ->paginate(30);

        foreach ($learnYears as $learnYear) {
            $academicDegreeId = $learnYear->program->academicDegree->url ?? 'null';
            $url = "https://portal.gipa.ge/{$academicDegreeId}/{$learnYear->hash}";
            $learnYear->url = $url;
        }

        $programs = Program::filter(new ProgramFilter(request()))->pluck('name_ka', 'id');
        $schools = School::filter(new SchoolFilter(request()))->pluck('name_ka', 'id');

        return response([
            'learnYears' => $learnYears,
            'programs' => $programs,
            'schools' => $schools,
        ]);
    }

    public function store(StoreRequest $request): Response
    {
        $idHash = md5($request->id);
        $data = $request->validated();
        $data['hash'] = $idHash;
        $learnYear = LearnYear::create($data);
        return response($learnYear->load(['program.academicDegree']), Response::HTTP_CREATED);
    }

    public function show(int $learnYear): Response
    {
        return response(
            LearnYear::with(['program'])
                ->find($learnYear)
        );
    }

    public function update(UpdateRequest $request, int $learnYear): Response
    {
        $learnYear = LearnYear::findOrFail($learnYear);
        $learnYear->update($request->validated());
        return response($learnYear->load(['program']));
    }

    public function destroy(int $learnYear): Response
    {
        LearnYear::findOrFail($learnYear)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, LearnYearFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $learnYears = LearnYear::filter($filter)->with(['program'])->get();
        $fileName = 'learn-years-' . Str::random(8) . '.xlsx';
        Excel::store(
            new LearnYearExport($columns, $learnYears),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }
}
