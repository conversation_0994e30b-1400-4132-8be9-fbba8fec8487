<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\DirectionExport;
use App\Filters\DirectionFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Direction\StoreRequest;
use App\Http\Requests\Reestry\Direction\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Direction;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class DirectionController extends Controller
{

    /**
     * @OA\Get(
     * path="/directions",
     * tags={"Directions"},
     * summary="List of directions",
     * description="List of directions",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(DirectionFilter $filter): Response
    {
        return response(Direction::filter($filter)->OrderByDesc('id')->paginate(10));
    }

    /**
     * @OA\Post(
     * path="/directions",
     * tags={"Directions"},
     * summary="Add Direction",
     * description="Add Direction",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreDirectionRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Direction created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */


    public function store(StoreRequest $request): Response
    {
        $direction = Direction::create($request->validated());
        return response($direction, Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/directions/{id}",
     * tags={"Directions"},
     * summary="Show direction",
     * description="Show direction",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="name_en",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     @OA\Property(
     *                         property="name_ka",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name_en" : "test name en",
     *                        "name_ka" : "test name ka"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show(int $direction): Response
    {
        return response(Direction::findOrFail($direction));
    }

    /**
     * @OA\Put(
     * path="/directions/{id}",
     * tags={"Directions"},
     * summary="Update Direction",
     * description="Update Direction",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *      )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateDirectionRequest",
     *               required={"name_en","name_ka"},
     *               @OA\Property(property="name_ka", type="string"),
     *               @OA\Property(property="name_en", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Direction updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, int $direction): Response
    {
        $direction = Direction::findOrFail($direction);
        $direction->update($request->validated());
        return response($direction);
    }

    /**
     * @OA\Delete(
     * path="/directions/{id}",
     * tags={"Directions"},
     * summary="Delete direction",
     * description="Delete direction",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete direction",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy(int $direction): Response
    {
        Direction::findOrFail($direction)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, DirectionFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $directions = Direction::filter($filter)->get();
        $fileName = 'directions-' . Str::random(8) . '.xlsx';
        Excel::store(
            new DirectionExport($columns, $directions),
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }
}
