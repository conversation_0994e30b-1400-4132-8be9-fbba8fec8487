<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Filters\Student\ApplicantsFilter;
use App\Filters\Student\StudentFilter;
use App\Http\Controllers\API\V1\RegisterForms\BachelorRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\DoctorRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\MasterRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\ProffesionRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\TrainingRegisterController;
use App\Http\Controllers\Controller;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentBasicsOfEnrollment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\RegisterForms\BachelorRegister;
use App\Models\RegisterForms\MasterRegister;
use App\Models\RegisterForms\TrainingRegister;
use App\Models\User\User;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Schema;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Validator;

class ApplicantsController extends Controller
{
    public function checkRouteForFilter(Request $request)
    {
        if ($request->type == 'bachelor') {
            $class = new BachelorRegisterController();
        }
        if ($request->type == 'master') {
            $class = new MasterRegisterController();
        }
        if ($request->type == 'phd') {
            $class = new DoctorRegisterController();
        }
        if ($request->type == 'tcc') {
            $class = new TrainingRegisterController();
        }
        if ($request->type == 'hse') {
            $class = new ProffesionRegisterController();
        }
        $request->merge(['flow_id' => $request->flow_id]);
        return $class->index(new ApplicantsFilter($request));
    }

    public function checkUrl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'hash' => 'required|exists:learn_years,hash',
        ]);
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }
        $data = LearnYear::where('hash', $request->hash)->first();
        $response = $data->id;
        return response($response);
    }

    public function copyToStudentsTable(Request $request)
    {
        $applicantId = $request->applicant_id;
        $statusId = $request->status_id;
        $groupId = $request->group_id;
        if ($request->type == 'bachelor') {
            $response = $this->copyApplicantDataToStudent($applicantId, BachelorRegister::class, $statusId, $groupId);
        }
        if ($request->type == 'master') {
            $response = $this->copyApplicantDataToStudent($applicantId, MasterRegister::class, $statusId, $groupId);
        }
        if ($request->type == 'phd') {
            $response = $this->copyApplicantDataToStudent($applicantId, BachelorRegister::class, $statusId, $groupId);
        }
        if ($request->type == 'tcc') {
            $response = $this->copyApplicantDataToStudent($applicantId, TrainingRegister::class, $statusId, $groupId);
        }
        if ($request->type == 'hse') {
            $response = $this->copyApplicantDataToStudent($applicantId, BachelorRegister::class, $statusId, $groupId);
        }
        return response()->json($response);
    }

    public function copyApplicantDataToStudent($applicantId, $modelClass, $statusId, $groupId)
    {
        // Retrieve the applicant data
        if($modelClass == TrainingRegister::class){
            $applicant = $modelClass::with('registerFormInfo','address')->findOrFail($applicantId);
            $address = $applicant->address;
        } else {
            $applicant = $modelClass::with('registerFormInfo')->findOrFail($applicantId);
        }
        // Retrieve the related register_form_info data
        $registerFormInfo = $applicant->registerFormInfo;

        // Create a new user record
        $user = new User;
        $user->name = $registerFormInfo->first_name . ' ' . $registerFormInfo->last_name;
        $user->email = $registerFormInfo->email;
        $user->password = Hash::make('gipa2025');
        $user->user_type_id = 3; // Set the user type id based on your application logic
        $user->save();

        // Create a new student record
        $student = new Student;

        // Loop through the applicant model attributes
        foreach ($applicant->getAttributes() as $attribute => $value) {
            // Check if the attribute exists in the student model
            if (Schema::hasColumn('students', $attribute) && $attribute !== 'id') {
                $student->$attribute = $value;
            }
        }
        $school = Program::where('id', $applicant->program_id)->first();

        $student->user_id = $user->id;
        $student->name = $registerFormInfo->first_name;
        $student->name_en = $applicant->first_name_en;
        $student->surname = $registerFormInfo->last_name;
        $student->surname_en = $applicant->last_name_en;
        $student->personal_id = $registerFormInfo->identity_number;
        $student->sex = $registerFormInfo->gender;
        $student->birthday = $registerFormInfo->date_of_birth;
        $student->phone = $registerFormInfo->phone;
        $student->email = $registerFormInfo->email;
        $student->photo = $applicant->photo;
        $student->program_id = $applicant->program_id;
        $student->school_id = $school->school_id;
        $student->learn_year_id = $applicant->flow_id;
        $student->status_id = $statusId;
        if($modelClass == TrainingRegister::class){
            $student->address = $address->city.','.$address->street;
        } else {
            $student->address = $applicant->address;
        }
        $student->basics_of_enrollement_id = 1;
        $student->applicant_id = $applicantId;
        $student->applicant_type = $registerFormInfo->registerable_type;
        $student->group_id = $groupId;
        $student->citizenship = "საქართველო";
        $student->save();

        $applicant->update(['status' => 1]);

        return response($student, ResponseAlias::HTTP_CREATED);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
