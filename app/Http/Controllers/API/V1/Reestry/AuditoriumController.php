<?php

namespace App\Http\Controllers\API\V1\Reestry;

use App\Exports\AuditoriumExport;
use App\Filters\AuditoriumFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Reestry\Auditorium\StoreRequest;
use App\Http\Requests\Reestry\Auditorium\UpdateRequest;
use App\Http\Requests\Reestry\Student\ExcelRequest;
use App\Models\Reestry\Auditorium;
use App\Models\Reestry\Campus;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use function config;
use function response;

class AuditoriumController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:auditoriums.index'])->only(['index']);
        $this->middleware(['permission:auditoriums.store'])->only(['store']);
        $this->middleware(['permission:auditoriums.show'])->only(['show']);
        $this->middleware(['permission:auditoriums.update'])->only(['update']);
        $this->middleware(['permission:auditoriums.destroy'])->only(['destroy']);
        $this->middleware(['permission:auditoriums.export'])->only(['exportExcel']);
    }

    public function index(AuditoriumFilter $filter): Response
    {
        return response([
            'auditoriums' => Auditorium::filter($filter)->with(['campus'])
                ->OrderByDesc('id')
                ->paginate(50),
            'campuses' => Campus::pluck('name_ka', 'id')]);
    }

    public function store(StoreRequest $request): Response
    {
        $auditorium = Auditorium::create($request->validated());
        return response($auditorium->load(['campus']), Response::HTTP_CREATED);
    }

    public function show(int $auditorium): Response
    {
        return response(
            Auditorium::with(['campus'])
                ->findOrFail($auditorium)
        );
    }

    public function update(UpdateRequest $request, int $auditorium)
    {
        $auditorium = Auditorium::findOrFail($auditorium);
        $auditorium->update($request->validated());
        return response($auditorium->load(['campus']));
    }

    public function destroy(int $auditorium): Response
    {
        Auditorium::findOrFail($auditorium)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function exportExcel(ExcelRequest $request, AuditoriumFilter $filter): string
    {
        $columns = json_decode($request->columns);
        $auditoriums = Auditorium::filter($filter)->with(['campus'])->get();
        $fileName = 'auditoriums-' . Str::random(8) . '.xlsx';
        Excel::store(
            new AuditoriumExport($columns, $auditoriums),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }
}
