<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\ModelPermissionStoreRequest;
use App\Http\Requests\Permission\StoreRequest;
use App\Http\Requests\Permission\UpdateRequest;
use App\Http\Requests\ProgramPermissionStoreRequest;
use App\Models\Own;
use App\Models\Permission;
use App\Models\Reestry\Program\Program;
use App\Models\User\UserFullAccess;
use App\Models\UserProgram;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use function response;

class PermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:permissions.index'])->only(['index']);
        $this->middleware(['permission:permissions.store'])->only(['store']);
        $this->middleware(['permission:permissions.show'])->only(['show']);
        $this->middleware(['permission:permissions.update'])->only(['update']);
        $this->middleware(['permission:permissions.destroy'])->only(['destroy']);
    }

    /**
     * @OA\Get(
     * path="/permissions",
     * tags={"Permissions"},
     * summary="List of permissions",
     * description="List of permissions",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="title",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name" : "test name",
     *                        "program" : "related object"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(): Response
    {
        return response([
            'permissions' => Permission::paginate(50)
        ]);
    }

    /**
     * @OA\Post(
     * path="/permissions",
     * tags={"Permissions"},
     * summary="Add Permission",
     * description="Add Permission",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StorePermissionRequest",
     *               required={"title"},
     *               @OA\Property(property="title", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Permission created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $permission = Permission::create($request->validated());
        return response($permission, Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/permissions/{id}",
     * tags={"Permissions"},
     * summary="Show permission",
     * description="Show permission",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="title",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "title" : "test title",
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show($permission): Response
    {
        return response(Permission::findOrFail($permission));
    }

    /**
     * @OA\Put(
     * path="/permissions/{id}",
     * tags={"Permissions"},
     * summary="Update Permission",
     * description="Update Permission",
     *   @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *   )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="UpdateFlowRequest",
     *               required={"title"},
     *               @OA\Property(property="title", type="string"),
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Permission updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, $permission): Response
    {
        $permission = Permission::findOrFail($permission);
        $permission->update($request->validated());
        return response($permission);
    }

    /**
     * @OA\Delete(
     * path="/permissions/{id}",
     * tags={"Permissions"},
     * summary="Delete permission",
     * description="Delete permission",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete permission",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy($permission): Response
    {
        Permission::findOrFail($permission)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function modelPermissions(): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'data' => Own::MODELS
        ]);
    }

    public function setProgramPermissions(ProgramPermissionStoreRequest $request): JsonResponse
    {
        $programIds = $request->post('program_ids');
        foreach ($programIds as $programId) {
            UserProgram::create([
                'user_id' => $request->post('user_id'),
                'program_id' => $programId
            ]);
        }
        return response()->json([
            'success' => true,
            'message' => 'Program permissions set successfully'
        ]);
    }

    public function programs(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => Program::select('id', 'name_ka', 'name_en')->get()
        ]);
    }

    public function setModelFullAccess(ModelPermissionStoreRequest $request)
    {
        $modelIds = $request->post('model_ids');
        foreach ($modelIds as $modelId) {
            UserFullAccess::create([
                'user_id' => $request->post('user_id'),
                'model_id' => $modelId
            ]);
        }
    }

    public function checkTokenValidity(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Token is valid'], 200);
    }
}
