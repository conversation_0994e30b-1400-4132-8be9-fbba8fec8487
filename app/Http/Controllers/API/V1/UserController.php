<?php

namespace App\Http\Controllers\API\V1;

use App\Exports\TempUserExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\ModelPermissionStoreRequest;
use App\Http\Requests\ProgramPermissionStoreRequest;
use App\Http\Requests\Reestry\Administration\UpdateInfoRequest;
use App\Http\Requests\User\UpdatePasswordRequest;
use App\Models\Own;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Student\Student;
use App\Models\TempUser;
use App\Models\User\User;
use App\Models\User\UserFullAccess;
use App\Models\User\UserType;
use App\Models\UserProgram;
use App\Services\ImageService;
use Hash;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;
use function response;

class UserController extends Controller
{
    public function personalInformation(): Response
    {
        $administrator = Administration::with(['administrationPosition:id,name_ka',
            'administrationItem:id,name_ka'])
            ->select('first_name', 'last_name', 'identity_number', 'phone', 'email',
                'administration_position_id', 'administration_item_id', 'cv', 'photo')
            ->where('user_id', '=', auth()->id())->first();
        return response($administrator);
    }

    public function updatePersonalInformation(UpdateInfoRequest $request, $id, ImageService $imageService): Response
    {
        $administration = Administration::findOrFail($id);
        $validated = $request->validated();
        $pathName = '/administrations/' . $administration->identity_number . '/profile';
        if ($request->has('photo')) {
            $validated['photo'] = $imageService->upload(file: $request->photo, path: $pathName);
        }
        if ($request->has('cv')) {
            $validated['cv'] = $imageService->upload(file: $request->cv, path: $pathName);
        }

        $administration->update($validated);
        return response($administration->load(['administrationItem', 'administrationPosition']));
//        $administration->load(['administrationItem', 'administrationPosition', 'user', 'school']);
//        $permissions = User::findOrFail($administration->user->id)->permissions();
//        $administration['permissions'] = $permissions;
//        return response($administration);
    }

    public function updatePassword(UpdatePasswordRequest $request): \Illuminate\Http\JsonResponse
    {
        $user = auth()->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'error' => 'მიმდინარე პაროლი არ არის სწორი!'
            ], 400);
        }

        $user->password = Hash::make($request->new_password);
        $user->save();

        return response()->json([
            'message' => 'თქვენი პაროლი განახლდა!'
        ]);
    }

    public function updateUserPassword(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required|exists:users,id',
            'password' => 'required|string',
        ]);

        if ($validatedData) {
            $user = User::findOrFail($validatedData['id']);
            $user->password = Hash::make($validatedData['password']);
            $user->save();
            return response()->json([
                'message' => 'თქვენი პაროლი განახლდა!'
            ]);
        } else {
            return response()->json([
                'error' => 'დაფიქსირდა შეცდომა, სცადეთ თავიდან.'
            ], 400);
        }
    }

    public function tempUsersExport(): BinaryFileResponse
    {
        return \Excel::download(new TempUserExport(), 'temp-users-'.now().'.xlsx');
    }

}
