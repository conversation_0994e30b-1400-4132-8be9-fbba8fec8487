<?php

namespace App\Http\Controllers\API\V1\HR;

use App\Http\Controllers\Controller;
use App\Models\HR\Administration\HrAdministrationEducation;
use App\Models\HR\Administration\HrAdministrationInfo;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureEducation;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo;
use App\Models\HR\Lecturer\Academic\HrAcademicLecturePosition;
use App\Models\HR\Lecturer\Academic\LecturerCategory;
use App\Models\HR\Lecturer\Academic\LecturerPosition;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureEducation;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Lecturer\Lecturer;
use Illuminate\Http\JsonResponse;

class HrStatisticsController extends Controller
{
    public function __construct()
    {
//        $this->middleware(['permission:hr-statistics.index'])->only(['index']);
    }

    /**
     * Get HR statistics
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $statistics = [
            'administrative' => $this->getAdministrativeStatistics(),
            'academic' => $this->getAcademicStatistics(),
            'invited' => $this->getInvitedStatistics(),
        ];

        return response()->json($statistics);
    }

    /**
     * Get administrative personnel statistics
     *
     * @return array
     */
    private function getAdministrativeStatistics()
    {
        // Get all administrative personnel using the Administration model
        $administrators = Administration::with(['hrAdministrationInfo' => [
            'hrAdministrationEducations.academicDegree',
            'hrAdministrationFiles',
            'user',
            'hrAdministrationAppointment',
        ], 'user'])->get();
        $administratorsWithInfo = $administrators->filter(function ($admin) {
            return $admin->hrAdministrationInfo !== null;
        });

//        return [$administrators->count(), $administratorsWithInfo->count(), $administrators];
        // Filter out administrators without HR info
        $administratorsWithInfo = $administrators->filter(function ($admin) {
            return $admin->hrAdministrationInfo !== null;
        });

        // Calculate total count
        $total = $administratorsWithInfo->count();

        if ($total === 0) {
            return $this->getEmptyAdministrativeStatistics();
        }

        // Calculate gender distribution
        $maleCount = $administratorsWithInfo->filter(function ($admin) {
            return $admin->hrAdministrationInfo->gender === 1;
        })->count();

        $femaleCount = $administratorsWithInfo->filter(function ($admin) {
            return $admin->hrAdministrationInfo->gender === 0;
        })->count();
//        return [$]


        $ageCount =  $maleCount + $femaleCount;
        $malePercentage = $ageCount > 0 ? round(($maleCount / $ageCount) * 100) : 0;
        $femalePercentage = $ageCount > 0 ? round(($femaleCount / $ageCount) * 100) : 0;

        // Calculate average age
        $averageAge = $administratorsWithInfo->avg(function ($admin) {
            return $admin->hrAdministrationInfo->age;
        }) ?? 0;
        $averageAge = round($averageAge);

        // Calculate education level distribution
        $educationStats = $this->calculateAdministrativeEducationStats($administratorsWithInfo);

        return [
            'total' => $total,
            'gender' => [
                'male' => $maleCount,
                'female' => $femaleCount,
                'male_percentage' => $malePercentage,
                'female_percentage' => $femalePercentage,
            ],
            'average_age' => $averageAge,
            'education' => $educationStats,
        ];
    }

    /**
     * Get academic personnel statistics
     *
     * @return array
     */
    private function getAcademicStatistics(): array
    {
        // Get all academic personnel using the Lecturer model with affiliated=1
        $academicLecturers = Lecturer::with(['academicDegree', 'hrAcademicLectureInfo' => [
            'hrAcademicLectureEducations.academicDegree',
            'hrAcademicLectureAttachments',
            'user',
            'hrAcademicLecturePosition',
            'hrAcademicLectureAdditional'
        ]])->whereAffiliated(1)->get();

        // Filter out lecturers without HR info
        $academicLecturersWithInfo = $academicLecturers->filter(function ($lecturer) {
            return $lecturer->hrAcademicLectureInfo !== null;
        });

        // Calculate total count
        $total = $academicLecturersWithInfo->count();

        if ($total === 0) {
            return $this->getEmptyAcademicStatistics();
        }

        // Calculate gender distribution
        $maleCount = $academicLecturersWithInfo->filter(function ($lecturer) {
            return $lecturer->hrAcademicLectureInfo->gender === 1;
        })->count();

        $femaleCount = $academicLecturersWithInfo->filter(function ($lecturer) {
            return $lecturer->hrAcademicLectureInfo->gender === 0;
        })->count();

        $ageTotal = $maleCount + $femaleCount;
        $malePercentage = $ageTotal > 0 ? round(($maleCount / $ageTotal) * 100) : 0;
        $femalePercentage = $ageTotal > 0 ? round(($femaleCount / $ageTotal) * 100) : 0;

        // Calculate average age
        $averageAge = $academicLecturersWithInfo->avg(function ($lecturer) {
            return $lecturer->hrAcademicLectureInfo->age;
        }) ?? 0;
        $averageAge = round($averageAge);

        // Calculate education level distribution
        $educationStats = $this->calculateAcademicEducationStats($academicLecturersWithInfo);

        // Calculate position distribution
        $positionStats = $this->calculatePositionStats($academicLecturersWithInfo);

        // Calculate affiliation status
        $affiliationStats = $this->calculateAffiliationStats($academicLecturersWithInfo);

        // Calculate category distribution
        $categoryStats = $this->calculateCategoryStats($academicLecturersWithInfo);

        return [
            'total' => $total,
            'gender' => [
                'male' => $maleCount,
                'female' => $femaleCount,
                'male_percentage' => $malePercentage,
                'female_percentage' => $femalePercentage,
            ],
            'average_age' => $averageAge,
            'education' => $educationStats,
            'position' => $positionStats,
            'affiliation' => $affiliationStats,
            'category' => $categoryStats,
        ];
    }

    /**
     * Get invited personnel statistics
     *
     * @return array
     */
    private function getInvitedStatistics(): array
    {
        // Get all invited personnel using the Lecturer model with affiliated=0
        $invitedLecturers = Lecturer::with(['hrInvitedLectureInfo' => [
            'hrInvitedLectureEducations.academicDegree',
            'hrInvitedLectureAttachments',
            'user',
            'hrInvitedLecturePosition',
        ]])->whereAffiliated(0)->get();

        // Filter out lecturers without HR info
        $invitedLecturersWithInfo = $invitedLecturers->filter(function ($lecturer) {
            return $lecturer->hrInvitedLectureInfo !== null;
        });

        // Calculate total count
        $total = $invitedLecturersWithInfo->count();

        if ($total === 0) {
            return $this->getEmptyInvitedStatistics();
        }

        // Calculate gender distribution
        $maleCount = $invitedLecturersWithInfo->filter(function ($lecturer) {
            return $lecturer->hrInvitedLectureInfo->gender === 1;
        })->count();

        $femaleCount = $invitedLecturersWithInfo->filter(function ($lecturer) {
            return $lecturer->hrInvitedLectureInfo->gender === 0;
        })->count();

        $ageTotal = $maleCount + $femaleCount;
        $malePercentage = $ageTotal > 0 ? round(($maleCount / $ageTotal) * 100) : 0;
        $femalePercentage = $ageTotal > 0 ? round(($femaleCount / $ageTotal) * 100) : 0;

        // Calculate average age
        $averageAge = $invitedLecturersWithInfo->avg(function ($lecturer) {
            return $lecturer->hrInvitedLectureInfo->age;
        }) ?? 0;
        $averageAge = round($averageAge);

        // Calculate education level distribution
        $educationStats = $this->calculateInvitedEducationStats($invitedLecturersWithInfo);

        return [
            'total' => $total,
            'gender' => [
                'male' => $maleCount,
                'female' => $femaleCount,
                'male_percentage' => $malePercentage,
                'female_percentage' => $femalePercentage,
            ],
            'average_age' => $averageAge,
            'education' => $educationStats,
        ];
    }

    /**
     * Calculate education statistics for administrative personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculateAdministrativeEducationStats($personnel): array
    {
        // Collect all education records from the administrators
        $educations = collect();

        foreach ($personnel as $admin) {
            if ($admin->hrAdministrationInfo && $admin->hrAdministrationInfo->hrAdministrationEducations) {
                $educations = $educations->merge($admin->hrAdministrationInfo->hrAdministrationEducations);
            }
        }

        $total = $educations->count();

        // Define education levels
        $educationLevels = [
            'secondary' => ['count' => 0, 'percentage' => 0],
            'bachelor' => ['count' => 0, 'percentage' => 0],
            'master' => ['count' => 0, 'percentage' => 0],
            'phd' => ['count' => 0, 'percentage' => 0],
            'doctoral' => ['count' => 0, 'percentage' => 0],
            'professional' => ['count' => 0, 'percentage' => 0],
        ];

        // Count education levels
        foreach ($educations as $education) {
            $academicDegree = $education->academicDegree;
            if (!$academicDegree) {
                continue;
            }

            $degreeUrl = $academicDegree->url ?? '';

            if (str_contains(strtolower($academicDegree->name_en), 'bachelor') || $degreeUrl === 'bachelor') {
                $educationLevels['bachelor']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'master') || $degreeUrl === 'master') {
                $educationLevels['master']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'phd') || $degreeUrl === 'phd') {
                $educationLevels['phd']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'doctoral')) {
                $educationLevels['doctoral']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'professional') || $degreeUrl === 'hse') {
                $educationLevels['professional']['count']++;
            } else {
                $educationLevels['secondary']['count']++;
            }
        }

        // Calculate percentages
        foreach ($educationLevels as $level => $stats) {
            $educationLevels[$level]['percentage'] = $total > 0 ? round(($stats['count'] / $total) * 100) : 0;
        }

        return $educationLevels;
    }

    /**
     * Calculate education statistics for academic personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculateAcademicEducationStats($personnel): array
    {
        // Collect all education records from the academic lecturers
        $educations = collect();

        foreach ($personnel as $lecturer) {
            if ($lecturer->hrAcademicLectureInfo && $lecturer->hrAcademicLectureInfo->hrAcademicLectureEducations) {
                $educations = $educations->merge($lecturer->hrAcademicLectureInfo->hrAcademicLectureEducations);
            }
        }

        $total = $educations->count();

        // Define education levels
        $educationLevels = [
            'bachelor' => ['count' => 0, 'percentage' => 0],
            'master' => ['count' => 0, 'percentage' => 0],
            'phd' => ['count' => 0, 'percentage' => 0],
            'doctoral' => ['count' => 0, 'percentage' => 0],
        ];

        // Count education levels
        foreach ($educations as $education) {
            $academicDegree = $education->academicDegree;
            if (!$academicDegree) {
                continue;
            }

            $degreeUrl = $academicDegree->url ?? '';

            if (str_contains(strtolower($academicDegree->name_en), 'bachelor') || $degreeUrl === 'bachelor') {
                $educationLevels['bachelor']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'master') || $degreeUrl === 'master') {
                $educationLevels['master']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'phd') || $degreeUrl === 'phd') {
                $educationLevels['phd']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'doctoral')) {
                $educationLevels['doctoral']['count']++;
            }
        }

        // Calculate percentages
        foreach ($educationLevels as $level => $stats) {
            $educationLevels[$level]['percentage'] = $total > 0 ? round(($stats['count'] / $total) * 100) : 0;
        }

        return $educationLevels;
    }

    /**
     * Calculate education statistics for invited personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculateInvitedEducationStats($personnel): array
    {
        // Collect all education records from the invited lecturers
        $educations = collect();

        foreach ($personnel as $lecturer) {
            if ($lecturer->hrInvitedLectureInfo && $lecturer->hrInvitedLectureInfo->hrInvitedLectureEducations) {
                $educations = $educations->merge($lecturer->hrInvitedLectureInfo->hrInvitedLectureEducations);
            }
        }

        $total = $educations->count();

        // Define education levels
        $educationLevels = [
            'bachelor' => ['count' => 0, 'percentage' => 0],
            'master' => ['count' => 0, 'percentage' => 0],
            'phd' => ['count' => 0, 'percentage' => 0],
            'doctoral' => ['count' => 0, 'percentage' => 0],
        ];

        // Count education levels
        foreach ($educations as $education) {
            $academicDegree = $education->academicDegree;
            if (!$academicDegree) {
                continue;
            }

            $degreeUrl = $academicDegree->url ?? '';

            if (str_contains(strtolower($academicDegree->name_en), 'bachelor') || $degreeUrl === 'bachelor') {
                $educationLevels['bachelor']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'master') || $degreeUrl === 'master') {
                $educationLevels['master']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'phd') || $degreeUrl === 'phd') {
                $educationLevels['phd']['count']++;
            } elseif (str_contains(strtolower($academicDegree->name_en), 'doctoral')) {
                $educationLevels['doctoral']['count']++;
            }
        }

        // Calculate percentages
        foreach ($educationLevels as $level => $stats) {
            $educationLevels[$level]['percentage'] = $total > 0 ? round(($stats['count'] / $total) * 100) : 0;
        }

        return $educationLevels;
    }

    /**
     * Calculate position statistics for academic personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculatePositionStats($personnel): array
    {
        // Get all positions
        $positions = LecturerPosition::all()->pluck('title', 'id');

        // Define position types
        $positionTypes = [
            'professor' => ['count' => 0, 'percentage' => 0],
            'associate_professor' => ['count' => 0, 'percentage' => 0],
            'assistant_professor' => ['count' => 0, 'percentage' => 0],
            'assistant' => ['count' => 0, 'percentage' => 0],
        ];

        $filteredPersonnel = $personnel->filter(function ($lecturer) use ($positions) {
            $info = $lecturer->hrAcademicLectureInfo;
            $position = $info?->hrAcademicLecturePosition?->lecturer_position_id ?? null;

            return $info && $position && isset($positions[$position]);
        });


        // Count positions
        foreach ($filteredPersonnel as $lecturer) {
            $position = $lecturer->hrAcademicLectureInfo->hrAcademicLecturePosition->lecturer_position_id ?? null;
            $positionTitle = $positions[$position];

            if (str_contains(strtolower($positionTitle), 'პროფესორი') && !str_contains(strtolower($positionTitle), 'ასოცირებული') && !str_contains(strtolower($positionTitle), 'ასისტენტ')) {
                $positionTypes['professor']['count']++;
            } elseif (str_contains(strtolower($positionTitle), 'ასოცირებული')) {
                $positionTypes['associate_professor']['count']++;
            } elseif (str_contains(strtolower($positionTitle), 'ასისტენტ პროფესორი')) {
                $positionTypes['assistant_professor']['count']++;
            } elseif (str_contains(strtolower($positionTitle), 'ასისტენტი')) {
                $positionTypes['assistant']['count']++;
            }
        }

        // Calculate percentages
        $total = $filteredPersonnel->count();
        foreach ($positionTypes as $type => $stats) {
            $positionTypes[$type]['percentage'] = $total > 0 ? round(($stats['count'] / $total) * 100) : 0;
        }

        return $positionTypes;
    }

    /**
     * Calculate affiliation statistics for academic personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculateAffiliationStats($personnel): array
    {
        $affiliatedCount = 0;
        $nonAffiliatedCount = 0;

        foreach ($personnel as $lecturer) {
            if (!$lecturer->hrAcademicLectureInfo || !$lecturer->hrAcademicLectureInfo->hrAcademicLecturePosition) {
                continue;
            }

            $affiliated = $lecturer->hrAcademicLectureInfo->hrAcademicLecturePosition->affiliated ?? null;

            if ($affiliated === 1) {
                $affiliatedCount++;
            } else {
                $nonAffiliatedCount++;
            }
        }

        $total = $personnel->count();
        $affiliatedPercentage = $total > 0 ? round(($affiliatedCount / $total) * 100) : 0;
        $nonAffiliatedPercentage = $total > 0 ? round(($nonAffiliatedCount / $total) * 100) : 0;

        return [
            'affiliated' => [
                'count' => $affiliatedCount,
                'percentage' => $affiliatedPercentage,
            ],
            'non_affiliated' => [
                'count' => $nonAffiliatedCount,
                'percentage' => $nonAffiliatedPercentage,
            ],
        ];
    }

    /**
     * Calculate category statistics for academic personnel
     *
     * @param \Illuminate\Support\Collection $personnel
     * @return array
     */
    private function calculateCategoryStats($personnel): array
    {
        // Get all categories
        $categories = LecturerCategory::all()->pluck('title', 'id');

        // Define category types
        $categoryTypes = [
            'a' => ['count' => 0, 'percentage' => 0],
            'b' => ['count' => 0, 'percentage' => 0],
            'c' => ['count' => 0, 'percentage' => 0],
            'd' => ['count' => 0, 'percentage' => 0],
        ];

        // Count categories
        foreach ($personnel as $lecturer) {
            if (!$lecturer->hrAcademicLectureInfo || !$lecturer->hrAcademicLectureInfo->hrAcademicLecturePosition) {
                continue;
            }

            $category = $lecturer->hrAcademicLectureInfo->hrAcademicLecturePosition->lecturer_category_id ?? null;
            if (!$category || !isset($categories[$category])) {
                continue;
            }

            $categoryTitle = $categories[$category];

            if (str_contains(strtolower($categoryTitle), 'ა')) {
                $categoryTypes['a']['count']++;
            } elseif (str_contains(strtolower($categoryTitle), 'ბ')) {
                $categoryTypes['b']['count']++;
            } elseif (str_contains(strtolower($categoryTitle), 'გ')) {
                $categoryTypes['c']['count']++;
            } elseif (str_contains(strtolower($categoryTitle), 'დ')) {
                $categoryTypes['d']['count']++;
            }
        }

        // Calculate percentages
        $total = $personnel->count();
        foreach ($categoryTypes as $type => $stats) {
            $categoryTypes[$type]['percentage'] = $total > 0 ? round(($stats['count'] / $total) * 100) : 0;
        }

        return $categoryTypes;
    }

    /**
     * Get empty administrative statistics structure
     *
     * @return array
     */
    private function getEmptyAdministrativeStatistics(): array
    {
        return [
            'total' => 0,
            'gender' => [
                'male' => 0,
                'female' => 0,
                'male_percentage' => 0,
                'female_percentage' => 0,
            ],
            'average_age' => 0,
            'education' => [
                'secondary' => ['count' => 0, 'percentage' => 0],
                'bachelor' => ['count' => 0, 'percentage' => 0],
                'master' => ['count' => 0, 'percentage' => 0],
                'phd' => ['count' => 0, 'percentage' => 0],
                'doctoral' => ['count' => 0, 'percentage' => 0],
                'professional' => ['count' => 0, 'percentage' => 0],
            ],
        ];
    }

    /**
     * Get empty academic statistics structure
     *
     * @return array
     */
    private function getEmptyAcademicStatistics(): array
    {
        return [
            'total' => 0,
            'gender' => [
                'male' => 0,
                'female' => 0,
                'male_percentage' => 0,
                'female_percentage' => 0,
            ],
            'average_age' => 0,
            'education' => [
                'bachelor' => ['count' => 0, 'percentage' => 0],
                'master' => ['count' => 0, 'percentage' => 0],
                'phd' => ['count' => 0, 'percentage' => 0],
                'doctoral' => ['count' => 0, 'percentage' => 0],
            ],
            'position' => [
                'professor' => ['count' => 0, 'percentage' => 0],
                'associate_professor' => ['count' => 0, 'percentage' => 0],
                'assistant_professor' => ['count' => 0, 'percentage' => 0],
                'assistant' => ['count' => 0, 'percentage' => 0],
            ],
            'affiliation' => [
                'affiliated' => ['count' => 0, 'percentage' => 0],
                'non_affiliated' => ['count' => 0, 'percentage' => 0],
            ],
            'category' => [
                'a' => ['count' => 0, 'percentage' => 0],
                'b' => ['count' => 0, 'percentage' => 0],
                'c' => ['count' => 0, 'percentage' => 0],
                'd' => ['count' => 0, 'percentage' => 0],
            ],
        ];
    }

    /**
     * Get empty invited statistics structure
     *
     * @return array
     */
    private function getEmptyInvitedStatistics(): array
    {
        return [
            'total' => 0,
            'gender' => [
                'male' => 0,
                'female' => 0,
                'male_percentage' => 0,
                'female_percentage' => 0,
            ],
            'average_age' => 0,
            'education' => [
                'bachelor' => ['count' => 0, 'percentage' => 0],
                'master' => ['count' => 0, 'percentage' => 0],
                'phd' => ['count' => 0, 'percentage' => 0],
                'doctoral' => ['count' => 0, 'percentage' => 0],
            ],
        ];
    }
}
