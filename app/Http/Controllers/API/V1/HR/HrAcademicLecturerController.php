<?php

namespace App\Http\Controllers\API\V1\HR;

use App\Exports\Hr\AcademicLecturerExport;
use App\Filters\Hr\AcademicLecturerFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\AcademicLectureStoreRequest;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureAdditional;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureAttachment;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureEducation;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo;
use App\Models\HR\Lecturer\Academic\HrAcademicLecturePosition;
use App\Models\HR\Lecturer\Academic\LecturerCategory;
use App\Models\HR\Lecturer\Academic\LecturerPosition;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Administration\AdministrationItem;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\School;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;

class HrAcademicLecturerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:hr-academic-lecturers.index'])->only(['index']);
        $this->middleware(['permission:hr-academic-lecturers.store'])->only(['store']);
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @throws \Exception
     */
    public function export(AcademicLecturerFilter $filter)
    {
        $fileName = 'hr-academic-lecturers-'.now().'.xlsx';

        $lectures = Lecturer::filter($filter)->with(['hrAcademicLectureInfo' => [
            'hrAcademicLectureEducations',
            'hrAcademicLectureAttachments',
            'user',
            'hrAcademicLecturePosition',
            'hrAcademicLectureAdditional'
        ]])->whereAffiliated(1)->get();

        Excel::store(
            new AcademicLecturerExport($lectures),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }

    public function index(AcademicLecturerFilter $filter): Response
    {
        return response([
            'lecturers' => Lecturer::filter($filter)->with(['academicDegree', 'hrAcademicLectureInfo' => [
                'hrAcademicLectureEducations',
                'hrAcademicLectureAttachments',
                'user',
                'hrAcademicLecturePosition',
                'hrAcademicLectureAdditional'
            ]])
                ->whereAffiliated(1)->paginate(10),
//            'lecturerCategories' => LecturerCategory::pluck('title', 'id'),
            'lecturerPositions' => LecturerPosition::pluck('title', 'id'),
            'schools' => School::pluck('name_ka', 'id'),
            'academicDegrees' => AcademicDegree::pluck('name_ka', 'id'),
            'administrationItems' => AdministrationItem::pluck('name_ka', 'id')
        ]);
    }

    public function store(
        AcademicLectureStoreRequest $request,
        ImageService                $imageService)
    {
        $userId = $request->user_id;
        $data = $request->validated();
//        $data['date_of_birth'] = Carbon::createFromFormat('d/m/Y', $request->date_of_birth);
        $lecturer = Lecturer::where('user_id', '=', $request->user_id)
            ->first();
        $lecturer->update($data);
        HrAcademicLectureInfo::updateOrCreate([
            'user_id' => $userId
        ], $data);
        $hrAcademicLectureInfoId = HrAcademicLectureInfo::whereUserId($request->user_id)->first()->id;
        HrAcademicLectureEducation::where('hr_academic_lecture_info_id', '=', $hrAcademicLectureInfoId)
            ->delete();
        if (is_array($request->hr_academic_lecture_educations)) {
            foreach ($request->hr_academic_lecture_educations as $education) {
                $education['hr_academic_lecture_info_id'] = $hrAcademicLectureInfoId;
                HrAcademicLectureEducation::create($education);
            }
        }

        if ($request->hasFile('vacancy_command_number_file')) {
            $data['vacancy_command_number_file'] = $imageService->upload($request->vacancy_command_number_file, '/hr/academic-lecture/vacancy_command_number_file/' . $request->identity_number . '/', null, true);
        }
        if ($request->hasFile('appointment_command_number_file')) {
            $data['appointment_command_number_file'] = $imageService->upload($request->appointment_command_number_file, '/hr/academic-lecture/appointment_command_number_file/' . $request->identity_number . '/', null, true);
        }
        if ($request->hasFile('vacancy_document_file')) {
            $data['vacancy_document_file'] = $imageService->upload($request->vacancy_document_file, '/hr/academic-lecture/vacancy_document_file/' . $request->identity_number . '/', true);
        }

        if ($request->hasFile('commission_file')) {
            $data['commission_file'] = $imageService->upload($request->commission_file, '/hr/academic-lecture/commission_file/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('cv_georgian')) {
            $data['cv_georgian'] = $imageService->upload($request->cv_georgian, '/hr/academic-lecture/cv_georgian/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('cv_english')) {
            $data['cv_english'] = $imageService->upload($request->cv_english, '/hr/academic-lecture/cv_english/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('id_card')) {
            $data['id_card'] = $imageService->upload($request->id_card, '/hr/academic-lecture/id_card/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('diploma')) {
            $data['diploma'] = $imageService->upload($request->diploma, '/hr/academic-lecture/diploma/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('scientific_works')) {
            $data['scientific_works'] = $imageService->upload($request->scientific_works, '/hr/academic-lecture/scientific_works/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('certificate')) {
            $data['certificate'] = $imageService->upload($request->certificate, '/hr/academic-lecture/certificate/' . $request->identity_number . '/', null, true);
        }

//        return \response()->json($data);

        HrAcademicLecturePosition::updateOrCreate([
            'hr_academic_lecture_info_id' => $hrAcademicLectureInfoId
        ], $data);
        if (is_array($request->hr_academic_lecture_attachments)) {
            foreach ($request->hr_academic_lecture_attachments as $attachment) {
                if (!is_string($attachment)) {
                    $uploadedFile = $imageService->upload($attachment,
                        '/hr/academic-lecture/' . $request->identity_number . '/files', null, true);
                    HrAcademicLectureAttachment::create([
                        'hr_academic_lecture_info_id' => $hrAcademicLectureInfoId,
                        'filename' => $uploadedFile,
                        'name' => $attachment->getClientOriginalName()
                    ]);
                }
            }
        }
        HrAcademicLectureAdditional::updateOrCreate([
            'hr_academic_lecture_info_id' => $hrAcademicLectureInfoId
        ], $request->validated());

        return response([
            'lecturers' => Lecturer::with(['hrAcademicLectureInfo' => [
                'hrAcademicLectureEducations',
                'hrAcademicLectureAttachments',
                'user',
                'hrAcademicLecturePosition',
                'hrAcademicLectureAdditional'
            ]])
                ->where('user_id', '=', $request->user_id)
                ->first()
        ]);

    }


    public function removeImage(Request $request): Response
    {
        $request->validate([
            'filename' => [
                'required',
                'string',
                'exists:hr_academic_lecture_attachments,filename'
            ]
        ]);

        HrAcademicLectureAttachment::where(
            'filename',
            '=',
            $request->filename
        )->delete();

        \Storage::delete(substr($request->filename, 1));
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
