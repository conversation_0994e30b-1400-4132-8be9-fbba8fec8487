<?php

namespace App\Http\Controllers\API\V1\HR;

use App\Exports\Hr\AcademicLecturerExport;
use App\Exports\Hr\InvitedLecturerExport;
use App\Filters\Hr\InvitedLecturerFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\InvitedLectureStoreRequest;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureAttachment;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureAttachment;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureEducation;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo;
use App\Models\HR\Lecturer\Invited\HrInvitedLecturePosition;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Administration\AdministrationItem;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\WorkType;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class HrInvitedLecturerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:hr-invited-lecturers.index'])->only(['index']);
        $this->middleware(['permission:hr-invited-lecturers.store'])->only(['store']);
    }

    public function export(InvitedLecturerFilter $filter)
    {
        $lecturers = Lecturer::filter($filter)->with(['hrInvitedLectureInfo' => [
            'hrInvitedLectureEducations',
            'hrInvitedLectureAttachments',
            'user',
            'hrInvitedLecturePosition',
        ]])->whereAffiliated(0)->get();

        $fileName = 'hr-invited-lecturers-'.now().'.xlsx';
        Excel::store(
            new InvitedLecturerExport($lecturers),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }

    public function index(InvitedLecturerFilter $filter): Response
    {
        return response([
            'lecturers' => Lecturer::filter($filter)->with(['hrInvitedLectureInfo' => [
                'hrInvitedLectureEducations',
                'hrInvitedLectureAttachments',
                'user',
                'hrInvitedLecturePosition',
            ]])->whereAffiliated(0)->paginate(10),
            'academicDegrees' => AcademicDegree::pluck('name_ka', 'id'),
            'administrationItems' => AdministrationItem::pluck('name_ka', 'id'),
            'schools' => School::pluck('name_ka', 'id'),
            'programs' => Program::pluck('name_ka', 'id'),
            'workTypes' => WorkType::pluck('title', 'id')
        ]);
    }

    public function store(
        InvitedLectureStoreRequest $request,
        ImageService               $imageService
    )
    {
        $lecturer = Lecturer::where('user_id', '=', $request->user_id)
            ->first();
        $userId = $request->user_id;
        $data = $request->validated();
        $lecturer->update($data);
        HrInvitedLectureInfo::updateOrCreate([
            'user_id' => $userId
        ], $data);
        $hrInvitedLectureInfoId = HrInvitedLectureInfo::whereUserId($request->user_id)->first()->id;
        HrInvitedLectureEducation::where(
            'hr_invited_lecture_info_id',
            '=',
            $hrInvitedLectureInfoId
        )->delete();


        if (is_array($request->hr_invited_lecture_educations)) {
            foreach ($request->hr_invited_lecture_educations as $education) {
                $education['hr_invited_lecture_info_id'] = $hrInvitedLectureInfoId;
                HrInvitedLectureEducation::create($education);
            }
        }
        $data = $request->validated();

        if ($request->hasFile('cv_georgian')) {
            $data['cv_georgian'] = $imageService->upload($request->cv_georgian, '/hr/invited-lecture/cv_georgian/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('cv_english')) {
            $data['cv_english'] = $imageService->upload($request->cv_english, '/hr/invited-lecture/cv_english/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('id_card')) {
            $data['id_card'] = $imageService->upload($request->id_card, '/hr/invited-lecture/id_card/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('diploma')) {
            $data['diploma'] = $imageService->upload($request->diploma, '/hr/invited-lecture/diploma/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('scientific_works')) {
            $data['scientific_works'] = $imageService->upload($request->scientific_works, '/hr/invited-lecture/scientific_works/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('certificate')) {
            $data['certificate'] = $imageService->upload($request->certificate, '/hr/invited-lecture/certificate/' . $request->identity_number . '/', null, true);
        }

//        return \response()->json($data);

        HrInvitedLecturePosition::updateOrCreate([
            'hr_invited_lecture_info_id' => $hrInvitedLectureInfoId
        ], $data);


        if (is_array($request->hr_invited_lecture_attachments)) {
            foreach ($request->hr_invited_lecture_attachments as $attachment) {
                if (!is_string($attachment)) {
                    $uploadedFile = $imageService->upload($attachment,
                        '/hr/invited-lecture/' . $request->identity_number . '/files', null, true);
                    HrInvitedLectureAttachment::create([
                        'hr_invited_lecture_info_id' => $hrInvitedLectureInfoId,
                        'filename' => $uploadedFile,
                        'name' => $attachment->getClientOriginalName()
                    ]);
                }
            }
        }
        $lecturer = Lecturer::select(
            [
                'id',
                'first_name',
                'last_name',
                'identity_number',
                'phone'
            ])
            ->where('user_id', '=', $userId)
            ->where('affiliated', '=', 0)->first();

//        $hrLecturer = HrInvitedLectureInfo::with([
//            'hrInvitedLectureAttachments',
//            'hrInvitedLectureEducations',
//            'hrInvitedLecturePosition'
//        ])->where('user_id', '=', $userId)->first();

        return response([
            'lecturer' => Lecturer::with(['hrInvitedLectureInfo' => [
                'hrInvitedLectureEducations',
                'hrInvitedLectureAttachments',
                'user',
                'hrInvitedLecturePosition',
            ]])->where('user_id', '=', $userId)->first()
        ]);
    }

    public function removeImage(Request $request): Response
    {
        $request->validate([
            'filename' => [
                'required',
                'string',
                'exists:hr_invited_lecture_attachments,filename'
            ]
        ]);

        HrInvitedLectureAttachment::where(
            'filename',
            '=',
            $request->filename
        )->delete();

        \Storage::delete(substr($request->filename, 1));
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
