<?php

namespace App\Http\Controllers\API\V1\HR;

use App\Exports\Hr\AdministrationExport;
use App\Filters\Hr\AcademicAdministratorFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\AdministrationStoreRequest;
use App\Models\HR\Administration\HrAdministrationFile;
use App\Models\HR\Administration\HrAdministrationInfo;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureAttachment;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Administration\AdministrationItem;
use App\Models\Reestry\School;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class HrAdministrationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:hr-administrations.index'])->only(['index']);
        $this->middleware(['permission:hr-administrations.store'])->only(['store']);
    }

    public function export(AcademicAdministratorFilter $filter)
    {
        $administrations = Administration::filter($filter)
            ->with([
                'hrAdministrationInfo' => [
                    'hrAdministrationEducations',
                    'hrAdministrationFiles',
                    'user',
                    'hrAdministrationAppointment',
                ]
            ])
            ->get();

        $fileName = 'hr-administrations-'.now().'.xlsx';
        Excel::store(
            new AdministrationExport($administrations),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/'.config('excel.storage_registry').'/'.$fileName;
    }

    public function index(AcademicAdministratorFilter $filter): Response
    {
        return response([
            'administrators' => Administration::filter($filter)->with(['hrAdministrationInfo' => [
                'hrAdministrationEducations',
                'hrAdministrationFiles',
                'user',
                'hrAdministrationAppointment',
            ]])->paginate(10),
            'academicDegrees' => AcademicDegree::pluck('name_ka', 'id'),
            'schools' => School::pluck('name_ka', 'id'),
            'administrationItems' => AdministrationItem::pluck('name_ka', 'id')
        ]);
    }

    public function store(
        AdministrationStoreRequest $request,
        ImageService               $imageService
    ): Response
    {
        $data = $request->validated();
        $userId = $request->user_id;
        $administrator = Administration::whereUserId($userId)
            ->first();
        $administrator->update($data);
        HrAdministrationInfo::updateOrCreate([
            'user_id' => $userId
        ], $data);
        $hrAdministrationInfoId = \App\Models\HR\Administration\HrAdministrationInfo::whereUserId($request->user_id)->first()->id;
        \App\Models\HR\Administration\HrAdministrationEducation::where('hr_administration_info_id', $hrAdministrationInfoId)->delete();
        if (is_array($request->hr_administration_educations)) {
            foreach ($request->hr_administration_educations as $education) {
                $education['hr_administration_info_id'] = $hrAdministrationInfoId;
                \App\Models\HR\Administration\HrAdministrationEducation::create($education);
            }
        }
        if ($request->hasFile('vacancy_command_number_file')) {
            $data['vacancy_command_number_file'] = $imageService->upload($request->vacancy_command_number_file, '/hr/administration/command_number_file' . $request->identity_number . '/', null, true);
        }
        if ($request->hasFile('appointment_command_number_file')) {
            $data['appointment_command_number_file'] = $imageService->upload($request->appointment_command_number_file,
                '/hr/administration/appointment_number_file' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('cv_georgian')) {
            $data['cv_georgian'] = $imageService->upload($request->cv_georgian,
                '/hr/administration/cv_georgian/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('cv_english')) {
            $data['cv_english'] = $imageService->upload($request->cv_english,
                '/hr/administration/cv_english/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('id_card')) {
            $data['id_card'] = $imageService->upload($request->id_card,
                '/hr/administration/id_card/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('diploma')) {
            $data['diploma'] = $imageService->upload($request->diploma,
                '/hr/administration/diploma/' . $request->identity_number . '/', null, true);
        }


        if ($request->hasFile('scientific_works')) {
            $data['scientific_works'] = $imageService->upload($request->scientific_works,
                '/hr/administration/scientific_works/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('certificate')) {
            $data['certificate'] = $imageService->upload($request->certificate,
                '/hr/administration/certificate/' . $request->identity_number . '/', null, true);
        }



        if ($request->hasFile('disciplinary_remark_file')) {
            $data['disciplinary_remark_file'] = $imageService->upload($request->disciplinary_remark_file,
                '/hr/administration/disciplinary_remark_file/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('disciplinary_complaint_file')) {
            $data['disciplinary_complaint_file'] = $imageService->upload($request->disciplinary_complaint_file,
                '/hr/administration/disciplinary_complaint_file/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('disciplinary_compensation_file')) {
            $data['disciplinary_compensation_file'] = $imageService->upload($request->disciplinary_compensation_file,
                '/hr/administration/disciplinary_compensation_file/' . $request->identity_number . '/', null, true);
        }

        if ($request->hasFile('disciplinary_contract_termination_file')) {
            $data['disciplinary_contract_termination_file'] = $imageService->upload($request->disciplinary_contract_termination_file,
                '/hr/administration/disciplinary_contract_termination_file/' . $request->identity_number . '/', null, true);
        }




        \App\Models\HR\Administration\HrAdministrationAppointment::updateOrCreate([
            'hr_administration_info_id' => $hrAdministrationInfoId
        ], $data);
        if (is_array($request->hr_administration_files)) {
            foreach ($request->hr_administration_files as $file) {
                if (!is_string($file)) {
                    $uploadedFile = $imageService->upload($file,
                        '/hr/administration/' . $request->identity_number . '/files', null, true);
                    HrAdministrationFile::create([
                        'hr_administration_info_id' => $hrAdministrationInfoId,
                        'filename' => $uploadedFile,
                        'name' => $file->getClientOriginalName()
                    ]);
                }
            }
        }
        return response([
            'administrators' => Administration::with(['hrAdministrationInfo' => [
                'hrAdministrationEducations',
                'hrAdministrationFiles',
                'user',
                'hrAdministrationAppointment',
            ]])->find($hrAdministrationInfoId)
        ]);
    }

    public function removeImage(Request $request): Response
    {
        $request->validate([
            'filename' => [
                'required',
                'string',
                'exists:hr_administration_files,filename'
            ]
        ]);

        HrAdministrationFile::where(
            'filename',
            '=',
            $request->filename
        )->delete();

        \Storage::delete(substr($request->filename, 1));
        return response(null, Response::HTTP_NO_CONTENT);
    }

}
