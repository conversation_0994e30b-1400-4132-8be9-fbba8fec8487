<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\EventRequest;
use App\Http\Resources\Administration\CalendarEventResource;
use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Http\Response;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:calendar_events.store'])->only(['store']);
        $this->middleware(['permission:calendar_events.show'])->only(['show']);
        $this->middleware(['permission:calendar_events.update'])->only(['update']);
        $this->middleware(['permission:calendar_events.destroy'])->only(['destroy']);
    }
    public function store(EventRequest $request)
    {
        $event = new Event();

        $event->title = $request->title;
        $event->description = $request->description;
        $event->start = Carbon::parse($request->start)->format('Y-m-d H:i');
        $event->end = Carbon::parse($request->end)->format('Y-m-d H:i');
        $event->save();

        return response(CalendarEventResource::make($event), Response::HTTP_CREATED);
    }

    public function show(int $eventId)
    {
        $event = Event::findOrFail($eventId);

        return response($event, Response::HTTP_OK);
    }

    public function update(EventRequest $request, int $eventId)
    {
        $event = Event::findOrFail($eventId);
        $event->title = $request->title;
        $event->description = $request->description;
        $event->start = Carbon::parse($request->start)->format('Y-m-d H:i');
        $event->end = Carbon::parse($request->end)->format('Y-m-d H:i');
        $event->save();

        return response(CalendarEventResource::make($event), Response::HTTP_CREATED);
    }

    public function destroy(int $eventId)
    {
        $event = Event::findOrFail($eventId);
        $event->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
