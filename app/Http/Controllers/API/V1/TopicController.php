<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Topic\StoreRequest;
use App\Http\Requests\Topic\UpdateRequest;
use App\Http\Resources\TopicListResource;
use App\Models\Topic;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class TopicController extends Controller
{
    public function __construct()
    {
        //$this->middleware(['permission:library_topics.index'])->only(['index']);
        $this->middleware(['permission:library-subject.store'])->only(['store']);
        $this->middleware(['permission:library-subject.show'])->only(['show']);
        $this->middleware(['permission:library-subject.update'])->only(['update']);
        $this->middleware(['permission:library-subject.destroy'])->only(['destroy']);
    }

    public function index()
    {
        $data['topics'] = Topic::paginate(20);
        return response()->json($data);
    }

    public function store(StoreRequest $request): TopicListResource
    {
        $topic = Topic::create($request->validated());
        return new TopicListResource($topic);
    }

    public function show($id): TopicListResource
    {
        return new TopicListResource(Topic::findOrFail($id));
    }

    public function update(UpdateRequest $request, $id): TopicListResource
    {
        $topic = Topic::findOrFail($id);
        $topic->update($request->validated());
        return new TopicListResource($topic);
    }

    public function destroy($id): Response
    {
        Topic::findOrFail($id)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
