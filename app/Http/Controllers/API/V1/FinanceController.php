<?php

namespace App\Http\Controllers\API\V1;

use App\Filters\FinanceFilter;
use App\Filters\ProgramFilter;
use App\Filters\SchoolFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\FinanceConfirmationRequest;
use App\Http\Requests\FinanceScheduleRequest;
use App\Models\Finance\Finance;
use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Setting;
use App\Services\FinanceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use App\Exports\FinanceExport;
use App\Exports\FinanceMultiSheetExport;
use Maatwebsite\Excel\Facades\Excel;
use Str;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FinanceController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:finances.index'])->only(['index']);
    }

    public function index(FinanceFilter $filter, Request $request): JsonResponse
    {
        $financeService = new FinanceService();
        $financeService->setAdditionalData();

        $data = [
            'finances' => $financeService->debtQuarterTest($filter),
            'additionalData' => $financeService->getAdditionalData()
        ];

        return response()->json($data);
    }

    public function additionalData()
    {
        $programFilter = (new ProgramFilter());
        $schoolFilter = (new SchoolFilter());
        $data = [
            'additionalData' => [
                'programs' => Program::filter($programFilter)->pluck('name_ka', 'id'),
                'schools' => School::filter($schoolFilter)->pluck('name_ka', 'id'),
                'status' => StudentStatusList::pluck('name_ka', 'id'),
            ]
        ];
        return response()->json($data);
    }

    public function periods()
    {
        return Setting::where('key', 'finance_start_learn_year')->pluck('value')->first() . ',' . Carbon::now()->format('d/m/Y');
    }

    public function requestScheduler(FinanceScheduleRequest $request): Response
    {
        return response((new FinanceService($request->user_id))
            ->scheduler($request->deadlines));
    }

    public function schedulerStatusUpdate(FinanceConfirmationRequest $request): Response
    {
        FinanceScheduler::withTrashed()->whereId($request->id)
            ->whereYear('created_at', '=', now()->year)
            ->update([
                'is_active' => $request->is_active
            ]);
        return response(
            'Finance Scheduler '
            .
            $request->is_active ? 'Activated' : 'Deactivated'
        );
    }

    public function calendar(Request $request): Response
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);
        if ($validated) {
            $student = Student::whereUserId($request->get('user_id'))
                ->first();
            $studentIdNumber = $student->personal_id;
            $studentProgramId = $student->program_id;
            $financeId = Finance::wherePiradiNom($studentIdNumber)
                ->where('ProgramID', $studentProgramId)
                ->latest('id')->first()->id;
            return (new FinanceService($request->user_id, $financeId))->calendar();
        }
        return response(null, Response::HTTP_NOT_FOUND);
    }

    public function checkFinanceLog(Request $request)
    {
        $validated = $request->validate([
            'personal_id' => 'required'
        ]);
        if ($validated) {
            return (new FinanceService())->payments($request->personal_id);
        }
        return response(null, Response::HTTP_NOT_FOUND);
    }

    public function delete(Request $request): Response
    {
        $validated = $request->validate([
            'user_id' => 'required'
        ]);
        if ($validated) {
            $financeScheduler = FinanceScheduler::withTrashed()->with('calendars')->where('user_id', $request->user_id)->first();
            if ($financeScheduler) {
                $financeCalendar = FinanceCalendar::where('finance_scheduler_id', $financeScheduler->id)->get();
                $financeCalendar->each->delete();
                $financeScheduler->forceDelete();
            }
        }
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function studentFinances($id = null): JsonResponse
    {
        if($id){
            $user_id = Student::whereId($id)->first()->user_id;
        } else {
            $user_id = auth()->id();
        }
        $financeService = new FinanceService(userId: $user_id);
        $financeScheduler = FinanceScheduler::whereUserId($user_id)->latest('id')->first();
        if ($financeScheduler !== null && $financeScheduler->exists()) {
            switch ($financeScheduler->status_id) {
                case 1:
                    $text = 'მოთხოვნილი განცხადება განხილვის პროცესშია.';
                    $status = 1;
                    break;
                case 2:
                    $text = 'მოთხოვნილი გრაფიკი დაკმაყოფილებულია.';
                    $status = 2;
                    break;
                case 3:
                    $text = 'მოთხოვნილი გრაფიკი არ დაკმაყოფილდა.';
                    $comment = $financeScheduler->comment;
                    //$comment = "კომენტარი - არ დაკმაყოფილდა მოთხოვნილი გრაფიკი";
                    $status = 3;
                    break;
            }
        } else {
            $text = Null;
            $status = 0;
        }
        $individualSchedule = [
            'text' =>  $text ?? Null,
            'status' => $status ?? Null,
            'comment' => $comment ?? Null
        ];
        return response()->json([
            'generalInfo' => $financeService->studentGeneralInfo(),
            'commitments' => $financeService->commitments($id),
            'individualSchedule' => $individualSchedule
        ]);
    }

//    public function exportExcel(FinanceFilter $filter): BinaryFileResponse
//    {
//        $financeService = new FinanceService();
//        $financeService->setAdditionalData();
//        $finances = $financeService->debtQuarter($filter);
//
//        return Excel::download(new FinanceExport($finances), 'finances.xlsx');
//    }

    public function exportExcel(FinanceFilter $filter): string
    {
        $fileName = 'students-' . Str::random(8) . '.xlsx';
        Excel::store(
            new FinanceMultiSheetExport($filter),
            $fileName,
            config('excel.storage_registry')
        );
        return 'excel/' . config('excel.storage_registry') . '/' . $fileName;
    }

    public function studentDiploma($id)
    {
        $student = Student::query()->find($id);


    }

}
