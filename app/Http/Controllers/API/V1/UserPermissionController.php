<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UserPermissionController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $rolesWithPermissions = $user->roles->load('role.permissions');
        $permissions = $rolesWithPermissions->pluck('role.permissions')->flatten()->pluck('title')->unique();

        return response()->json($permissions);
    }
}
