<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\AuthorizationRequest;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\SetPermissionRequest;
use App\Models\Finance\Finance;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\SystemLog;
use App\Models\User\User;
use App\Models\User\UserType;
use App\Services\AuthService;
use App\Services\FinanceService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use function bcrypt;
use function response;

class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function login(AuthorizationRequest $request): JsonResponse
    {
        $user = User::with('roles.permissions')
            ->where('email', $request->get('email'))
            ->first();
      if($user->user_type_id===UserType::STUDENT){
            $student = Student::where('user_id', $user->id)->firstOrFail();
            if($student->status_id==9){
                $finance = Finance::wherePiradiNom((new FinanceService($user->id))->getStudentIdentityNumber())
                    ->where('ProgramID', $student->program_id)
                    ->orderByDesc('id')
                    ->first();
                if ($finance) {
                    $response['finance_debt'] = max((new FinanceService(userId: $user->id))->studentCurrentDebtForAuth(), 0);
                } else {
                    $response['finance_debt'] = 0;
                }
                return response()->json([
                    'message_ka' => '<p class="pb-2">თქვენი სტატუსი შეჩერებულია დავალიანების გამო!</p>  <p class="text-danger font-bold">დავალიანება შეადგენს: '.$response['finance_debt'].' ლარს</p><hr>',
                    'message_en' => '<p class="pb-2">Your status is suspended due to debt!</p>  <p class="text-danger font-bold">Your debt amount is: '.$response['finance_debt'].' GEL</p><hr>'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }
        $expiredAt = match ($user->user_type_id) {
            UserType::ADMINISTRATION => now()->addDays(7),
            UserType::LECTURER => now()->addHours(12),
            UserType::STUDENT => now()->addHours(24),
        };

        $tokenResult = $user->createToken('MyApp');
        $token = $tokenResult->token;

        // Apply dynamic expiration
        $token->expires_at = $expiredAt;
        $token->save();
        $accessToken = $tokenResult->accessToken;

        //$userInfo = [];
        $userInfo = match ($user->user_type_id) {
            UserType::ADMINISTRATION => $this->authService->administratorInfo($user->id),
            UserType::LECTURER => $this->authService->lecturerInfo($user->id),
            UserType::STUDENT => $this->authService->studentInfo($user->id),
        };
        if ($user->user_type_id == UserType::STUDENT) {
            $studentModel =  Student::query()->where('user_id', $user->id)->first();
            $userInfo['learn_year_id'] = $studentModel->learn_year_id ?? null;
            $userInfo['minor_id'] = $studentModel->minor_id ?? null;
        }
        $tokenExpiration = Carbon::now()->addDays(7)->format('Y-m-d H:i:s');
        $userInfo['user_type'] = $user->user_type_id;
        $userInfo['permissions'] = $user->roles->pluck('permissions')->flatten()->pluck('title')->toArray();
        $response = [
            'accessToken' => $accessToken,
            'tokenExpiration' => $tokenExpiration,
            'user' => $userInfo
        ];

        // login monitoring
        SystemLog::query()->create([
            'action_type' => 'login',
            'model_name' => User::class,
            'action_data' => $response,
        ]);

//        if ($user->user_type_id === UserType::STUDENT) {
//            $response['currentDebt'] = (new FinanceService(userId: $user->id))->debt();
//        }
        return response()->json($response);
    }

    public function logout(Request $request): Response
    {
        $request->user()->token()->revoke();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function forgotPassword(ForgotPasswordRequest $request): Response
    {
        return response($this->authService->forgotPassword());
    }

    public function resetPassword(ResetPasswordRequest $request): Response
    {
        return response($this->authService->resetPassword());
    }

    public function changePassword(ChangePasswordRequest $request): Response
    {
        User::findOrFail(\Auth::id())->update([
            'password' => bcrypt($request->password)
        ]);
        return response('პაროლი წარმატებით შეიცვალა!');
    }

    public function redirectToGoogle(): JsonResponse
    {
        return response()->json([
            'url' => Socialite::driver('google')->stateless()->redirect()->getTargetUrl(),
        ]);
    }

    public function handleGoogleCallback()
    {
        $authGoogle = $this->authService->googleAuth();
        return response($authGoogle);
    }

    public function check(): Response
    {
        return $this->authService->isAuthorized();
    }


    public function setPermissions(SetPermissionRequest $request): Response
    {
        Auth::user()->update($request->only('is_super_admin'));
        Auth::user()->roles()->sync($request->roles, []);
        Auth::user()->programs()->sync($request->programs, []);
        return response(Auth::user()->load(['roles', 'programs:id']));
    }

    public function generateStudentToken(Request $request): JsonResponse
    {
        $user = User::query()->find($request->user_id);

        $userInfo = match ($user->user_type_id) {
            UserType::LECTURER => $this->authService->lecturerInfo($user->id),
            UserType::STUDENT => $this->authService->studentInfo($user->id),
        };

        $userInfo['user_type'] = $user->user_type_id;
        $userInfo['permissions'] = $user->roles->pluck('permissions')->flatten()->pluck('title')->toArray();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // ვამოწმებთ, აქვს თუ არა მომხმარებელს არსებული ტოკენი
        $existingToken = $user->tokens()->where('name', 'auth-token')->first();

        if ($existingToken) {
            // თუ ტოკენი უკვე არსებობს, დავაბრუნოთ იგივე
            return response()->json([
                'token' => $existingToken->plainTextToken,
                'user' => $userInfo,
            ]);
        }

        $tokenResult = $user->createToken('MyApp');
        $token = $tokenResult->accessToken;

        return response()->json([
            'token' => $token,
            'user' => $userInfo,
        ]);
    }
}
