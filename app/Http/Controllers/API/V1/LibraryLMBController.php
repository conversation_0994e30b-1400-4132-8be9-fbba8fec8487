<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Models\LibraryLMB;
use Illuminate\Http\Request;
use Validator;

class LibraryLMBController extends Controller
{
    public function index(Request $request)
    {
        $keyword = $request->input('keyword', '');
        $perPage = $request->input('per_page', 40);

        $validator = Validator::make($request->all(), [
            'keyword' => 'nullable|string|min:2|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $query = LibraryLMB::query();

        if ($keyword && strlen($keyword) >= 2) {

            $sanitizedKeyword = htmlspecialchars($keyword, ENT_QUOTES, 'UTF-8');

            $query->where(function ($query) use ($sanitizedKeyword) {
                $query->where('name', 'LIKE', "%{$sanitizedKeyword}%")
                    ->orWhere('autor', 'LIKE', "%{$sanitizedKeyword}%")
                    ->orWhere('sagani', 'LIKE', "%{$sanitizedKeyword}%")
                    ->orWhere('lektori', 'LIKE', "%{$sanitizedKeyword}%");
            });
        }

        $libraries = $query->OrderBy('id', 'desc')->paginate($perPage);

        return response()->json(['library-lmb'=>$libraries], 200);
    }
}
