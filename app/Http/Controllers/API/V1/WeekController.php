<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Week\StoreWeekRequest;
use App\Http\Requests\Week\UpdateWeekRequest;
use App\Models\Week;
use Illuminate\Http\Response;
use function response;


class WeekController extends Controller
{

    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreWeekRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreWeekRequest $request)
    {
        $week = Week::create($request);
        $week->update(['syllabus_id', $request->syllabus_id]);

        return response($week, Response::HTTP_CREATED);

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Week  $week
     * @return \Illuminate\Http\Response
     */
    public function show(Week $week)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Week  $week
     * @return \Illuminate\Http\Response
     */
    public function edit(Week $week)
    {
        //
    }


    public function update(UpdateWeekRequest $request, Week $week)
    {
        $week->update($request->all());
        $week->update(['syllabus_id', $request->syllabus_id]);
        return response($week, Response::HTTP_CREATED);
    }


    public function destroy(Week $week)
    {
        $week->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
