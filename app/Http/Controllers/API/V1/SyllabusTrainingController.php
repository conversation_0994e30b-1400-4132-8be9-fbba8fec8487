<?php

namespace App\Http\Controllers\API\V1;

use App\Filters\Syllabus\SyllabusFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Syllabus\StoreSyllabusTrainingRequest;
use App\Http\Requests\Syllabus\UpdateSyllabusTrainingRequest;
use App\Models\Assignment;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\LearnYear;
use App\Models\Syllabus\LecturerContactTime;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class SyllabusTrainingController extends Controller
{
    public function index(SyllabusFilter $filter): Response
    {
        return response([
            'syllabi' => Syllabus::filter($filter)->with([
                'learnYear:id,name',
                'lecturers',
                'assignments',
                'academicDegree',
                'curriculum.lecture:id,curriculum_id',
            ])->whereIsTraining(1)->orderBy('status_id')->orderBy('semester_id')->get(),
        ]);
    }

    public function store(StoreSyllabusTrainingRequest $request, SyllabusService $service)
    {
        $data = $request->validated();
        $data['status_id'] = 1;
        $data['semester_id'] = 1;
        $data['seminar_hours'] = 0;
        $data['mid_and_final_exam_hours'] = 0;
        $data['independent_work_hours'] = 0;
        $data['is_training'] = 1;

        $syllabus = Syllabus::create($data);
        $syllabus->lecturers()->attach(array_column($request->lecturers, 'lecturer_id'));
        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }
        $parents = [];
        foreach ($request->exams as $exam) {
            if (isset($exam['parent_id']) && $exam['parent_id'] !== '' && array_key_exists($exam['parent_id'], $parents)) {
                $service->StoreExam($syllabus->id, $exam);
            } else {
                $assignment = $service->StoreExam($syllabus->id, $exam);
                $parents[$exam['id']] = $assignment->id;
            }
        }
        return response($syllabus->load(['learnYear.program:id,school_id']), 201);
    }

    public
    function edit(int $id): JsonResponse
    {
        $syllabus = Syllabus::with([
            'learnYear',
            'lecturerContactTimes' => function ($query) use ($id) {
                $query->where('syllabus_id', $id)->with('lecturer');
            },
            'assignments.assessmentComponent',
            'academicDegree',
        ])->findOrFail($id);
        $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
        $syllabus->setAttribute('program_id', $program_id);
        $data = [
            'syllabus' => $syllabus,
            'academic_degrees_list' => AcademicDegree::pluck('name_ka', 'id'),
        ];
        return response()->json($data);
    }

    public
    function update(UpdateSyllabusTrainingRequest $request, int $id, SyllabusService $service)
    {
        $syllabus = Syllabus::findOrFail($id);
        $syllabus->update($request->toArray());
        $syllabus->lecturers()->sync(array_column($request->lecturers, 'lecturer_id'), true);
        $service->deleteLecturerContactHours($syllabus->id);
        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }
        Assignment::where('syllabus_id', $syllabus->id)->delete();
        foreach ($request->exams as $exam) {
            $service->StoreExam($syllabus->id, $exam);
        }
        return response($syllabus->load(['learnYear.program:id,school_id']), 201);
    }

    public
    function destroy(int $id)
    {
        $syllabus = Syllabus::findOrFail($id);
        $syllabus->lecturers()->sync([]);
        $syllabus->delete();
        return response(null, 204);
    }
}
