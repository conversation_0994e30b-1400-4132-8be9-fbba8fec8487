<?php

namespace App\Http\Controllers\API\V1;

use App\Exports\CompareSyllabiHoursExport;
use App\Exports\ProgramWithGpaExport;
use App\Exports\Syllabus\GradeAnalyzeExport;
use App\Exports\SyllabusLiteratureExport;
use App\Filters\SchoolFilter;
use App\Filters\Syllabus\SyllabusFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProfessionStudentMarkRequest;
use App\Http\Requests\SetExamDateRequest;
use App\Http\Requests\StudentAttandanceRequest;
use App\Http\Requests\StudentMarkRequest;
use App\Http\Requests\Syllabus\EditAssignmentRequest;
use App\Http\Requests\Syllabus\StoreSyllabusRequest;
use App\Http\Requests\Syllabus\UpdateSyllabusRequest;
use App\Imports\ImportStudentMarks;
use App\Jobs\SendExamNotification;
use App\Models\Assignment;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Flow;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Semester;
use App\Models\Setting;
use App\Models\Status;
use App\Exports\Syllabus\StudentMarkExport;
use App\Models\Syllabus\LecturerContactTime;
use App\Models\Syllabus\Method;
use App\Models\Syllabus\Syllabus;
use App\Models\Syllabus\SyllabusPassAssignment;
use App\Models\Syllabus\SyllabusType;
use App\Models\SyllabusStudentGuest;
use App\Models\User\UserType;
use App\Models\Week;
use App\Services\Syllabus\SyllabusService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Log;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class SyllabusController extends Controller
{
    public function __construct(public SyllabusService $service)
    {
        $this->middleware(['permission:syllabus.index'])->only(['index']);
        $this->middleware(['permission:syllabus.create'])->only(['create']);
        $this->middleware(['permission:syllabus.store'])->only(['store']);
        $this->middleware(['permission:syllabus.show'])->only(['show']);
        $this->middleware(['permission:syllabus.edit'])->only(['edit']);
        $this->middleware(['permission:syllabus.update'])->only(['update']);
        $this->middleware(['permission:syllabus.destroy'])->only(['destroy']);
        $this->middleware(['permission:syllabus.export'])->only(['exportAsPdf']);
    }

    public function index(SyllabusFilter $filter): Response
    {
        return response([
            'syllabi' => Syllabus::filter($filter)->select(['id','is_training','is_profession','syllabus_type_id',
                'name','name_en','learn_year_id','status_id','semester_id',
                'academic_degree_id','code','credits','contact_hours','lecture_hours',
                'seminar_hours','mid_and_final_exam_hours','independent_work_hours',
                'total_hours', 'is_external'])
            ->with([
                'learnYear:id,name',
                'status:id,name_ka',
                'semester:id,name',
                'lecturers:id,first_name,last_name,user_id',
                //'weeks',
                //'assignments',
                'prerequisites:id,name,code',
                'academicDegree:id,name_en,name_ka',
                'curriculum.lecture:id,curriculum_id',
            ])->orderBy('semester_id')->get(),
        ]);
    }

    public function create(): Response
    {
        return response([
            'academic_degrees' => AcademicDegree::pluck('name_ka', 'id'),
            'statuses' => Status::pluck('name_ka', 'id'),
            'semesters' => Semester::pluck('name', 'id'),
            //'prerequisites'    => Syllabus::pluck('name', 'id'),
            //'learningOutcomes' => learningOutcome::pluck('title', 'id'),
            'methods' => Method::select(['id', 'title', 'title_en', 'text', 'text_en'])->get(),
            'learn_years' => LearnYear::pluck('name', 'id')
        ]);
    }

    public function store(StoreSyllabusRequest $request, SyllabusService $service)
    {
        $syllabus = Syllabus::create($request->toArray());

        $syllabus->prerequisites()->attach($request->prerequisites_ids);
        $syllabus->lecturers()->attach(array_column($request->lecturers, 'lecturer_id'));
        //$syllabus->learningOutcomes()->attach($request->learning_outcome_ids);
        $syllabus->methods()->attach($request->method_ids);

        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }
        if ($request->post('is_profession')==0) {
            foreach ($request->weeks as $week) {
                $service->StoreWeek($syllabus->id, $week);
            }
        }
        $parents = [];
        foreach ($request->exams as $exam) {

            if (isset($exam['parent_id']) && $exam['parent_id'] !== '' && array_key_exists($exam['parent_id'], $parents)) {
                $service->StoreExam($syllabus->id, $exam);
            } else {
                $assignment = $service->StoreExam($syllabus->id, $exam);
                $parents[$exam['id']] = $assignment->id;
            }
        }

//        foreach ($request->exams as $exam) {
//            if (!isset($exam['parent_id'])) {
//                $assignment = $service->StoreExam($syllabus->id, $exam);
//
//                $parents[$exam['id']] = $assignment->id;
//            }
//        }
//
//        foreach ($request->exams as $exam) {
//            if (isset($exam['parent_id'])) {
//                $exam['parent_id'] = $parents[$exam['parent_id']];
//
//                $service->StoreExam($syllabus->id, $exam);
//            }
//        }
        if ($request->post('is_profession')==0) {
            $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
            $school_id = Program::whereId($program_id)->first();

            $syllabus->setAttribute('school_id', $school_id->school_id);
            $syllabus->setAttribute('program_id', $program_id);
            $syllabus->setAttribute('flow_id', $syllabus->learn_year_id);
        }

        return response(
            $syllabus
                ->load(['lecturers.lecturerContactTimes',
                    'weeks',
                    'assignments',
                    'prerequisites',//'learningOutcomes',
                ]),
            201
        );
    }

    public
    function show(int $syllabusId)
    {
        $syllabus = Syllabus::with([
            'learnYear',
            'status',
            'semester',
            //'lecturers',
            'lecturerContactTimes' => function ($query) use ($syllabusId) {
                $query->where('syllabus_id', $syllabusId)->with('lecturer');
            },
            'weeks',
            'assignments',
            'prerequisites',
            'academicDegree',
            'learningOutcomes',
            'methods',
        ])->findOrFail($syllabusId);

        return response($syllabus);
    }

    public
    function edit(int $syllabusId): \Illuminate\Http\JsonResponse
    {
        $syllabus = Syllabus::with([
            'learnYear',
            'status',
            'semester',
            'lecturerContactTimes' => function ($query) use ($syllabusId) {
                $query->where('syllabus_id', $syllabusId)->with('lecturer');
            },
            'weeks',
            'assignments',
            'prerequisites',
            'academicDegree',
            'learningOutcomes',
            'methods',
        ])->findOrFail($syllabusId);
        $learn_years = LearnYear::whereIn('program_id', function ($query) use ($syllabus) {
            $query->select('program_id')
                ->from(with(new LearnYear)->getTable())
                ->where('id', $syllabus->learn_year_id);
        })->get();

        $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();

        $syllabus->setAttribute('program_id', $program_id);
        $syllabus->setAttribute('subject_count', $this->checkSequence($syllabus->learn_year_id, $syllabus->semester_id));

        $data = [
            'syllabus' => $syllabus,
            'academic_degrees_list' => AcademicDegree::pluck('name_ka', 'id'),
            'status_list' => Status::pluck('name_ka', 'id'),
            'semesters_list' => Semester::pluck('name', 'id'),
            'methods_list' => Method::select(['id', 'title', 'text'])->get(),
            'learn_years_list' => $learn_years
        ];

        return response()->json($data);
    }

    public
    function update(UpdateSyllabusRequest $request, int $syllabusId, SyllabusService $service)
    {
        $syllabus = Syllabus::findOrFail($syllabusId);

        $syllabus->update($request->toArray());
        $syllabus->prerequisites()->sync($request->prerequisites_ids);
        $syllabus->lecturers()->sync(array_column($request->lecturers, 'lecturer_id'));
        $syllabus->methods()->sync($request->method_ids);
        //$syllabus->learningOutcomes()->sync($request->learning_outcome_ids);
        $service->deleteLecturerContactHours($syllabus->id);
        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }

        Week::where('syllabus_id', $syllabus->id)->delete();
        if ($request->post('is_profession')==0) {
            foreach ($request->weeks as $week) {
                $service->StoreWeek($syllabus->id, $week, true);
            }
        }
//მუშა ვერსია, მოსაფიქრებელია
        $syllabus_assignments_change_status = Setting::where('key','syllabus_assignments_change_status')->first();
        if($syllabus_assignments_change_status->value == 1) {
            Assignment::where('syllabus_id', $syllabus->id)->delete();
            StudentAssignment::where('syllabus_id', $syllabus->id)->delete();
            $parents = [];
            foreach ($request->exams as $exam) {
                $service->StoreExam($syllabus->id, $exam);
            }
        }
//ენდ მუშა ვერსია


//        foreach ($request->exams as $exam) {
//            if (!isset($exam['parent_id'])) {
//                $assignment = $service->StoreExam($syllabus->id, $exam);
//
//                $parents[$exam['id']] = $assignment->id;
//            }
//        }
//
//        foreach ($request->exams as $exam) {
//            if (isset($exam['parent_id']) && array_key_exists($exam['parent_id'], $parents)) {
//                $exam['parent_id'] = $parents[$exam['parent_id']];
//                $service->StoreExam($syllabus->id, $exam);
//            }
//        }
        if ($request->post('is_profession')==0) {
            $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
            $school_id = Program::whereId($program_id)->first();

            $syllabus->setAttribute('school_id', $school_id->school_id);
            $syllabus->setAttribute('program_id', $program_id);
            $syllabus->setAttribute('flow_id', $syllabus->learn_year_id);
        }
        return response(
            $syllabus
                ->load([
                    'lecturers.lecturerContactTimes',
                    'weeks',
                    'assignments',
                    'prerequisites',
                    'learningOutcomes',
                ]),
            201
        );
    }

    public
    function destroy(int $syllabusId)
    {
        if (auth()->user()->id != 1) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        $syllabus = Syllabus::findOrFail($syllabusId);
        //detach related
        $syllabus->prerequisites()->sync([]);
        $syllabus->lecturers()->sync([]);
        $syllabus->weeks()->delete();
        //delete
        $syllabus->deleteOrFail();
        return response(null, 204);
    }

    public
    function showPDF(Syllabus $syllabus, $lang)
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        if ($lang == "ge") {
            $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
            $school_id = Program::whereId($program_id)->first();

            $syllabus->setAttribute('school_id', $school_id->school_id);
            $syllabus->setAttribute('program_id', $program_id);
            return view('pdf.syllabus', [
                'syllabus' => $syllabus->load([
                    'learnYear',
                    'status',
                    'semester',
                    'lecturerContactTimes' => function ($query) use ($syllabus) {
                        $query->where('syllabus_id', $syllabus)->with('lecturer');
                    },
                    'weeks',
                    'assignments',
                    'prerequisites',
                    'academicDegree',
                    'learningOutcomes',
                    'methods',
                ])
            ]);
        }
    }

    public
    function exportAsPdf(Syllabus $syllabus, $lang)
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        if ($lang == "ge") {
            $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
            $school_id = Program::whereId($program_id)->first();

            $syllabus->setAttribute('school_id', $school_id->school_id);
            $syllabus->setAttribute('program_id', $program_id);
            return PDF::loadView('pdf.syllabus', [
                'syllabus' => $syllabus->load([
                    'learnYear',
                    'status',
                    'semester',
                    'lecturerContactTimes.lecturer',
                    'weeks',
                    'assignments',
                    'prerequisites',
                    'academicDegree',
                    'learningOutcomes',
                    'methods',
                ])
            ])->stream('document.pdf');
        } else {
            return Pdf::loadView('pdf.syllabus_eng', [
                'syllabus' => $syllabus->load([
                    'learnYear',
                    'status',
                    'semester',
                    'lecturers.lecturerContactTimes',
                    'weeks',
                    'assignments',
                    'prerequisites',
                    'academicDegree',
                    'learningOutcomes',
                    'methods'
                ])
            ])->stream();
        }
    }

    public function checkSequence($flow, $semester): string
    {
        $program_id = LearnYear::where('id', $flow)->value('program_id');
        $program_ids = LearnYear::where('program_id', $program_id)->pluck('id')->toArray();

        $data = Syllabus::whereIn('learn_year_id', $program_ids)
            ->select('code')
            ->where('semester_id', $semester)
            ->where('lmb_id', Null)
            ->orderBy('code')
            ->withTrashed()
            ->get();

        $lastData = $data->last();

        if ($lastData && !empty($lastData->code)) {
            $parts = explode('.', $lastData->code);
            if (count($parts) == 4) {
                $parts[2] = intval($parts[2]);
                $newCode = $parts[2] + 1;
            } else {
                $newCode = 1;
            }
            $lastTwoDigits = substr(strval($newCode), -2);
        } else {
            $lastTwoDigits = '01';
        }
        return $lastTwoDigits;
    }

    public
    function checkPrerequisites($flow, $keyword = null): array
    {
        $query = Syllabus::whereLearnYearId($flow);

        if (!is_null($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('code', $keyword);
            });
        }

        $data = $query->select('id', 'name', 'code')->get();

        $result = $data->map(function ($syllabus) {
            return [
                'id' => $syllabus->id,
                'name' => $syllabus->name,
                'code' => $syllabus->code
            ];
        })->toArray();

        return $result;
    }

    public
    function findLecturer($keyword = null): \Illuminate\Http\JsonResponse
    {
        $lecturers = Lecturer::where('first_name', 'LIKE', '%' . $keyword . '%')
            ->orWhere('last_name', 'LIKE', '%' . $keyword . '%')
            ->select(['id', 'first_name', 'last_name', 'email', 'phone'])
            ->get();

        return response()->json($lecturers);
    }

    public
    function syllabusStudents(
        Request         $request,
        Syllabus        $syllabus,
        SyllabusService $syllabusService,
    )
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        $lectureDate = $request->lecture_date;
        $syllabusStudentsData = $syllabusService->syllabusStudents(
            $syllabus,
            $lectureDate,
            $request->student_group,
            $request->lecturer
        );
        return response()->json($syllabusStudentsData);
    }

    public
    function syllabusLectureDates(Syllabus $syllabus): JsonResponse
    {
        return response()->json([
            'data' => Lecture::whereSyllabusId($syllabus->id)
                ->whereDate('lecture_date', '<=', now()->format('Y-m-d'))
                ->pluck('lecture_date', 'id')
                ->toArray(),
            'status' => 200,
        ]);
    }

    public
    function setStudentMark(
        StudentMarkRequest $request,
        Syllabus           $syllabus,
        SyllabusService    $syllabusService
    ): JsonResponse
    {
        try {
            $studentMark = $syllabusService->setStudentMark(
                $syllabus->id,
                $request->assignment_id,
                $request->point,
                $request->student_id,
                $request->is_percent,
                $request->last_checkout_status_id
            );
            return response()->json($studentMark);
        } catch (\Exception $e) {
            Log::error('Error occurred while setting student mark: ' . $e->getMessage());
            return response()->json([
                'message' => 'ქულის დაწერა ვერ მოხერხდა!',
                'status' => 500
            ]);
        }
    }

    public
    function setNotPresentedStudents(
        StudentAttandanceRequest $request,
        Lecture                  $lecture,
        SyllabusService          $syllabusService
    ): JsonResponse
    {

        try {
            $setNotPresentedStudents = $syllabusService->setNotPresentedStudents(
                $lecture,
                $request->student_id,
                $request->nth_lecture
            );
            return response()->json($setNotPresentedStudents);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'არას დაწერა ვერ მოხერხდა',
                'status' => 500
            ]);
        }
    }

    public
    function syllabusAssessments(Syllabus $syllabus): JsonResponse
    {
        $data = $syllabus->assignments()
            ->with(['assessmentComponent:id,name_ka,name_en,type_id'])
            ->whereHas('assessmentComponent', function ($query) {
                $query->whereIn('type_id', [2, 3]);
            })
            ->get(['id', 'assessment_component_id', 'exam_date', 'expiration_date']);

        return response()->json($data);
    }


    public
    function setExamDates(SetExamDateRequest $request)
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        $assignments = Assignment::whereIn('id', collect($request->assignments)->pluck('id'))->get();

        foreach ($assignments as $assignment) {
            $dates = collect($request->assignments)->firstWhere('id', $assignment->id);

            if ($dates['exam_date']) {
                $assignment->update([
                    'exam_date' => $dates['exam_date'],
                    'expiration_date' => $dates['expiration_date'] ?? null
                ]);

                SendExamNotification::dispatchAfterResponse($assignment, $dates['exam_date']);
            }

        }

        return response()->json([
            'message' => 'Assignment dates has been successfully set up'
        ]);
    }

    public function importStudentMarks(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,csv,xls',
        ]);

        try {
            Excel::import(new ImportStudentMarks, $request->file('file'));
        }catch (\Exception $exception) {
            // response file's error line
            return response()->json(['data' => ['errors'=>$exception->getMessage()], 'success' => false, 'line' => $exception->getMessage(), 'status' => 500]);
        }

        return response()->json([
            'data' => [],
            'success' => true,
            'message' => 'ნიშნები აიტვირთა!',
            'status' => 200
    ]);
    }


    public function exportStudentMarks($id, Request $request): string
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return response()->json([
                'status' => 403,
                'message' => __('Forbidden')
            ]);
        }

        $groupId = $request->group_id;
        $syllabus = Syllabus::query()
            ->select(['id', 'name', 'semester_id','is_profession'])
            ->with(['students.student', 'students' => function($history) use($groupId){
                $history->when($groupId, function ($history) use ($groupId){
                    $history->whereHas('student', function ($student) use ($groupId) {
                        $student->where('group_id', $groupId);
                    });
                });
            }])
            ->find($id)
        ;

        $studentGuests = SyllabusStudentGuest::query()
            ->with('student')
            ->whereNotIn('student_id', $syllabus->students->pluck('student_id'))
            ->where('student_group_id', $groupId)
            ->where('syllabus_id', $id)
            ->get()
        ;

        $allStudents = $syllabus->students->merge($studentGuests->pluck('student'));
        $syllabus->students = $allStudents;

        $randomStr = Str::random(3);
        $syllabusShortName = Str::limit($syllabus->name, 7);
        $fileName = "{$syllabusShortName}-{$randomStr}.xlsx";
        $storage = config('excel.storage');

        Excel::store(
            new StudentMarkExport($syllabus, false),
            $fileName,
            $storage
        );

        return "excel/$storage/$fileName";
    }

    public
    function exportStudentMarkTemplate(Syllabus $syllabus): string
    {
        $randomStr = Str::random(6);
        $syllabusShortName = Str::limit($syllabus->name, 7);
        $fileName = "{$syllabusShortName}-{$randomStr}.xlsx";
        Excel::store(
            new StudentMarkExport($syllabus, true),
            $fileName,
            config('excel.storage')
        );
        return 'excel/' . config('excel.storage') . '/' . $fileName;
    }

    public
    function subjectList(SyllabusService $syllabusService): JsonResponse
    {
        $subjectData = $syllabusService->subjectList();
        $schoolFilter = (new SchoolFilter(request()));
        return response()->json([
            'journal' => $subjectData,
            'school' => School::filter($schoolFilter)->pluck('name_ka', 'id'),
            'learnYears' => Flow::orderByDesc('id')->pluck('name', 'id'),
        ]);
    }

    public function setProfessionStudentMark(
        ProfessionStudentMarkRequest $request,
        Syllabus                     $syllabus,
        SyllabusService              $syllabusService
    ): JsonResponse
    {
        try {
            $studentMark = $syllabusService->setProfessionStudentMark(
                $syllabus->id,
                $request->assignment_id,
                $request->mark,
                $request->student_id
            );
            return response()->json($studentMark);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'ქულის დაწერა ვერ მოხერხდა!',
                'status' => 500
            ]);
        }
    }

    public function createOrUpdateAssignment(EditAssignmentRequest $request): JsonResponse
    {
        $assignment = Assignment::query()->find($request->id);
        if($assignment) {
            $updated = $assignment->update($request->validated());
        }else{
            $assignment = Assignment::query()->create($request->validated());
        }

        return response()->json([
            'message' => 'სილაბუსი წარმატებით განახლდა!',
            'status' => 200,
            'data' => $assignment
        ]);
    }

    public function deleteAssignment($id): JsonResponse
    {
        $deleted = Assignment::destroy($id);

        if ($deleted)
        {
            $deleted = StudentAssignment::query()->where('assignment_id', $id)->delete();
        }

        return $deleted?response()->json([
                'message' => 'ნიშანი წარმატებით წაიშალა!',
                'status' => 200,
                'deleted' => $deleted
            ]):response()->json([
                'message' => 'ნიშანი ვერ მოიძებნა',
                'status' => 404,
                'deleted' => $deleted
            ]);
    }

    public function syllabusCopy(Request $request)
    {
        $request->validate([
            'syllabus_ids' => 'required|array',
            'syllabus_ids.*' => 'exists:syllabi,id',
//            'name' => 'string|nullable',
            'learn_year_id' => 'required|integer',
        ]);

        foreach ($request->syllabus_ids as $syllabusId)
        {
            $originalSyllabus = Syllabus::query()
                ->find($syllabusId);

            $newSyllabus = $originalSyllabus->replicate();

//            $newSyllabus->name = $datum['name'] ?? $newSyllabus->name;
            $newSyllabus->learn_year_id = $request->learn_year_id;
            $newSyllabus->save();


            $this->service->copySyllabusRelationData(
                $newSyllabus,
                $originalSyllabus
            );
        }

        return response()->json([
            'status' => 200,
            'success' => true
        ]);
    }

    public function syllabusCopyByLearnYear(Request $request)
    {
        $request->validate([
            'learn_year_id' => 'required|integer',
        ]);

        $syllabus = Syllabus::query()
            ->where('learn_year_id', $request['learn_year_id'])
            ->get()
        ;

        foreach ($syllabus as $originalSyllabus)
        {
            $newSyllabus = $originalSyllabus->replicate();

            $this->service->copySyllabusRelationData(
                $newSyllabus,
                $originalSyllabus
            );
        }


        return response()->json([
            'status' => 200,
            'success' => true
        ]);

    }

    public function deleteSyllabusHistory(Request $request): JsonResponse
    {
        $request->validate([
            'id' => 'required|exists:student_syllabus_history',
        ]);

        $history = StudentSyllabusHistory::query()
            ->with('syllabus')
            ->whereHas('syllabus')
            ->find($request->id);

        $deleted = false;
        if ($history)
        {
//            if($history->manual_addition!=0 or $history->syllabus->is_external!=0 or $history->lmb!=0)
//            {
                if ($history->syllabus->is_external==1)
                {
                    $history->syllabus->delete();
                }
                $deleted = $history->delete();
            //}
        }
        $syllabusHistoriesRequest = Request::create(
            '/syllabusHistories',
            'GET',
            ['student_id' => $request->post('student_id')]
        );
        $syllabusHistoriesResponse = $this->syllabusHistories($syllabusHistoriesRequest);
        $syllabusHistoriesData = json_decode($syllabusHistoriesResponse->getContent(), true);

        return response()->json([
            'status' => $deleted ? 200 : 404,
            'success' => (boolean) $deleted,
            'data' => $syllabusHistoriesData,
            'message' => $deleted ? 'ჩანაწერი წაიშალა' : 'თქვენ არ გაქვთ წაშლის უფლება, მიმართეთ ადმინისტრატორს.'
        ]);

    }

    public function createExternalSyllabus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'semester_id' => 'required',
            'code' => 'required',
            'name' => 'required',
            'name_en' => 'required',
            'credits' => 'required',
            'student_id' => 'required|exists:students,id',
            'point' => 'required|numeric',
            'is_passed' => 'boolean|nullable',
        ]);

        $academicDegreeId = Student::query()
            ->select(['id','program_id'])
            ->find($request->post('student_id'))
            ->program
            ->academic_degree_id
        ;

        $syllabus = Syllabus::query()->create(
            array_merge($validated, [
                'is_external' => 1,
                'status_id' => 1,
                'academic_degree_id' => $academicDegreeId,
                'contact_hours' => 0,
                'lecture_hours' => 0,
                'seminar_hours' => 0,
                'mid_and_final_exam_hours' => 0,
                'independent_work_hours' => 0,
                'total_hours' => 0,
            ]));

        $validated['is_passed'] = $validated['is_passed'] ?? round($validated['point']) >= 51;

        StudentSyllabusHistory::query()
            ->create(array_merge($validated, ['is_closed' => 1, 'syllabus_id' => $syllabus->id, 'manual_addition' => 1]));

        $syllabusHistoriesRequest = Request::create(
            '/syllabusHistories',
            'GET',
            ['student_id' => $request->post('student_id')]
        );
        $syllabusHistoriesResponse = $this->syllabusHistories($syllabusHistoriesRequest);
        $syllabusHistoriesData = json_decode($syllabusHistoriesResponse->getContent(), true);

        return response()->json([
            'status' => 200,
            'success' => true,
            'data' => $syllabusHistoriesData
        ]);
    }

    public function createSyllabusHistory(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'syllabus_id' => 'required',
            'point' => 'required|numeric',
            'is_passed' => 'boolean|nullable',
            'credits' => 'numeric|nullable',
            'semester_id' => 'nullable|:semesters,id'
        ]);

        $validated['is_passed'] = $validated['is_passed'] ?? round($validated['point']) >= 51;

        $history = StudentSyllabusHistory::query()
            ->create(array_merge($validated, ['is_closed' => 1, 'manual_addition' => 1]));

        $syllabusHistoriesRequest = Request::create(
            '/syllabusHistories',
            'GET',
            ['student_id' => $request->post('student_id')]
        );
        $syllabusHistoriesResponse = $this->syllabusHistories($syllabusHistoriesRequest);
        $syllabusHistoriesData = json_decode($syllabusHistoriesResponse->getContent(), true);

        return response()->json([
            'status' => $history ? 200 : 404,
            'success' => (bool) $history,
            'data' => $syllabusHistoriesData
        ]);

    }

    public function editSyllabusHistory(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'id' => 'required|exists:student_syllabus_history',
            'student_id' => 'exists:students,id',
            'syllabus_id' => 'integer',
            'point' => 'required|numeric',
            'is_passed' => 'boolean|nullable',
            'credits' => 'numeric|nullable',
            'is_closed' => 'boolean|nullable',
            'semester_id' => 'exists:semesters,id',
            'code' => 'sometimes|string',
            'name' => 'sometimes|string',
            'name_en' => 'sometimes|string',
        ]);

        $point =  $request->post('point');
        $validated['is_passed'] = $validated['is_passed'] ?? round($point) >= 51;

        $history = StudentSyllabusHistory::query()
            ->find($request->post('id'));
        $syllabus = Syllabus::find($history->syllabus_id);
        if($syllabus->credits == $request->post('credits')){
            $validated['credits'] = null;
        } else {
            $validated['credits'] = $request->post('credits');
        }
        if($syllabus->semester_id == $request->post('semester_id')){
            $validated['semester_id'] = null;
        } else {
            $validated['semester_id'] = $request->post('semester_id');
        }
        if ($request->has('name') && $request->post('name') != '') {
            $syllabus->name = $validated['name'];
            $syllabus->name_en = $validated['name_en'];
            $syllabus->code = $validated['code'];
            $syllabus->save();
        }
        $history->update($validated);

        $syllabusHistoriesRequest = Request::create(
            '/syllabusHistories',
            'GET',
            ['student_id' => $request->post('student_id')]
        );
        $syllabusHistoriesResponse = $this->syllabusHistories($syllabusHistoriesRequest);
        $syllabusHistoriesData = json_decode($syllabusHistoriesResponse->getContent(), true);

        return response()->json([
            'status' => $history ? 200 : 404,
            'success' => (bool) $history,
            'data' => $syllabusHistoriesData,
        ]);
    }

    public function syllabusHistories(Request $request): JsonResponse
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
        ]);

        return response()->json([
            'status' => 200,
            'success' => true,
            'data' => StudentSyllabusHistory::studentHistories($request->student_id)
        ]);
    }

    public function listSyllabus(Request $request): JsonResponse
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
        ]);

        $student = Student::find($request->get('student_id'));
        $flow = $student->learn_year_id;
        $data = Syllabus::query()
            ->select([
                'id', 'name', 'name_en', 'semester_id', 'code', 'credits'
            ])
            ->where('learn_year_id', $flow)
            ->orderBy('semester_id')
            ->orderBy('status_id')
            ->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function gradeAnalyzeExport(Request $request)
    {
        return Excel::download(new GradeAnalyzeExport($request->program_id, $request->learn_year_id, $request->flow_id), 'grade-analyze-export-'.now().'.xlsx');
    }

    public function syllabusLiteratureExport()
    {
        return Excel::download(new SyllabusLiteratureExport, 'syllabus-literature-export.xlsx');
    }

    public function exportProgramWithGpa()
    {
        return Excel::download(new ProgramWithGpaExport(), 'student-program-gpa.xlsx');
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function compareSyllabiHours(): BinaryFileResponse
    {
        return Excel::download(new CompareSyllabiHoursExport, 'compare-syllabi-hours.xlsx');
    }

    public function lectureDelete(Request $request)
    {
        $request = $request->validate(['id' => 'required']);
        $deleted = Lecture::destroy($request['id']);

        return [
            'code' => 200,
            'message' => 'Lecture successfully deleted!',
            'id' => $deleted
        ];
    }

}
