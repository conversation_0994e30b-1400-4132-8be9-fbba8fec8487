<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Role\StoreRequest;
use App\Http\Requests\Role\UpdateRequest;
use App\Models\Permission;
use App\Models\Reestry\Program\Program;
use App\Models\Role;
use Illuminate\Http\Response;
use function response;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:roles.index'])->only(['index']);
        $this->middleware(['permission:roles.store'])->only(['store']);
        $this->middleware(['permission:roles.show'])->only(['show']);
        $this->middleware(['permission:roles.update'])->only(['update']);
        $this->middleware(['permission:roles.destroy'])->only(['destroy']);
    }

    /**
     * @OA\Get(
     * path="/roles",
     * tags={"Roles"},
     * summary="List of roles",
     * description="List of roles",
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="title",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                    @OA\Property(
     *                         property="permissions",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                     example={
     *                        "name" : "test name",
     *                        "permissions" : "list of permissions for role"
     *                     }
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function index(): Response
    {
        return response([
            'roles' => Role::with(['permissions:title'])->get(),
            'permissions' => Permission::pluck('title', 'id'),
        ]);
    }

    /**
     * @OA\Post(
     * path="/roles",
     * tags={"Roles"},
     * summary="Add role",
     * description="Add role",
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreRoleRequest",
     *               required={"title","permissions"},
     *               @OA\Property(property="title", type="string"),
     *               @OA\Property(property="permissions",type="array",@OA\Items(
     *          type="integer",
     *      ),)
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=201,
     *          description="Role created!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function store(StoreRequest $request): Response
    {
        $role = Role::create($request->validated());
        $role->permissions()->sync($request->permissions, []);
        return response($role->load(['permissions']), Response::HTTP_CREATED);
    }

    /**
     * @OA\Get(
     * path="/roles/{id}",
     * tags={"Roles"},
     * summary="Show role",
     * description="Show role",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="ok",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                     @OA\Property(
     *                         property="title",
     *                         type="string",
     *                         description="String",
     *                     ),
     *                    @OA\Property(
     *                         property="permissions",
     *                         type="string",
     *                         description="String",
     *                     ),
     *
     *                     example={
     *                        "title" : "test title",
     *                        "permissions" : "list of permissions for role"
     *                     }
     *
     *                 )
     *             )
     *         }
     *     ),
     *      @OA\Response(response=400, description="Bad request"),
     * )
     */

    public function show($role): Response
    {
        return response(Role::with(['permissions', 'programs'])->find($role));
    }

    /**
     * @OA\Put(
     * path="/roles/{id}",
     * tags={"Roles"},
     * summary="Update Role",
     * description="Update Role",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     *      )
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(),
     *         @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *               type="object",
     *               schema="StoreUserDriverLicenseRequest",
     *               required={"title","permissions"},
     *               @OA\Property(property="title", type="string"),
     *               @OA\Property(property="permissions",type="array",@OA\Items(
     *          type="integer",
     *      ),)
     *            ),
     *        ),
     *    ),
     *      @OA\Response(
     *          response=200,
     *          description="Role updated!",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Resource Not Found"),
     * )
     */

    public function update(UpdateRequest $request, $role): Response
    {
        $role = Role::findOrFail($role);
        $role->update($request->validated());
        $role->permissions()->sync($request->permissions);
        return response($role->load(['permissions']));
    }

    /**
     * @OA\Delete(
     * path="/roles/{id}",
     * tags={"Roles"},
     * summary="Delete role",
     * description="Delete role",
     *     @OA\Parameter(
     *     required=true,
     *     in="path",
     *     name="id",
     *     @OA\Schema(
     *     type="integer"
     * ),
     * ),
     *      @OA\Response(
     *          response=204,
     *          description="Delete role",
     *          @OA\JsonContent()
     *       ),
     *      @OA\Response(response=422, description="Id field is required!"),
     * )
     * @throws \Throwable
     */

    public function destroy($role): Response
    {
        Role::findOrFail($role)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
