<?php

namespace App\Http\Controllers\API\V1;

use App\Filters\ElBookFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Elbook\StoreRequest;
use App\Http\Requests\Elbook\UpdateRequest;
use App\Http\Resources\ElBookResource;
use App\Models\ElBook\ElBook;
use App\Models\Elbook\ElBookFile;
use App\Models\ElBookTopic;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Topic;
use App\Services\ElBookService;
use App\Services\ImageService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use function response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class ElBookController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:library.index'])->only(['index']);
        $this->middleware(['permission:library.store'])->only(['store']);
        $this->middleware(['permission:library.show'])->only(['show']);
        $this->middleware(['permission:library.update'])->only(['update']);
        $this->middleware(['permission:library.destroy'])->only(['destroy']);
    }

    public function index(ElBookFilter $filter)
    {
        $data['library'] = ElBook::filter($filter)->with(['lecturer', 'files', 'topics'])->OrderByDesc('id')->paginate(50);
        $additionalData = [
            'topics' => Topic::pluck('title', 'id'),
            'lecturers' => Lecturer::select(['id', 'first_name', 'last_name'])->get()
        ];
        $data['additional'] = $additionalData;

        return response()->json($data);
    }

    public function store(StoreRequest $request, ImageService $imageService): JsonResponse
    {
        $validated = $request->validated();
        $validated['published_date'] = $request->published_date
            ? Carbon::createFromFormat('d-m-Y', $request->published_date) : null;
        $elBook = ElBook::create($validated);
        if ($request->has('files')) {
            foreach ($validated['files'] as $key => $file) {
                $name = $validated['files'][$key]->getClientOriginalName();
                $validated['files'][$key] = $imageService
                    ->upload($file, '/elbooks/' . $validated['title']);
                DB::insert('insert into el_book_files (el_book_id, filename,path) values (?, ?,?)'
                    , [$elBook->id, $name, $validated['files'][$key]]);
            }
        }
        if ($request->has('topics')) {
            ElBookService::syncTopics($elBook, $request->topics);
        }
        return (new ElBookResource($elBook))
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    public
    function show(int $elBook): ElBookResource
    {
        return new ElBookResource(ElBook::findOrFail($elBook));
    }

    public
    function update(UpdateRequest $request, int $elBook, ImageService $imageService): ElBookResource
    {
        $validated = $request->validated();
        $validated['published_date'] = $request->published_date
            ? Carbon::createFromFormat('d-m-Y', $request->published_date): '';
        if ($request->has('files')) {
            foreach ($validated['files'] as $key => $file) {
                $name = $validated['files'][$key]->getClientOriginalName();
                $validated['files'][$key] = $imageService
                    ->upload($file, '/elbooks/' . $validated['title']);
                DB::insert('insert into el_book_files (el_book_id, filename,path) values (?, ?,?)'
                    , [$elBook, $name, $validated['files'][$key]]);
            }
        }
        if ($request->has('deleted_files')) {
            foreach ($request->deleted_files as $deletedFile) {
                Storage::delete($deletedFile);
                ElBookFile::where([
                    'path' => $deletedFile,
                    'el_book_id' => $elBook
                ])->delete();
            }
        }
        $elBook = ElBook::findOrFail($elBook);
        $elBook->update($validated);
        ElBookTopic::whereElBookId($elBook->id)->delete();
        if ($request->has('topics')) {
            ElBookService::syncTopics($elBook, $request->topics);
        }
        return new ElBookResource($elBook);
    }

    public
    function destroy(int $elBook): Response
    {
        ElBook::findOrFail($elBook)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
