<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\AssessmentComponentRequest;
use App\Models\AssessmentComponent;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AssessmentComponentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:assessments.index'])->only(['index']);
        $this->middleware(['permission:assessments.store'])->only(['store']);
        $this->middleware(['permission:assessments.show'])->only(['show']);
        $this->middleware(['permission:assessments.update'])->only(['update']);
        $this->middleware(['permission:assessments.destroy'])->only(['destroy']);
    }

    public function list(Request $request)
    {
        $assessmentComponents = AssessmentComponent::when($request->has('type_id'), function ($query) use ($request) {
            $query->where('type_id', $request->type_id);
        })->when($request->has('keyword'), function ($query) use ($request) {
            $query->where('name_ka', 'like', '%' . $request->keyword . '%')
                ->orWhere('name_en', 'like', '%' . $request->keyword . '%');
        })->when($request->has('is_parent'), function ($query) use ($request) {
            $query->where('is_parent', intval($request->is_parent));
        })->orderBy('name_ka')
            ->get();

        return response($assessmentComponents);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = AssessmentComponent::when($request->has('type_id'), function ($query) use ($request) {
            $query->where('type_id', $request->type_id);
        })->when($request->has('keyword'), function ($query) use ($request) {
            $query->where('name_ka', 'like', '%' . $request->keyword . '%')
                ->orWhere('name_en', 'like', '%' . $request->keyword . '%');
        })->when($request->has('is_parent'), function ($query) use ($request) {
            $query->where('is_parent', intval($request->is_parent));
        });
        $assessmentComponents['assessments'] = $query->paginate(50);

        return response($assessmentComponents);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(AssessmentComponentRequest $request): Response
    {
        $assessmentComponent = AssessmentComponent::create($request->validated());

        return response($assessmentComponent, Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $assessmentComponent = AssessmentComponent::findOrFail($id);

        return response($assessmentComponent);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(AssessmentComponentRequest $request, $id): Response
    {
        $assessmentComponent = AssessmentComponent::findOrFail($id);
        $assessmentComponent->update($request->validated());

        return response($assessmentComponent, Response::HTTP_OK);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $assessmentComponent = AssessmentComponent::findOrFail($id);
        $assessmentComponent->delete();

        return response(null, 204);
    }
}
