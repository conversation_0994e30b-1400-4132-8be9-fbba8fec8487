<?php

namespace App\Http\Controllers\API\V1\Curriculum;

use App\Http\Controllers\Controller;
use App\Models\Curriculum\Curriculum;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Program\Program;
use App\Models\Setting;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class JournalController extends Controller
{

    public function filterDataList(): JsonResponse
    {
        $flows = Flow::select(['id', 'name'])->get();
        $programs = Program::select(['id', 'name_ka'])->get();
        return response()->json([
            'flows' => $flows,
            'programs' => $programs
        ]);
    }

    public function index(Request $request): JsonResponse
    {
        $syllabuses = Syllabus::with(['semester'])
            ->select(['id', 'name', 'semester_id', 'code', 'credits']);
        if ($request->has('programs')) {
            $syllabuses->whereHas('curriculum.flow', function ($item) {
                return $item->whereIn('program_id', json_decode(request()->get('programs')));
            });
        }
        if ($request->has('flows')) {
            $syllabuses->whereHas('curriculum', function ($item) {
                $flows = json_decode(request()->get('flows'));
                return $item->whereIn('flow_id', $flows);
            });
        } else {
            $syllabuses->whereHas('curriculum', function ($item) {
                $currentSemesterId = Setting::where('key', '=', 'current_semester')
                    ->first()->value;
                return $item->where('flow_id', $currentSemesterId);
            });
        }
        $syllabuses = $syllabuses->get();
        $syllabuses->map(function ($item) {
            $syllabusService = new SyllabusService();
            $item->studentGroups = $syllabusService->setStudentGroups($item->id);
            //$item->lecturers = $syllabusService->setLecturers($item->id)['lecturer_names'];
            return $item;
        });
        return response()->json([
            'syllabuses' => $syllabuses
        ]);
    }

    public function show(int $id): Response
    {
        $curriculum = Curriculum::where('syllabus_id', $id)->firstOrFail();

        $curriculum->load([
            'syllabus:id,name,semester_id' => [
                'assignments',
                'lectures.students'
            ],
            'lecture:id,curriculum_id,free_places,registered_free_students' => [
                'times:id,curriculum_lecture_id,auditorium_id,lecturer_id,payment_per_hour,is_lecture' => [
                    'auditorium:id,name,quantity',
                    'lecturer:id,first_name,last_name',
                    'studentGroups',
                ],
            ],
        ]);

        // Convert the lectures Eloquent Collection into a Base Collection
        $lecturesCollection = new Collection([$curriculum->lecture]);

        // Map the data to the desired format
        $formattedData = $lecturesCollection->map(function ($lecture) use ($curriculum) {
            // Extract subject_name and group_name
            $subjectName = $curriculum->syllabus->name;
            $semester = $curriculum->syllabus->semester_id;
            $course = 0;
            if ($semester == 1 || $semester == 2) {
                $course = 1;
            } elseif ($semester == 3 || $semester == 4) {
                $course = 2;
            } elseif ($semester == 5 || $semester == 6) {
                $course = 3;
            } elseif ($semester == 7 || $semester == 8) {
                $course = 4;
            }
            $groupName = $lecture->times[0]->studentGroups[0]->name_ka ?? '';


            // Group children by parent_id
            $childrenByParent = $curriculum->syllabus->assignments->filter(function ($assignment) {
                return !$assignment->assessmentComponent->is_parent;
            })->groupBy('parent_id');

            $assignments = $curriculum->syllabus->assignments->map(function ($assignment) use ($childrenByParent) {
                $data = [
                    'id' => $assignment->assessmentComponent->id,
                    'name' => $assignment->assessmentComponent->name_ka,
                    'type_id' => $assignment->assessmentComponent->type_id,
                    'is_parent' => $assignment->assessmentComponent->is_parent,
                    'parent_id' => $assignment->parent_id,
                    'score' => $assignment->score,
                    'min_score' => $assignment->min_score,
                    'calculation_type' => $assignment->calculation_type,
                    'total' => 0,
                ];

                if ($assignment->assessmentComponent->is_parent) {
                    $data['children'] = $childrenByParent->get($assignment->assessmentComponent->id, []);
                }

                return $data;
            })->filter(function ($assignment) {
                return $assignment['parent_id'] == 0;
            });


            $students = $curriculum->syllabus->lectures;


            // Return the formatted data
            return [
                'subject_name' => $subjectName,
                'course' => $course,
                'group_name' => $groupName,
                'total' => 30,
                'attendance' => 1,
                'attendance2' => 1,
                'attendance_percent' => 11,
                'assignments' => $assignments,
                'students' => $students,
            ];
        });

        return response($formattedData);

        //        'syllabus' => function($query) {
//        $query->select(['id', 'name'])
//            ->selectRaw('(CASE
//                                WHEN semester_id IN (1,2) THEN 1
//                                WHEN semester_id IN (3,4) THEN 2
//                                WHEN semester_id IN (5,6) THEN 3
//                                WHEN semester_id IN (7,8) THEN 4
//                            END) as course');
    }
}
