<?php

namespace App\Http\Controllers\API\V1\Curriculum;

use App\Http\Controllers\Controller;
use App\Http\Requests\Curriculum\CurriculumRequest;
use App\Http\Requests\Curriculum\FreeTimeSearchRequest;
use App\Http\Requests\CurriculumRemoveStudentRequest;
use App\Http\Requests\UpdateCurriculumRequest;
use App\Jobs\Curriculum\GenerateLectureJob;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class CurriculumController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:curriculum.index'])->only(['index']);
        $this->middleware(['permission:curriculum.copyCurriculum'])->only(['copyCurriculum']);
        $this->middleware(['permission:curriculum.store'])->only(['save']);
    }

    public function index(int $syllabus): Response
    {
        $check = Syllabus::where('id', $syllabus)->first();
        if ($check) {
            $curriculum = Curriculum::firstOrCreate(['syllabus_id' => $syllabus]);

            $curriculum->load([
                'syllabus:id,learn_year_id,status_id,semester_id,academic_degree_id,code,contact_hours,lecture_hours,seminar_hours',
                'lecture:id,curriculum_id,free_places,registered_free_students,lectures_count' => [
                    'times:id,curriculum_lecture_id,week_day,start_time,end_time,auditorium_id,lecturer_id,lecturer_accounting_code,payment_per_hour,is_lecture,lecturer_start_date,lecturer_end_date' => [
                        'auditorium:id,name,quantity',
                        'lecturer:id,first_name,last_name',
                        'studentGroups',
                    ],
                ],
            ]);
            $curriculum['learn_years'] = Flow::orderByDesc('id')->get();
            if (!$curriculum->lecture) {
                $curriculum->update_status = false;
            } else {
                $curriculum->update_status = true;
            }
            return response($curriculum);
        } else {
            return response('კურსი არ არსებობს', 404);
        }
    }

    public function copyCurriculum(int $targetLearnYear, int $sourceLearnYear)
    {
        $date = Carbon::now();

        $syllabus = Syllabus::where('learn_year_id', $sourceLearnYear)->get();
        foreach ($syllabus as $item) {
            $newSyllabus = $item->replicate();
            $newSyllabus->learn_year_id = $targetLearnYear;
            $newSyllabus->created_at = $date;
            $newSyllabus->updated_at = $date;
            $newSyllabus->save();

            $curriculum = Curriculum::where('syllabus_id', $item->id)->first();
            $newCurriculum = $curriculum->replicate();
            $newCurriculum->syllabus_id = $newSyllabus->id;
            $newCurriculum->created_at = $date;
            $newCurriculum->updated_at = $date;
            $newCurriculum->save();

            $lectures = CurriculumLecture::where('curriculum_id', $curriculum->id)->get();
            foreach ($lectures as $lecture) {
                $newLecture = $lecture->replicate();
                $newLecture->curriculum_id = $newCurriculum->id;
                $newLecture->created_at = $date;
                $newLecture->updated_at = $date;
                $newLecture->save();
                $times = CurriculumLectureTime::where('curriculum_lecture_id', $lecture->id)->get();
                foreach ($times as $time) {
                    $newTime = $time->replicate();
                    $newTime->curriculum_lecture_id = $newLecture->id;
                    $newTime->created_at = $date;
                    $newTime->updated_at = $date;
                    $newTime->save();
                }
            }

            GenerateLectureJob::dispatchAfterResponse($curriculum);
        }

        return response([
            'message' => 'Curriculum was copied successfully',
        ], Response::HTTP_CREATED);
    }

    public function save(CurriculumRequest $request): Response
    {
        $syllabus = Syllabus::findOrFail($request->syllabus_id);
        $startDate = Carbon::createFromFormat('d-m-Y', $request->start_date);

        foreach ($request->lecture_groups as $lectureGroup) {
            $totalHours = [];

            foreach ($lectureGroup['times'] as $time) {
                if (!isset($totalHours[$time['is_lecture']])) {
                    $totalHours[$time['is_lecture']] = 0;
                }

                if ($startDate->dayOfWeek > $time['week_day']) {
                    $startDate = $startDate->addDays(7 + $time['week_day'] - $startDate->dayOfWeek);
                } elseif ($startDate->dayOfWeek < $time['week_day']) {
                    $startDate = $startDate->addDays($time['week_day'] - $startDate->dayOfWeek);
                }

                $hours = $time['is_lecture'] ? $syllabus->lecture_hours : $syllabus->seminar_hours;
                $startTime = Carbon::createFromFormat('H:i', $time['start_time']);
                $endTime = Carbon::createFromFormat('H:i', $time['end_time']);
                $lectureTime = $endTime->diffInMinutes($startTime);
                $totalHours[$time['is_lecture']] += $lectureTime;

                $weeks = ceil($totalHours[$time['is_lecture']] / $lectureTime);
//ამოწებს შეტანილ თარიღებს სილაბუსთან და გლობალურ საგნის დაწყება დასრულების დღეებთან
//                if ($startDate->addWeeks($weeks)->gt($request->end_date)) {
//                    return response([
//                        'message' => 'ლექტორის შეხვედრების რაოდენობა მეტია საგნის დაწყება დასრულების კვირების რაოდენობაზე.',
//                    ], Response::HTTP_BAD_REQUEST);
//                }
//                if ($totalHours[$time['is_lecture']] > Carbon::now()->addHours($hours)->diffInMinutes()) {
//                    return response([
//                        'message' => 'შეტანილი შეხვედრების საათების რაოდენობა არ ემთხვევა სილაბუსში შეტანილ სალექციო და სასემინარო საათების რაოდენობას.',
//                    ], Response::HTTP_BAD_REQUEST);
//                }
            }
        }

        $curriculum = Curriculum::where('syllabus_id', $request->syllabus_id)->first();

        if (!$curriculum) {
            $curriculum = new Curriculum();
            $curriculum->syllabus_id = $request->syllabus_id;
        }

        $curriculum->flow_id = $request->learn_year_id; //სასწავლო სემესტრი
        //$curriculum->flow_id = Setting::where('key', 'current_semester')->pluck('value')->first(); //old

        $curriculum->start_date = Carbon::createFromFormat('d-m-Y', $request->start_date);
        $curriculum->end_date = Carbon::createFromFormat('d-m-Y', $request->end_date);
        if ($request->has('allowed_amount_of_students') && $request->allowed_amount_of_students) {
            $curriculum->allowed_amount_of_students = $request->allowed_amount_of_students;
        }
        if ($request->has('student_flow_ids') && $request->student_flow_ids) {
            $curriculum->student_flow_ids = $request->student_flow_ids;
        }
        if ($request->has('minimum_amount_of_students') && $request->minimum_amount_of_students) {
            $curriculum->minimum_amount_of_students = $request->minimum_amount_of_students;
        }

        if ($request->has('registration_start_date') && $request->registration_start_date) {
            $curriculum->registration_start_date = Carbon::createFromFormat('d-m-Y H:i', $request->registration_start_date);
        }

        if ($request->has('registration_end_date') && $request->registration_end_date) {
            $curriculum->registration_end_date = Carbon::createFromFormat('d-m-Y H:i', $request->registration_end_date);
        }

        $curriculum->save();

        $lectureIds = [];

        $studentGroups = [];

        foreach ($request->lecture_groups as $lectureGroup) {
            $lecture = new CurriculumLecture();
            $lecture->curriculum_id = $curriculum->id;
            $lecture->lectures_count = $lectureGroup['lectures_count'];
            $lecture->save();
            $times = [];
            $lectureIds[] = $lecture->id;
            foreach ($lectureGroup['times'] as $time) {
                $lectureTime = new CurriculumLectureTime();
                $lectureTime->curriculum_lecture_id = $lecture->id;
                $lectureTime->lecturer_id = $time['lecturer_id'];
                $lectureTime->lecturer_start_date = ($time['lecturer_start_date'] ?
                    Carbon::createFromFormat('d-m-Y', $time['lecturer_start_date'])
                    : $curriculum->start_date);
                $lectureTime->lecturer_end_date = ($time['lecturer_end_date'] ?
                    Carbon::createFromFormat('d-m-Y', $time['lecturer_end_date']) :
                    $curriculum->end_date);
                $lectureTime->is_lecture = $time['is_lecture'];
                $lectureTime->lecturer_accounting_code = $time['lecturer_accounting_code'];
                $lectureTime->payment_per_hour = $time['payment_per_hour'];
                $lectureTime->week_day = $time['week_day'];
                $lectureTime->start_time = $time['start_time'];
                $lectureTime->end_time = $time['end_time'];
                $lectureTime->auditorium_id = $time['auditorium_id'] ?? null;
                $lectureTime->save();

                if (isset($time['student_groups'])) {
                    $lectureTime->studentGroups()->sync($time['student_groups']);
                    $studentGroups[] = $time['student_groups'];
                }
                $times[] = $lectureTime->id;
            }
            $lecture->times()->whereNotIn('id', $times)->get()->each(function ($lectureTime) {
                $lectureTime->studentGroups()->detach();
                $lectureTime->delete();
            });
        }

        CurriculumLecture::where('curriculum_id', $curriculum->id)
            ->whereNotIn('id', $lectureIds)
            ->delete();
//        if (Carbon::createFromFormat('d-m-Y', $request->start_date) > now()->addDays(10)) {
        GenerateLectureJob::dispatchAfterResponse($curriculum, $studentGroups);
//        }

        return response(['message' => 'Curriculum was created',], Response::HTTP_CREATED);
    }

    public function getFreeTimes(FreeTimeSearchRequest $request)
    {
        $freeTimes = [];

        $freeTimes[] = [
            'start_time' => 'არჩევა',
            'end_time' => 'არჩევა',
        ];

        foreach (range(8, 23) as $hour) {
            for ($minute = 0; $minute < 60; $minute += 5) {
                $startHour = sprintf('%02d', $hour);
                $startMinute = sprintf('%02d', $minute);
                $startTime = $startHour . ':' . $startMinute;

                $lectureTime = CurriculumLectureTime::where('week_day', $request->week_day)
                    ->where(function ($query) use ($request) {
                        $query->where('auditorium_id', $request->auditorium_id)
                            ->orWhere('lecturer_id', $request->lecturer_id);
                    })
                    ->where('start_time', '<=', $startTime)
                    ->where('end_time', '>', $startTime)
                    ->where('lecturer_start_date', '<=', $request->auditorium_start_date)
                    ->where('lecturer_end_date', '>=', $request->auditorium_end_date)
                    ->count();

                if ($lectureTime) {
                    continue;
                }

                $endMinute = $minute + 5;
                if ($endMinute >= 60) {
                    $endMinute -= 60;
                    $hour++;
                }
                $endHour = sprintf('%02d', $hour);
                $endMinute = sprintf('%02d', $endMinute);
                $endTime = $endHour . ':' . $endMinute;

                $freeTimes[] = [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                ];
            }
        }

        return response($freeTimes);
    }


    public
    function removeStudents(CurriculumRemoveStudentRequest $request): JsonResponse
    {
        $studentIds = $request->post('student_id');
        $syllabusId = $request->post('syllabus_id');
        $response = (new SyllabusService())->removeStudentFromSyllabus($syllabusId, $studentIds);
        return response()->json($response);
    }

    public function updateCurriculumLectures(UpdateCurriculumRequest $request): JsonResponse
    {
        $response = (new SyllabusService())->updateCurriculumLectures($request->lectures);
        return response()->json($response);
    }

    public function deleteCurriculum($curriculum): JsonResponse
    {
        $curriculum_data = Curriculum::whereSyllabusId($curriculum)->first();
        try {
            StudentSyllabusHistory::whereSyllabusId($curriculum_data->syllabus_id)
                ->delete();
            Lecture::whereSyllabusId($curriculum_data->syllabus_id)->delete();
            $curriculum_data->delete();
            return response()->json([
                'code' => 204,
                'message' => 'Curriculum successfully deleted!'
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'code' => 500,
                'message' => 'Unexpected error!',
                'debug' => $exception->getMessage()
            ]);
        }
    }

    public function curriculumLectureTimes(int $syllabusId): JsonResponse
    {
        $curriculumId = Curriculum::whereSyllabusId($syllabusId)->first()?->id;
        $curriculumLectureTimes = CurriculumLectureTime::select('id', 'week_day', 'start_time', 'end_time', 'auditorium_id', 'lecturer_id')
            ->whereHas('curriculumLecture.curriculum', function ($query) use ($curriculumId) {
                return $query->where('curriculum_id', $curriculumId);
            })->with(['lecturer', 'auditorium'])->get();
        return response()->json([
            'success' => true,
            'code' => 200,
            'data' => $curriculumLectureTimes
        ]);
    }
}
