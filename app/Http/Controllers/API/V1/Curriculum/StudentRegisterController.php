<?php

namespace App\Http\Controllers\API\V1\Curriculum;

use App\Http\Controllers\Controller;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\LectureStudent;
use App\Models\Lectures\Lecture;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Status;
use App\Services\DateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Predis\Client;

class StudentRegisterController extends Controller
{
    public function index()
    {
        $student = Student::whereUserId(auth()->id())->first();
        if (!$student) {
            return response()->json([
                'message' => 'Student not found',
                'code' => 404
            ]);
        }
        $flowId = Setting::where('key', '=', 'current_semester')
            ->first()
            ->value;
        $academicDegreeId = $student->program->academic_degree_id;
        $curriculums = Curriculum::select(
            'id',
            'syllabus_id',
            'registration_start_date',
            'registration_end_date',
            'language',
            'allowed_amount_of_students'
        )->with([
            'syllabus' => function ($query) use ($flowId, $academicDegreeId) {
                $isOdd = (int)$flowId % 2 === 0;
                return $query->select('id', 'name', 'credits')
                    ->where('academic_degree_id', $academicDegreeId)
                    ->whereIn('learn_year_id', $isOdd ? [2, 4, 6, 8] : [1, 3, 5, 7]);
            },
            'lecture:id,curriculum_id,free_places,registered_free_students' => [
                'times:id,curriculum_lecture_id,week_day,start_time,end_time,auditorium_id,lecturer_id' => [
                    'auditorium:id,name,student_aid',
                    'lecturer:id,first_name,last_name,photo'
                ]
            ]
        ])
            ->whereHas('lecture', function ($query) {
                $query->where('free_places', '>', 'registered_students');
            })
            ->whereHas('syllabus.status', function ($query) {
                return $query->whereIn('id', [
                    Status::MANDATORY_OPTIONAL,
                    Status::OPTIONAL
                ]);
            })
            ->where('registration_start_date', '<=', now())
            ->where('registration_end_date', '>=', now())
            ->whereNotIn(
                'syllabus_id',
                $student->syllabi->where('is_passed', '=', 1)
                    ->pluck('id')
            )
            ->get()
            ->filter(function ($curriculum) use ($student) {
                return ($curriculum->syllabus->prerequisites->isEmpty() ||
                        $curriculum->syllabus->prerequisites->filter(function ($prerequisite) use ($student) {
                            return !$student->syllabusHistory->contains('syllabus_id', $prerequisite->prerequisite_id);
                        })->isEmpty()
                    ) && $curriculum->allowed_amount_of_students > $curriculum->syllabus->students->count();
            })->map(function ($item) {
                $response = [];
                $response['curriculumId'] = $item['id'];
                $response['name'] = $item['syllabus']['name'];
                $response['credits'] = $item['syllabus']['credits'];
                $response['registrationStartDate'] = $item['registration_start_date'];
                $response['registrationEndDate'] = $item['registration_end_date'];
                $response['language'] = $item['language'];
                $response['freePlaces'] = $item['lecture']['free_places'];
                $response['registeredStudents'] = $item['lecture']['registered_free_students'];
                foreach ($item['lecture']['times'] as $time) {
                    $dateService = new DateService();
                    $response['times'][] = [
                        'weekDay' => $dateService->getWeekDayName(['week_day']),
                        'start_time' => $time['start_time'],
                        'end_time' => $time['end_time'],
                        'auditorium' => $time['auditorium']['name'],
                        'auditoriumAid' => $time['auditorium']['student_aid'] ? 'შშმ ადაპტირებული' : 'არ არის შშმ ადაპტირებული',
                        'lecturer' => $time['lecturer']['first_name'] . " " . $time['lecturer']['last_name'],
                        'lecturerPhoto' => $time['lecturer']['photo'],
                    ];
                }
                return $response;
            });

        return response()->json([
            'data' => $curriculums
        ]);
    }

    public function curriculumSelection()
    {
        $student = Student::query()
            ->where('user_id', auth()->id())
            ->first();

        $studentFlowId = $student->learn_year_id;

        $syllabusHistories = StudentSyllabusHistory::query()
            ->where('student_id', $student->id)
            ->get();

        $settingFlowId = Setting::query()
            ->where('key', 'elective_subjects_learn_year_id')
            ->first()->value ?? Setting::query()
            ->where('key', 'current_semester')
            ->first()?->value
        ;

        $curriculums = Curriculum::query()
            ->with(['lecture.times.lecturer','lecture.times.auditorium.campus', 'syllabus' => function($syllabus){
                $syllabus->select(['id', 'name', 'name_en', 'credits']);
            }])
            ->where('flow_id', $settingFlowId)
            ->whereRaw('registration_end_date >?', [now()])
            ->whereJsonContains('student_flow_ids', (string) $studentFlowId)
            ->get();
//            ->filter(function ($curriculum) use ($student) {
//                return ($curriculum->syllabus->prerequisites->isEmpty() ||
//                        $curriculum->syllabus->prerequisites->filter(function ($prerequisite) use ($student) {
//                            return !$student->syllabusHistory->contains('syllabus_id', $prerequisite->prerequisite_id);
//                        })->isEmpty()
//                    ) && $curriculum->allowed_amount_of_students > $curriculum->syllabus->students->count();
//            })

        $historyForAllowedStudents = StudentSyllabusHistory::query()
            ->whereIn('syllabus_id', $curriculums->pluck('syllabus_id'))
            ->get();
//return [];
        return $curriculums->map(function ($curriculum) use ($syllabusHistories, $historyForAllowedStudents){
                return array_merge($curriculum->toArray(), [
                    'allowed_of_students' => $historyForAllowedStudents->where('syllabus_id', $curriculum->syllabus_id)->count(),
                    'is_elected' => (int) $syllabusHistories->contains('syllabus_id', $curriculum->syllabus_id)
                ]);
            })
        ;
    }

    /**
     * @throws \Throwable
     */
    public function register(Request $request)
    {
        $redis = new Client;

        $request = $request->validate([
            'syllabus_id' => ['required','exists:syllabi,id'],
        ]);

        $authUserId = auth()->id();
        $redisKey = 'student-'.$authUserId.'-register-syllabus-'.$request['syllabus_id'];


        if ($redis->exists($redisKey)) {
            if ($redis->get($redisKey) == 'registered')
            {
                return response()->json([
                    'status' => 200,
                    'success' => false,
                    'message' => 'სტუდენტი საგანზე რეგისტრირებულია! - The student is already registered for the subject!'
                ]);
            }
        }else{
            $redis->set($redisKey, 'registered');
        }


        $student = Student::query()
            ->where('user_id', auth()->id())
            ->first();


        if (StudentSyllabusHistory::query()
            ->where('syllabus_id', $request['syllabus_id'])
            ->where('student_id', $student->id)
            ->exists())
        {
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'სტუდენტი საგანზე რეგისტრირებულია! - The student is already registered for the subject!'
            ]);
        }

        $curriculum = Curriculum::query()
            ->where('syllabus_id', $request['syllabus_id'])
            ->first()
        ;

        if ($curriculum->registration_end_date < now()) {
            $redis->del($redisKey);
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'საგანზე რეგისტრაციის დრო დასრულდა! - The registration time for the subject is over!'
            ]);
        }


        if ($curriculum->registration_start_date > now()) {
            $redis->del($redisKey);
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'საგანზე რეგისტრაცია გაიხსნება ' . $curriculum->registration_start_date
            ]);
        }

        $registeredStudentCount = StudentSyllabusHistory::query()
            ->where('syllabus_id', $request['syllabus_id'])
            ->count()
        ;

        if ((int)$registeredStudentCount >= (int)$curriculum->allowed_amount_of_students)
        {
            $redis->del($redisKey);
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'საგანზე დასაშვები სტუდენტების რაოდენობა შევსებულია! - The number of students admitted to the subject has been filled!'
            ]);
        }else{
            $lectures = Lecture::query()
                ->where('syllabus_id', $request['syllabus_id'])
                ->get()
            ;

            DB::beginTransaction();
            try {
                $lectureStudentInsertData = [];
                foreach ($lectures as $lecture)
                {
                    $lectureStudentInsertData[] = [
                        'lecture_id' => $lecture->id,
                        'student_id' => $student->id,
                    ];
                }

                LectureStudent::query()->insert($lectureStudentInsertData);

                $studentSyllabusHistory = StudentSyllabusHistory::query()
                    ->create([
                        'syllabus_id' => $request['syllabus_id'],
                        'student_id' => $student->id,
                    ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                \Log::alert('user-syllabus-registration', [$e->getMessage()]);
                $redis->del($redisKey);
                return response()->json([
                    'status' => 500,
                    'success' => false,
                    'message' => 'Internal Server Error'
                ]);
            }

            return response()->json([
                'status' => 200,
                'success' => true,
                'message' => 'გილოცავთ! საგანზე რეგისტრაცია წარმატებით შესრულდა! - Congratulations! Registration for the subject has been completed successfully!',
                'data' => $studentSyllabusHistory
            ]);
        }
    }

    public function deleteRegistration(Request $request)
    {
        $request = $request->validate([ // gasatania validation rules
            'syllabus_id' => ['required','exists:syllabi,id'],
        ]);

        $redis = new Client;
        $authUserId = auth()->id();
        $redisKey = 'student-'.$authUserId.'-register-syllabus-'.$request['syllabus_id'];
        if ($redis->exists($redisKey))
        {
            $redis->del($redisKey);
        }


        $curriculum = Curriculum::query() // gasatania custom ruleshi
            ->where('syllabus_id', $request['syllabus_id'])
            ->first()
        ;
        if ($curriculum->registration_end_date < now()) {
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'საგანზე რეგისტრაციის დრო დასრულდა! - The registration time for the subject is over!'
            ]);
        }


        if ($curriculum->registration_start_date > now()) {
            return response()->json([
                'status' => 200,
                'success' => false,
                'message' => 'საგანზე რეგისტრაცია გაიხსნება ' . $curriculum->registration_start_date
            ]);
        }

        $student = Student::query()
            ->where('user_id', auth()->id())
            ->first();

        $lectures = Lecture::query()
            ->where('syllabus_id', $request['syllabus_id'])
            ->get()
        ;

        LectureStudent::query()
            ->whereIn('lecture_id', $lectures->pluck('id'))
            ->where('student_id', $student->id)
            ->delete()
        ;

        StudentSyllabusHistory::query()
            ->where('student_id', $student->id)
            ->where('syllabus_id', $request['syllabus_id'])
            ->delete()
        ;

        return response()->json([
            'status' => 200,
            'success' => true,
            'message' => 'საგანი ამოიშალა! - The subject has been removed!'
        ]);
    }

    public function deleteLecturerSyllabus(Request $request)
    {
        $lectures = Lecture::query()
            ->whereIn('syllabus_id', explode(',', $request['syllabus_ids'] ?? ''))
            ->get()
        ;

        $deleted = LectureStudent::query()
            ->whereIn('lecture_id', $lectures->pluck('id'))
            ->delete()
        ;
        return response()->json(['status' => 200, 'success' => true, 'is_deleted' => $deleted]);
    }

}
