<?php

namespace App\Http\Controllers\API\V1\Messages;

use App\Http\Controllers\Controller;
use App\Http\Requests\Message\ChangeStatusRequest;
use App\Http\Requests\Message\FilterByProgramRequest;
use App\Http\Requests\Message\FilterBySchoolRequest;
use App\Http\Requests\Message\SearchReceiverRequest;
use App\Http\Requests\Message\SendMessageRequest;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Curriculum\CurriculumStudentGroup;
use App\Models\Message\Message;
use App\Models\Message\MessageAddress;
use App\Models\Message\MessageStatus;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentGroupHistory;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\SyllabusStudentGuest;
use App\Services\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    public function __construct(public MessageService $service)
    {
        //
    }

    public function messageCount()
    {
        return response($this->service->allTypeMessageCount());
    }

    public function unreadCount(): JsonResponse
    {
        return response()->json([
            'count' => Message::with([
                'author',
                'addresses',
                'attachments'
            ])
                ->whereHas(
                    'addresses',
                    fn($query) => $query->active()
                        ->where('user_id', Auth::id())
                )
                ->whereHas('addresses', function ($query) {
                    $query->whereNull('deleted_at')->whereNull('viewed_at');
                })
                ->count()
        ]);
    }

    public function incomingMessages(): Response
    {
        $messages = Message::with([
            'author',
            'addresses.user',
            'attachments',
            'replies.attachments' => fn($query) => $query->orderByDesc('id')->skip((request()->page - 1) * 12)
                ->take(12),
        ])
            ->whereHas(
                'addresses',
                fn($query) => $query->active()
                    ->where('user_id', Auth::id())
            )
            ->whereNull('main_message_id')
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->orderByDesc('updated_at')
            ->paginate(10)
        ;

        $pluckedViewedAt = $messages->map(function ($message){
            return [
                'message_id' => $message->id,
                'viewed_any' => $message->addresses->whereNull('viewed_at')->pluck('viewed_at')->count() > 0
            ];
        });

        $totalMessages = $messages->total();

        return response([
            'messages' => $messages,
            'total_messages' => $totalMessages,
            'viewed' => $pluckedViewedAt
        ]);
    }

    public function favoriteMessages(): Response
    {
        $messages = Message::with([
            'author',
            'addresses.user',
            'attachments',
            'replies.attachments' => fn($query) => $query->orderByDesc('id')->skip((request()->page - 1) * 12)
                ->take(12)
        ])
            ->whereNull('main_message_id')
            ->where(function ($query) {
                return $query->where(function ($query) {
                    return $query->favorite()
                        ->where('author_id', Auth::id());
                })->orWhereHas('addresses', function ($query) {
                    return $query->favorite()
                        ->where('user_id', Auth::id());
                });
            })
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->orderByDesc('updated_at')
            ->paginate(10);

        $totalMessages = $messages->total();

        return response([
            'messages' => $messages,
            'total_messages' => $totalMessages,
        ]);
    }

    public function deletedMessages(): Response
    {
        $messages = Message::with([
            'author',
            'addresses.user',
            'attachments',
            'replies.attachments' => fn($query) => $query->orderByDesc('id')->skip((request()->page - 1) * 12)
                ->take(12)
        ])
            ->whereNull('main_message_id')
            ->where(function ($query) {
                return $query->where(function ($query) {
                    return $query->trash()
                        ->where('author_id', Auth::id());
                })->orWhereHas('addresses', function ($query) {
                    return $query->trash()
                        ->where('user_id', Auth::id());
                });
            })
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->orderByDesc('updated_at')
            ->paginate(10);

        $totalMessages = $messages->total();

        return response([
            'messages' => $messages,
            'total_messages' => $totalMessages,
        ]);
    }


    public function sentMessages(): Response
    {
        $messages = Message::active()->with([
            'author',
            'addresses.user',
            'attachments',
            'replies.attachments' => fn($query) => $query->orderByDesc('id')->skip((request()->page - 1) * 12)
                ->take(12)
        ])->whereNull('main_message_id')
            ->where('author_id', Auth::id())
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->orderByDesc('updated_at')
            ->paginate(10);

        $totalMessages = $messages->total();

        return response([
            'messages' => $messages,
            'total_messages' => $totalMessages,
        ]);
    }

    public function sendMessage(
        SendMessageRequest $request,
        MessageService     $messageService): Response
    {
        $validated = $request->validated();
        $validated['author_id'] = Auth::id();
        $message = Message::create($validated);
        $messageId = $message->id;
        if ($request->hasAny([
            'schools',
            'student_groups',
            'programs',
            'users',
            'group_ids',
            'syllabus_ids',
            'flows'
        ])) {
            $userIds = $messageService->getUserIds(
                $request->schools,
                $request->student_groups,
                $request->programs,
                $request->flows,
                $request->users,
                groupIds: $request->group_ids,
                syllabusIds: $request->syllabus_ids,
                isBoth: $request->is_both
            );
            $addressesData = $messageService->mapMessageToUsers($userIds, $messageId);
            $message->addresses()->createMany($addressesData);
        }

        if (is_array($request->attachments)) {
            $messageService->createAttachments($request->attachments, $messageId);
        }

        if ($request->has('main_message_id')) {
            $replyMessage = Message::find($request->main_message_id);

            $replyMessage->update(['updated_at' => now()]);

            $message->update(['title' => $replyMessage->title]);

            MessageAddress::query()->create([
                'user_id' => $replyMessage->author_id,
                'message_id' => $replyMessage->id
            ]);
        }

        return response([
            'message' => $message->load([
                'author',
                'addresses',
                'attachments'
            ])
        ]);
    }

    public function getMessages(Message $message): Response
    {
        $addressQuery = $message->addresses()->where('user_id', Auth::id())->whereNull('viewed_at');

        if ($addressQuery->exists()) {
            $addressQuery->update(['viewed_at' => now()]);
        }
        return response([
            'messages' => $message->load([
                'attachments',
                'replies.attachments' => fn($query) => $query->orderByDesc('id')->skip((request()->page - 1) * 12)
                    ->take(12),
                'author',
                'addresses.user'
            ])
        ]);
    }

    public function changeMessageStatus(
        ChangeStatusRequest $request): Response
    {
        foreach ($request->messages as $message) {
            $message = Message::find($message);
            if ($message->author_id == Auth::id()) {
                $message->update($request->only(['message_status_id']));
            } else {
                $message->addresses()->where('user_id', '=', Auth::id())
                    ->update($request->only(['message_status_id']));
            }
        }

        return response('Status updated!');
    }

    public function search(
        SearchReceiverRequest $request,
        MessageService        $messageService): Response
    {
        return response($messageService->searchPeople($request->keyword));
    }

    public function filterBySchools(
        FilterBySchoolRequest $request,
        MessageService        $messageService): Response
    {
        return response($messageService->filterBySchools($request->schools));
    }

    public function filterByPrograms(
        FilterByProgramRequest $request,
        MessageService         $messageService): Response
    {
        return response($messageService->filterByPrograms($request->programs));
    }

    public function deleteMessage($message_id): Response
    {
        //$messages = json_decode($message_id);
        MessageAddress::where('message_id', $message_id)->where('user_id', Auth::id())->delete();
//        MessageAddress::where('message_id', $message_id)
//            ->where('user_id', Auth::id())
//            ->delete();
//        Message::whereIn('id', $messages)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function emptyTrash(Request $request)
    {
        $request->validate([
            'messages.*' => 'required|int'
        ]);

        $messages = array_values(array_unique($request->input('messages', [])));

        MessageAddress::whereIn('message_id', $messages)->where('user_id', Auth::id())->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function lectureGroups(): array
    {
        $lecturer = Lecturer::query()
            ->whereUserId(auth()->id())
            ->first();

        if (!$lecturer) {
            return [
                'code' => 404,
                'message' => 'Lecturer not found'
            ];
        }

        $curriculumLectureTimeIds = CurriculumLectureTime::query()
            ->with(['curriculumLecture.curriculum.syllabus', 'studentGroups'])
            ->where('lecturer_id', $lecturer->id)
            ->get()
            ->groupBy('curriculum_lecture_id');

        $response = [];
        foreach ($curriculumLectureTimeIds as $items) {
            $firstItem = $items->first();
            if ($firstItem->studentGroups->isEmpty()) {
                $response[] = [
                    'group_id' => null,
                    'syllabus_id' => $firstItem->curriculumLecture->curriculum->syllabus->id ?? null,
                    'syllabus_name' => $firstItem->curriculumLecture->curriculum->syllabus->name ?? null,
                    'syllabus_name_en' => $firstItem->curriculumLecture->curriculum->syllabus->name_en ?? null,
                ];
            } else {
                foreach ($firstItem->studentGroups as $group) {
                    $response[] = [
                        'group_id' => $group->id,
                        'name_en' => $group->name_en,
                        'name_ka' => $group->name_ka,
                        'syllabus_id' => $firstItem->curriculumLecture->curriculum->syllabus->id ?? null,
                        'syllabus_name' => $firstItem->curriculumLecture->curriculum->syllabus->name ?? null,
                        'syllabus_name_en' => $firstItem->curriculumLecture->curriculum->syllabus->name_en ?? null,
                    ];
                }
            }
        }

        return $response;
    }

    public function messageAddresses(Message $id)
    {
        return $id->addresses()->get();
    }

}
