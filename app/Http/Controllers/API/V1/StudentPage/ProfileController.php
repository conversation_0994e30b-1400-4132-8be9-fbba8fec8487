<?php

namespace App\Http\Controllers\API\V1\StudentPage;

use App\Http\Controllers\Controller;
use App\Models\Curriculum\Curriculum;
use App\Models\Reestry\Student\Student;
use App\Models\Setting;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use OpenApi\Attributes\Response;
use Validator;

class ProfileController extends Controller
{
    public function index()
    {
        $data = Student::where('user_id',Auth::id())->firstOrFail();
        $mappedData = [
            'name' => $data->name,
            'name_en' => $data->name_en,
            'surname' => $data->surname,
            'surname_en' => $data->surname_en,
            'email' => $data->email,
            'phone' => $data->phone,
            'birthday' => Carbon::parse($data->birthday)->format('d F Y'),
            'address' => $data->address,
            'photo' => $data->photo,
        ];
        return response($mappedData);
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name_en' => 'required|max:255',
            'surname_en' => 'required|max:255',
            'phone' => 'required|max:255',
            'address' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = Student::where('user_id', Auth::id())->firstOrFail();
        $user->name_en = $request->input('name_en');
        $user->surname_en = $request->input('surname_en');
        $user->phone = $request->input('phone');
        $user->address = $request->input('address');
        $user->save();

        return response($user);
    }

}
