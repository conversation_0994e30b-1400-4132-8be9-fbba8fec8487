<?php

namespace App\Http\Controllers;

use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{

    public function inbox(int $userId)
    {

    }

    public function seen(int $notificationId): JsonResponse
    {
        return response()->json(
            (new NotificationService())->markNotificationsAsSeen($notificationId)
        );
    }

    public function getNotifications()
    {
        return response()->json(
            (new NotificationService())->getNotifications()
        );
    }

    public function getNotificationsHeader()
    {
        return response()->json(
            (new NotificationService())->getNotificationsHeader()
        );
    }
}
