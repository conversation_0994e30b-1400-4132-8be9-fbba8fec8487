<?php

namespace App\Http\Controllers;

use Excel;
use Illuminate\Http\Request;

class LMBOldDbController extends Controller
{
    public function syllabiImport(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx',
            'learn_year_id' => 'required',
            'academic_degree_id' => 'required',
        ]);
        Excel::import(
            new \App\Imports\OldDatabase\SyllabiImport(
                $request->post('learn_year_id'),
                $request->post('academic_degree_id'),
            ),
            $request->file('file')
        );
        return response()->json([
            'status' => 200,
            'success' => true
        ]);
    }

    public function assignmentImport(Request $request)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 3600);

        $request->validate([
            'file' => 'required|mimes:xlsx',
        ]);
        Excel::import(
            new \App\Imports\OldDatabase\AssignmentImport,
            $request->file('file')
        );

        return response()->json([
            'status' => 200,
            'success' => true
        ]);
    }

}
