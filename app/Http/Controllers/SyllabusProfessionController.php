<?php

namespace App\Http\Controllers;

use App\Filters\Syllabus\SyllabusFilter;
use App\Http\Requests\Syllabus\StoreSyllabusProfessionRequest;
use App\Http\Requests\Syllabus\UpdateSyllabusProfessionRequest;
use App\Models\Assignment;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Syllabus\LecturerContactTime;
use App\Models\Syllabus\Syllabus;
use App\Models\Syllabus\SyllabusProfession;
use App\Services\Syllabus\SyllabusService;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SyllabusProfessionController extends Controller
{

    public function index(SyllabusFilter $filter): Response
    {
        return response([
            'syllabi' => Syllabus::filter($filter)->with([
                'learnYear:id,name',
                'lecturers',
                'assignments',
                'academicDegree',
                'curriculum.lecture:id,curriculum_id',
                'status'
            ])->whereIsProfession(1)->orderBy('status_id')->orderBy('semester_id')->get(),
        ]);
    }

    public function store(StoreSyllabusProfessionRequest $request, SyllabusService $service): Response
    {
        $data = $request->validated();
        $data['status_id'] = 1;
        $data['seminar_hours'] = 0;
        $data['mid_and_final_exam_hours'] = 0;
        $data['syllabus_type_id'] = 2;
        //$data['independent_work_hours'] = 0;

        $syllabus = Syllabus::create($data);
        $syllabus->lecturers()->attach(array_column($request->lecturers, 'lecturer_id'));
        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }
        $parents = [];
        foreach ($request->exams as $exam) {
            if (isset($exam['parent_id']) && $exam['parent_id'] !== '' && array_key_exists($exam['parent_id'], $parents)) {
                $service->StoreExam($syllabus->id, $exam);
            } else {
                $assignment = $service->StoreExam($syllabus->id, $exam);
                $parents[$exam['id']] = $assignment->id;
            }
        }
        return response($syllabus->load(['learnYear.program:id,school_id']), 201);
    }

    public
    function edit(int $id): JsonResponse
    {
        $syllabus = Syllabus::with([
            'learnYear',
            'lecturerContactTimes' => function ($query) use ($id) {
                $query->where('syllabus_id', $id)->with('lecturer');
            },
            'assignments.assessmentComponent',
            'academicDegree',
        ])->findOrFail($id);
        $program_id = LearnYear::where('id', $syllabus->learn_year_id)->pluck('program_id')->first();
        $syllabus->setAttribute('program_id', $program_id);
        $data = [
            'syllabus' => $syllabus,
            'academic_degrees_list' => AcademicDegree::pluck('name_ka', 'id'),
        ];
        return response()->json($data);
    }

    public function update(UpdateSyllabusProfessionRequest $request, int $syllabusProfessionId, SyllabusService $service)
    {
        $syllabus = Syllabus::findOrFail($syllabusProfessionId);
        $syllabusRequest = $request->toArray();
        unset($syllabusRequest['lmb_id']);
        $syllabus->update($syllabusRequest);
        $syllabus->lecturers()->sync(array_column($request->lecturers, 'lecturer_id'));
        $service->deleteLecturerContactHours($syllabus->id);
        foreach ($request->lecturers as $lecturer) {
            $service->StoreLecturerContactHours($syllabus->id, $lecturer);
        }
        Assignment::where('syllabus_id', $syllabus->id)->delete();
        foreach ($request->exams as $exam) {
            $service->StoreExam($syllabus->id, $exam);
        }
        return response($syllabus->load(['learnYear.program:id,school_id']), 201);
    }

    public function delete(int $syllabusProfessionId): Response
    {
        $syllabus = Syllabus::findOrFail($syllabusProfessionId);
        LecturerContactTime::where('syllabus_id', $syllabusProfessionId)->delete();
        DB::table('lecturer_syllabus')->where('syllabus_id', $syllabusProfessionId)->delete();
        $syllabus->delete();
        return response(null, 204);
    }

}
