<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuthSuperAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $forbiddenCode = Response::HTTP_FORBIDDEN;
        if (!auth()->check()) {
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }

        if (!Auth::user()->is_super_admin) {
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }

        return $next($request);
    }
}
