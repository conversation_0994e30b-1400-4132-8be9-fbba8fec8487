<?php

namespace App\Http\Middleware;

use App\Models\User\UserType;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuthStudent
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $forbiddenCode = Response::HTTP_FORBIDDEN;
        if(!auth()->check()){
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }
        if (auth()->user()->user_type_id !== UserType::STUDENT) {
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }
//        if (now()->subHours(24) > auth()->user()->token()->created_at)
//        {
//            auth()->user()->token()->delete();
//            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
//        }
        return $next($request);
    }
}
