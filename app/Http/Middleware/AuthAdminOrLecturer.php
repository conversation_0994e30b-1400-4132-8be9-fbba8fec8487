<?php

namespace App\Http\Middleware;

use App\Models\User\UserType;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuthAdminOrLecturer
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $forbiddenCode = Response::HTTP_FORBIDDEN;
        if (!auth()->check()) {
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }
        $userType = auth()->user()->user_type_id;
        if ($userType !== UserType::ADMINISTRATION && $userType !== UserType::LECTURER) {
            return response(Response::$statusTexts[$forbiddenCode], $forbiddenCode);
        }
        return $next($request);
    }
}
