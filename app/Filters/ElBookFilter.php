<?php


namespace App\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ElBookFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }

    public function keyword($keyword)
    {
        $this->builder
            ->where('title', 'like', $keyword . '%')
            ->orWhere('author', 'like', '%' . $keyword . '%')
            ->orWhere('subject', 'like', $keyword . '%')
            ->orWhere('topic', 'like', $keyword . '%')
            ->orWhereYear('published_date', '=', $keyword)
            ->orWhereHas('lecturer', function ($query) use ($keyword) {
                return $query->where('first_name', 'like', $keyword . '%')
                    ->orWhere('last_name', 'like', $keyword . '%');
            });
    }

    public function year($year)
    {
        return $this->builder->whereYear('published_date', '=', $year);
    }

    public function topics($topic_id)
    {
        return $this->builder->whereHas('topics', function ($query) use ($topic_id) {
            return $query->where('topic_id', $topic_id);
        });
    }

    public function lecturers($lecturer)
    {
        return $this->builder->where('lecturer_id', $lecturer);
    }

}
