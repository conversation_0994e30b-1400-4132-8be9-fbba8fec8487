<?php

namespace App\Filters;

use Illuminate\Http\Request;

class eDocStudentFilter extends QueryFilters
{

    protected ?Request $request;

    public function __construct(?Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }
    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            $query->where('text', 'LIKE', '%' . $keyword . '%');
        })
            ->orWhereHas('template', function ($query) use ($keyword) {
                $query->where('name', 'LIKE', '%' . $keyword . '%');
            });
    }
}
