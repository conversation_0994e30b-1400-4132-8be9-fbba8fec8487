<?php


namespace App\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class DirectionFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_ka', 'LIKE', '%' . $keyword . '%');
        });
    }

}
