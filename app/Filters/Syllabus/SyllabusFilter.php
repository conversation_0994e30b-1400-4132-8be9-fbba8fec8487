<?php

namespace App\Filters\Syllabus;

use App\Filters\LearnYearFilter;
use App\Filters\ProgramFilter;
use App\Filters\QueryFilters;
use App\Filters\SchoolFilter;
use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class SyllabusFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['Syllabus']);
    }

    public function learn_year_id($learnYearId)
    {
        return $this->builder->whereHas('learnYear', function ($query) use ($learnYearId) {
            return $query->filter(new LearnYearFilter())->where('id', $learnYearId);
        });
    }

    public function learnYear($learnYearId)
    {
        return $this->builder->whereHas('learnYear', function ($query) use ($learnYearId) {
            return $query->filter(new LearnYearFilter())->where('id', $learnYearId);
        });
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            $query->where('name', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('id', 'LIKE', '%' . $keyword . '%')
                ->orWhere('code', 'LIKE', '%' . $keyword . '%')
                ->orWhereHas('lecturers', function ($lecturerQuery) use ($keyword) {
                    $lecturerQuery->where(function ($query) use ($keyword) {
                        $query
                            ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                            ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
                    });
                });
        });
    }

    public function school($school)
    {
        return $this->builder->whereHas('learnYear.program.school', function ($query) use ($school) {
            return $query->filter(new SchoolFilter())->where('id', $school);
        });
    }

    public function program($program)
    {
        return $this->builder->whereHas('learnYear.program', function ($query) use ($program) {
            return $query->filter(new ProgramFilter())->where('id', $program);
        });
    }

    public function flow_id($flow_id)
    {
        return $this->builder->whereHas('curriculum', function ($query) use ($flow_id) {
            return $query->where('flow_id', $flow_id);
        });
    }
}
