<?php

namespace App\Filters\Doc;

use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminInboxFilter extends QueryFilters
{
    protected ?Request $request;

    public function keyword($keyword)
    {
        return $this->builder->whereHas('user.student', function ($query) use ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where(DB::raw("CONCAT(name_en, ' ', surname_en)"), 'LIKE', '%' . $keyword . '%')
                    ->orWhere(DB::raw("CONCAT(name, ' ', surname)"), 'LIKE', '%' . $keyword . '%')
                    ->orWhere(DB::raw("CONCAT(surname_en, ' ', name_en)"), 'LIKE', '%' . $keyword . '%')
                    ->orWhere(DB::raw("CONCAT(surname, ' ', name)"), 'LIKE', '%' . $keyword . '%');
            });
        });
    }

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Auditorium']);
    }

    public function school_id($school_id)
    {
        return $this->builder->whereHas('user.student.learnYear.program.school', function ($school) use($school_id){
            return $school->where('id', $school_id);
        });
    }

    public function program_id($program_id)
    {
        return $this->builder->whereHas('user.student.learnYear.program', function ($school) use($program_id){
            return $school->where('id', $program_id);
        });
    }

}
