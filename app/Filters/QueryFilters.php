<?php

namespace App\Filters;

use App\Models\Own;
use App\Models\User\UserFullAccess;
use App\Models\User\UserType;
use App\Models\UserProgram;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QueryFilters
{
    protected ?Request $request;
    protected ?int $model;
    protected $builder;

    public function __construct(Request $request = null, $model = null)
    {
        $this->request = $request;
        $this->model = $model;
    }

    public function __destruct()
    {
        if (request()->all != '1')
        {
            if (auth()->check()) {
                $fullAccess = UserFullAccess::where('user_id', auth()->id())
                    ->where('model_id', $this->model)->exists();
                if (
                    in_array(Auth::user()->user_type_id, [UserType::STUDENT, UserType::LECTURER])
                    ||
                    auth()->user()->is_super_admin
                    ||
                    $fullAccess
                ) {
                    return;
                }

                $programIds = UserProgram::where('user_id', Auth::id())->pluck('program_id');
                $this->builder = $this->builder?->when(in_array($this->model, [
                    Own::MODELS['Curriculum'],
                    Own::MODELS['Student'],
                    Own::MODELS['Flow'],
                    Own::MODELS['ApplicantsRegister']
                ]), function (Builder $query) use ($programIds) {
                    return $query->whereIn('program_id', $programIds);
                })
                    ->when($this->model === Own::MODELS['Syllabus'], function (Builder $query) use ($programIds) {
                        return $query->whereHas('learnYear', function ($newQuery) use ($programIds) {
                            $newQuery->whereIn('program_id', $programIds);
                        });
                    })
                    ->when($this->model === Own::MODELS['Administration'], function (Builder $query) use ($programIds) {
                        return $query->whereNull('school_id')
                            ->orWhereHas('school.programs', function ($innerQuery) use ($programIds) {
                                return $innerQuery->whereIn('id', $programIds);
                            });
                    })
                    ->when($this->model === Own::MODELS['Program'], function (Builder $query) use ($programIds) {
                        return $query->whereIn('id', $programIds);
                    })
                    ->when(in_array($this->model, [Own::MODELS['LearnYear'], Own::MODELS['StudentGroup']]), function (Builder $query) use ($programIds) {
                        return $query->whereIn('program_id', $programIds);
                    })
                    ->when($this->model === Own::MODELS['School'], function (Builder $query) {
                        return $query->whereHas('programs', function ($innerQuery) {
                            return $innerQuery->whereIn('id', Auth::user()->programs()->pluck('program_id'));
                        });
                    });
            }
        }
    }

    public function apply(Builder $builder)
    {
        $this->builder = $builder;
        if (method_exists($this, '__destruct') && !is_null($this->model)) {
            $this->__destruct();
        }
        if (is_array($this->filters())) {
            foreach ($this->filters() as $name => $value) {
                if (!method_exists($this, $name)) {
                    continue;
                }
                $value == null ?: $this->$name($value);
            }
        }
        return $this->builder;
    }

    public function filters()
    {
        return $this->request?->all();
    }
}
