<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ProgramFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(?Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['Program']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_ka', 'LIKE', '%' . $keyword . '%');
        });
    }

    public function school($school_id)
    {
        return $this->builder->whereHas('school', function ($query) use ($school_id) {
            return $query->filter(new SchoolFilter())->where('id', $school_id);
        });
    }

    public function school_id($school_id)
    {
        return $this->builder->whereHas('school', function ($query) use ($school_id) {
            return $query->filter(new SchoolFilter())->where('id', $school_id);
        });
    }

    public function program_id($program_id)
    {
        return $this->builder->find($program_id);
    }

    public function program($program_id)
    {
        return $this->builder->find($program_id);
    }

    public function academic_degree($academic_degree_id)
    {
        return $this->builder->where('academic_degree_id', $academic_degree_id);
    }

}
