<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use JetBrains\PhpStorm\Pure;

class LearnYearFilter extends QueryFilters
{
    protected ?Request $request;

    #[Pure] public function __construct(?Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['LearnYear']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where('name', 'LIKE', '%' . $keyword . '%');
    }

    public function program_id($program_id)
    {
        return $this->builder->where('program_id', $program_id);
    }

//    public function school_id($school_id)
//    {
//        return (new FlowFilter($this->request))->school_id($school_id);
//    }

    public function program($program_id)
    {
        return $this->builder->where('program_id', $program_id);
    }

    public function school($school_id)
    {
        return $this->builder
            ->whereHas('program.school', fn($query) => $query->where('id', $school_id));
    }

}
