<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class LecturerFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Lecturer']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('first_name', 'LIKE', '%' . $keyword . '%')
                ->orWhere('last_name', 'LIKE', '%' . $keyword . '%')->orWhere(function ($query) use ($keyword) {
                    return is_integer($keyword) ? $query->where('identity_number', 'LIKE', $keyword . '%') : $query;
                })->orWhere('phone', 'LIKE', '%' . $keyword . '%')->orWhere('email', 'LIKE', '%' . $keyword . '%')
                ->orWhere('address', 'LIKE', $keyword . '%')->orWhere('card_number', 'LIKE', '%' . $keyword);
        });
    }

    public function academic_degree($campus_id)
    {
        return $this->builder->where('academic_degree_id', $campus_id);
    }

    public function type($type)
    {
        return $this->builder->where('type', $type);
    }

    public function affiliated($affiliated)
    {
        return $this->builder->where('affiliated', $affiliated);
    }

    public function directions($direction)
    {
        return $this->builder->whereHas('directions',function($query) use($direction){
            return $query->where('direction_id',$direction);
        });
    }

}
