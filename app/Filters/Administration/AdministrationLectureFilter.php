<?php


namespace App\Filters\Administration;

use App\Filters\LearnYearFilter;
use App\Filters\ProgramFilter;
use App\Filters\QueryFilters;
use App\Filters\SchoolFilter;
use App\Filters\Student\StudentFilter;
use App\Filters\Syllabus\SyllabusFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class AdministrationLectureFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }

    public function keyword($keyword): Builder
    {
        return $this->builder->where(function ($query) use ($keyword) {
            $query->whereHas('lecturer', function ($query) use ($keyword) {
                $search = explode(' ', $keyword);

                $query->where('first_name', 'like', '%' . $search[0] . '%')
                    ->orWhere('last_name', 'like', '%' . $search[0] . '%');

                if (isset($search[1])) {
                    $query->orWhere('first_name', 'like', '%' . $search[1] . '%')
                        ->orWhere('last_name', 'like', '%' . $search[1] . '%');
                }
            });
        });
    }

    public function school($school_id): Builder
    {
        return $this->builder->whereHas('syllabus', function ($query) use ($school_id) {
            $query->filter(new SyllabusFilter())->whereHas('learnYear', function ($query) use ($school_id) {
                $query->filter(new LearnYearFilter())->whereHas('program', function ($query) use ($school_id) {
                    $query->filter(new ProgramFilter())->where('school_id', $school_id);
                });
            });
        });
    }

    public function program($program_id): Builder
    {
        return $this->builder->whereHas('syllabus', function ($query) use ($program_id) {
            $query->filter(new SyllabusFilter())->whereHas('learnYear', function ($query) use ($program_id) {
                $query->filter(new LearnYearFilter())->where('program_id', $program_id);
            });
        });
    }

    public function campus($campus_id): Builder
    {
        return $this->builder->whereHas('auditorium', function ($query) use ($campus_id) {
            $query->where('campus_id', $campus_id);
        });
    }

    public function auditorium($auditorium_id): Builder
    {
        return $this->builder->where('auditorium_id', $auditorium_id);
    }

    public function lecturer($lecturer_id): Builder
    {
        return $this->builder->where('lecturer_id', $lecturer_id);
    }

    public function student($student_id): Builder
    {
        return $this->builder->whereHas('syllabus.students', function ($query) use ($student_id) {
            $query->where('student_id', $student_id);
           // $query->filter(new StudentFilter())->where('student_id', $student_id);
        });
    }
}
