<?php


namespace App\Filters\Administration;

use App\Filters\QueryFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use JetBrains\PhpStorm\Pure;

class AdministrationItemFilter extends QueryFilters
{
    protected ?Request $request;

    #[Pure] public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_ka', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_en', 'LIKE', '%' . $keyword . '%');
        });
    }

}
