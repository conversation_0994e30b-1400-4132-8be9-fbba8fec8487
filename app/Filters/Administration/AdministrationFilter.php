<?php


namespace App\Filters\Administration;

use App\Filters\QueryFilters;
use App\Filters\SchoolFilter;
use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class AdministrationFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['Administration']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('first_name', 'LIKE', '%' . $keyword . '%')
                ->orWhere('last_name', 'LIKE', '%' . $keyword . '%')->orWhere(function ($query) use ($keyword) {
                    $query->where('identity_number', 'LIKE', $keyword . '%');
                })->orWhere('phone', 'LIKE', $keyword . '%')->orWhere('email', 'LIKE', '%' . $keyword . '%');
        });
    }

    public function positions($administration_position_id)
    {
        return $this->builder->where('administration_position_id', $administration_position_id);
    }

    public function school($school_id)
    {
        return $this->builder->whereHas('school', function ($query) use ($school_id) {
            return $query->filter(new SchoolFilter())->where('id', $school_id);
        });
    }

    public function items($administration_item_id)
    {
        return $this->builder->where('administration_item_id', $administration_item_id);
    }

}
