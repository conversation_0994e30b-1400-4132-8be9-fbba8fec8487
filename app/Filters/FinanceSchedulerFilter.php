<?php

namespace App\Filters;

use App\Models\Own;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FinanceSchedulerFilter extends QueryFilters
{

    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['Student']);
    }
    public function keyword($keyword)
    {
        return $this->builder->whereHas('student', function ($query) use ($keyword) {
            $query
                ->where(function ($query) use ($keyword) {
                    $query
                        ->where(DB::raw("CONCAT(student.name, ' ', student.surname)"), 'LIKE', "%$keyword%")
                        ->orWhere(DB::raw("CONCAT(student.surname, ' ', student.name)"), 'LIKE', "%$keyword%");
                })
                ->orWhere('student.personal_id', 'LIKE', '%' . $keyword . '%')
                ->orWhere('student.phone', 'LIKE', '%' . $keyword . '%')
                ->orWhere('student.email', 'LIKE', '%' . $keyword . '%');
        });

    }

}
