<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class CampusFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Campus']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_ka', 'LIKE', '%' . $keyword . '%')
                ->orWhere('address_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('address_ka', 'LIKE', '%' . $keyword . '%');
        });
    }

}
