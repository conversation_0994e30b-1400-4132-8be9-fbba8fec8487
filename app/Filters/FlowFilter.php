<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use JetBrains\PhpStorm\Pure;

class FlowFilter extends QueryFilters
{
    protected ?Request $request;

    #[Pure] public function __construct(Request $request)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Flow']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where('name', 'LIKE', '%' . $keyword . '%');
    }

    public function program_id($program_id)
    {
        return $this->builder->where('program_id', $program_id);
    }

    public function school_id($school_id)
    {
        if($this->builder) {
            return $this->builder
                ->whereHas('program.school', fn($query) => $query->where('id', $school_id));
        }
    }

}
