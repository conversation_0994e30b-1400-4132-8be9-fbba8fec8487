<?php


namespace App\Filters\Student;

use App\Filters\ProgramFilter;
use App\Filters\QueryFilters;
use App\Filters\SchoolFilter;
use App\Models\Own;
use Illuminate\Http\Request;

class StudentRecordFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['School']);
    }

    public function school_id($school)
    {
        return $this->builder->find($school);
    }

    public function program_id($program)
    {
        return $this->builder->whereHas('programs', function ($query) use ($program) {
            $programFilter = new ProgramFilter();
            return $query->filter($programFilter)->where('id', $program);
        });
    }

    public function group_id($studentGroup)
    {
        $schoolFilter = new SchoolFilter();
        return $this->builder->filter($schoolFilter)->whereHas('programs.studentGroups', function ($query) use ($studentGroup) {
            $studentGroupFilter = new StudentGroupFilter();
            return $query->filter($studentGroupFilter)->where('id', $studentGroup);
        });
    }

}
