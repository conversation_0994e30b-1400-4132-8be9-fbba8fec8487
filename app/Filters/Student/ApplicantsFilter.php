<?php

namespace App\Filters\Student;

use App\Filters\LearnYearFilter;
use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Http\Request;

class ApplicantsFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['ApplicantsRegister']);
    }

    public function keyword($keyword)
    {
        return $this->builder->whereHas('registerFormInfo', function ($query) use ($keyword) {
            $query->where(function ($subquery) use ($keyword) {
                $subquery->where('first_name', 'like', '%' . $keyword . '%')
                    ->orWhere('last_name', 'like', '%' . $keyword . '%')
                    ->orWhere('identity_number', 'like', '%' . $keyword . '%')
                    ->orWhere('phone', 'like', '%' . $keyword . '%')
                    ->orWhere('email', 'like', '%' . $keyword . '%');
            });
        });
    }

    public function flow_id($flow_id)
    {
        return $this->builder->where('flow_id', $flow_id);
    }

    public function program_id($program_id)
    {
        return $this->builder->where('program_id', $program_id);
    }
}
