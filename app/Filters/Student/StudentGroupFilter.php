<?php


namespace App\Filters\Student;

use App\Filters\ProgramFilter;
use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class StudentGroupFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['StudentGroup']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_ka', 'LIKE', '%' . $keyword . '%');
        });
    }

    public function program($program)
    {
        return $this->builder->whereHas('program', function ($query) use ($program) {
            return $query->filter(new ProgramFilter())->where('id', $program);
        });
    }

    public function school_id($school_id)
    {
        return $this->builder->whereHas('program', function ($query) use ($school_id) {
            return $query->filter(new ProgramFilter())->where('school_id', $school_id);
        });
    }

    public function school($school_id)
    {
        return $this->builder->whereHas('program', function ($query) use ($school_id) {
            return $query->filter(new ProgramFilter())->where('school_id', $school_id);
        });
    }

    public function group($group_id)
    {
        return $this->builder->find($group_id);
    }

}
