<?php


namespace App\Filters\Student;

use App\Filters\LearnYearFilter;
use App\Filters\ProgramFilter;
use App\Filters\QueryFilters;
use App\Filters\SchoolFilter;
use App\Models\Own;
use App\Models\Reestry\School;
use Illuminate\Http\Request;

class StudentFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['Student']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query
                ->where(function ($query) use ($keyword) {
                    $query
                        ->whereRaw("CONCAT(name, ' ', surname) LIKE ?", ["%$keyword%"])
                        ->orWhereRaw("CONCAT(surname, ' ', name) LIKE ?", ["%$keyword%"]);
                })
//                ->where('name', 'LIKE', '%' . $keyword . '%')
//                ->orWhere('surname', 'LIKE', '%' . $keyword . '%')
                ->orWhere('personal_id', 'LIKE', '%' . $keyword)
                ->orWhere('phone', 'LIKE', '%' . $keyword)
                ->orWhere('email', 'LIKE', '%' . $keyword);
        });
    }

    public function school($school)
    {
        return $this->builder->whereHas('school', function ($query) use ($school) {
            return $query->filter(new SchoolFilter())->where('id', $school);
        });
    }

    public function program($program)
    {
        return $this->builder->whereHas('program', function ($query) use ($program) {
            return $query->filter(new ProgramFilter())->where('id', $program);
        });
    }

    public function group($studentGroup)
    {
        return $this->builder->whereHas('studentGroup', function ($query) use ($studentGroup) {
            return $query->filter(new StudentGroupFilter())->where('id', $studentGroup);
        });
    }

    public function sex($gender)
    {
        return $this->builder->where('sex', $gender);
    }

    public function citizenship($citizenship)
    {
        return $this->builder->where('citizenship', 'LIKE', $citizenship . '%');
    }

    public function age($age)
    {
        $date = now()->subYears($age)->format('Y-m-d');
        return $this->builder->whereDate('birthday', '=', $date);
    }

    public function mobility($mobility)
    {
        return $this->builder->where('mobility', $mobility);
    }

    public function basicOfEnrollments($basics_of_enrollment_id)
    {
        return $this->builder->where('basics_of_enrollement_id', $basics_of_enrollment_id);
    }

    public function status($status)
    {
        return $this->builder->where('status_id', $status);
    }

    public function learnYear($flow)
    {
        return $this->builder->whereHas('learnYear', function ($query) use ($flow) {
            return $query->filter(new LearnYearFilter())->where('id', $flow);
        });
    }

}
