<?php

namespace App\Filters;

use App\Models\Reestry\Student\Student;
use App\Models\UserProgram;
use App\Services\FinanceService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use JetBrains\PhpStorm\Pure;

class FinanceFilter extends QueryFilters
{
    protected ?Request $request;

    const PARAMETERS = [
        'piradi_nom',
        'program_id',
        'school_id',
        'learn_year',
        'trainings',
        'period'
    ];

    #[Pure] public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request);
    }

    public function __destruct()
    {
        return $this->builder
            ->whereIn('piradi_nom', $this->studentIdentityNumbers());
    }

    public function piradi_nom(string $piradi_nom): Builder
    {
        return $this->builder->where('piradi_nom', 'like', $piradi_nom . '%');
    }

    public function program_id(int $program_id): Builder
    {
        $students = Student::query()->where('program_id', $program_id)->pluck('personal_id');
        //$userProgramIds = UserProgram::whereUserId(auth()->id())->pluck('program_id')->toArray();
        return $this->builder->whereIn('piradi_nom', $students);
//        return $this->builder->where('programId', $program_id);
        //return $this->builder->whereNotIn('ProgramID', $userProgramIds)->where('ProgramID', $program_id);
    }

    public function learn_year(int $learn_year): Builder
    {
        return $this->builder->whereSaswavloWeli($learn_year);
    }

    public function trainings(bool $trainings): Builder
    {
        return $this->builder->where('IsTrainings', $trainings);
    }

    public function school_id($school_id)
    {
        return $this->builder->whereHas(
            'program.school',
            function ($query) use ($school_id) {
                return $query->filter(new SchoolFilter())->where('id', $school_id);
            }
        );
    }

    public function learn_year_id($learn_year_id)
    {
        return $this->builder->whereHas(
            'student.learnYear',
            function ($query) use ($learn_year_id) {
                return $query->where('id', $learn_year_id);
            }
        );
    }

    public function periods($periods)
    {
        $periods = explode(',', $periods);
        [$from, $to] = $periods;
        [$fromDay, $fromMonth, $fromYear] = explode('/', $from);
        [$toDay, $toMonth, $toYear] = explode('/', $to);
        $quarters = collect([]);

        if ($fromMonth < 9) {
            $fromYear--;
        }

        $quarterFrom = (new FinanceService())->getQuarter($fromYear, $fromMonth, $fromDay);
        $quarterTo = (new FinanceService())->getQuarter($toYear, $toMonth, $toDay);

        foreach (range($fromYear, $toYear) as $year) {
            if ($fromYear !== $toYear) {
                if ($fromYear == $year) {
                    $quarterTo = 4;

                    if ($fromYear + 1 == $toYear && intval($toMonth) < 9) {
                        $quarterTo = (new FinanceService())->getQuarter($year + 1, $toMonth, $toDay);
                    }

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                } else if (
                    $toYear == $year &&
                    Carbon::createFromDate($toYear, $toMonth, $toDay) > Carbon::createFromDate($toYear, '08', '31')
                ) {
                    $quarterFrom = 1;
                    $quarterTo = (new FinanceService())->getQuarter($year, $toMonth, $toDay);

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                } elseif ($toYear != $year) {
                    $quarterTo = 4;
                    $quarterFrom = 1;

                    if ($year + 1 == $toYear && intval($toMonth) < 9) {
                        $quarterTo = (new FinanceService())->getQuarter($year + 1, $toMonth, $toDay);
                    }

                    foreach (range($quarterFrom, $quarterTo) as $quarter) {
                        $quarters->push("$year-$quarter");
                    }
                }
            } else {
                foreach (range($quarterFrom, $quarterTo) as $quarter) {
                    if ($quarter >= 3 && ($fromMonth != 12 || $toMonth != 12)) {
                        $updatedYear = $year - 1;
                        $quarters->push("$updatedYear-$quarter");
                    } else {
                        $quarters->push("$year-$quarter");
                    }
                }
            }
        }

        return $this->builder->whereIn('PeriodFrom', $quarters);
    }

    public function course($course)
    {
        $studentPersonalIds = Student::whereCourse($course)->pluck('personal_id');
        return $this->builder->whereIn('piradi_nom', $studentPersonalIds);
    }

    public function status($status)
    {
//        $studentPersonalIds = Student::whereStatusId($status)
//            ->pluck('personal_id');
        return $this->builder->whereHas('student', function ($student) use($status){
            $student->where('status_id', $status);
        });
    }

    public function studentIdentityNumbers(): array
    {

        return Student::pluck('personal_id')->toArray();
    }
}
