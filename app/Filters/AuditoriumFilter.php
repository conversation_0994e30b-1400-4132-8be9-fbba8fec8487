<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class AuditoriumFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Auditorium']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name', 'LIKE', '%' . $keyword . '%')
                ->orWhere('quantity', 'LIKE', $keyword . '%');
        });
    }

    public function campuses($campus_id)
    {
        return $this->builder->where('campus_id', $campus_id);
    }

    public function campus_id($campus_id)
    {
        return $this->campuses($campus_id);
    }
}
