<?php

namespace App\Filters\Hr;

use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Http\Request;

class InvitedLecturerFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Auditorium']);
    }

    public function keyword($keyword)
    {
        return $this->builder
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
            })
            ->orWhere('identity_number', 'LIKE', "%$keyword%")
            ->orWhere('address', 'LIKE', "%$keyword%")
            ->orWhere('phone', 'LIKE', "%$keyword%")
            ->orWhere('date_of_birth', 'LIKE', "%$keyword%")
            ->orWhere('email', 'LIKE', "%$keyword%")
            ;
    }

    public function has_photo($has_photo)
    {
        return $has_photo
            ? $this->builder->whereNotNull('photo')
            : $this->builder->whereNull('photo');
    }

    public function has_cv($has_cv)
    {
        return $has_cv
            ? $this->builder->whereNotNull('cv')
            : $this->builder->whereNull('cv');
    }

    public function user_id($user_id)
    {
        return $this->builder->where('user_id', $user_id);
    }

    public function academic_degree_id($academic_degree_id)
    {
        return $this->builder->where('academic_degree_id', $academic_degree_id);
    }

    public function lmb_id($lmb_id)
    {
        return $this->builder->where('lmb_id', $lmb_id);
    }

    public function lecturer_affiliated($lecturer_affiliated)
    {
        return $this->builder->where('affiliated', $lecturer_affiliated);
    }

    public function lecturer_do_lectures_another_university($lecturer_do_lectures_another_university)
    {
        return $this->builder->where('do_lectures_another_university', $lecturer_do_lectures_another_university);
    }

    // hrInvitedLectureInfo
    public function lecturer_father_name($lecturer_father_name)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_father_name){
            return $hrAcademicLectureInfo->where('father_name', 'LIKE', '%' . $lecturer_father_name);
        });
    }

    public function lecturer_gender($lecturer_gender)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_gender){
            return $hrAcademicLectureInfo->where('gender', $lecturer_gender);
        });
    }

    public function lecturer_age($lecturer_age)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_age){
            return $hrAcademicLectureInfo->where('age', $lecturer_age);
        });
    }

    public function lecturer_family_state($lecturer_family_state)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_family_state){
            return $hrAcademicLectureInfo->where('family_state', $lecturer_family_state);
        });
    }

    // hrInvitedLectureEducations
    public function lecturer_education_qualification($lecturer_education_qualification)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_qualification){
            return $hrAcademicLectureInfo->whereHas('hrInvitedLectureEducations', function ($education) use ($lecturer_education_qualification){
                return $education->where('qualification', 'LIKE', "%$lecturer_education_qualification%");
            });
        });
    }

    public function lecturer_education_country($lecturer_education_country)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_country){
            return $hrAcademicLectureInfo->whereHas('hrInvitedLectureEducations', function ($education) use ($lecturer_education_country){
                return $education->where('country', 'LIKE', "%$lecturer_education_country%");
            });
        });
    }

    public function lecturer_education_academic_degree_id($lecturer_education_academic_degree_id)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_academic_degree_id){
            return $hrAcademicLectureInfo->whereHas('hrInvitedLectureEducations', function ($education) use ($lecturer_education_academic_degree_id){
                return $education->where('academic_degree_id', $lecturer_education_academic_degree_id);
            });
        });
    }

    // hrInvitedLecturePosition
    public function position_salary($position_salary)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_salary){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_salary){
                return $query->where('salary', $position_salary);
            });
        });
    }

    public function position_direction($position_direction)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_direction){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_direction){
                return $query->where('direction', 'LIKE', "%$position_direction%");
            });
        });
    }

    public function position_school_id($position_school_id)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_school_id){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_school_id){
                return $query->where('school_id', $position_school_id);
            });
        });
    }

    public function position_program_id($position_program_id)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_program_id){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_program_id){
                return $query->where('program_id', $position_program_id);
            });
        });
    }

    public function position_course($position_course)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_course){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_course){
                return $query->where('course', 'LIKE', "%$position_course%");
            });
        });
    }

    public function position_work_type_id($work_type_id)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($work_type_id){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($work_type_id){
                return $query->where('work_type_id', $work_type_id);
            });
        });
    }

    public function position_status($position_status)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_status){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_status){
                return $query->where('status', $position_status);
            });
        });
    }

    public function position_workplace_name($position_workplace_name)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_workplace_name){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_workplace_name){
                return $query->where('workplace_name', 'LIKE', "%$position_workplace_name%");
            });
        });
    }

    public function position_position($position_position)
    {
        return $this->builder->whereHas('hrInvitedLectureInfo', function ($query) use ($position_position){
            return $query->whereHas('hrInvitedLecturePosition', function ($query) use ($position_position){
                return $query->where('position', 'LIKE', "%$position_position%");
            });
        });
    }



}
