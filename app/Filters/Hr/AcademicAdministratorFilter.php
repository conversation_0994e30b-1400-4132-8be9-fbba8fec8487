<?php

namespace App\Filters\Hr;

use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Http\Request;

class AcademicAdministratorFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Auditorium']);
    }

    public function keyword($keyword)
    {
        return $this->builder
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
            })
            ->orWhere('identity_number', 'LIKE', "%$keyword%")
            ->orWhere('phone', 'LIKE', "%$keyword%")
            ->orWhere('email', 'LIKE', "%$keyword%")
            ;
    }

    public function administration_position_id($administration_position_id)
    {
        return $this->builder->where('administration_position_id', $administration_position_id);
    }

    public function administration_item_id($administration_item_id)
    {
        return $this->builder->where('administration_item_id', $administration_item_id);
    }

    public function administration_school_id($administration_school_id)
    {
        return $this->builder->where('school_id', $administration_school_id);
    }

    public function user_id($user_id)
    {
        return $this->builder->where('user_id', $user_id);
    }

    public function has_photo($has_photo)
    {
        return $has_photo
            ? $this->builder->whereNotNull('photo')
            : $this->builder->whereNull('photo');
    }

    public function has_cv($has_cv)
    {
        return $has_cv
            ? $this->builder->whereNotNull('cv')
            : $this->builder->whereNull('cv');
    }


    // hrAdministrationInfo
    public function info_father_name($info_father_name)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($info_father_name){
            return $hrAdministrationInfo->where('father_name', 'LIKE', '%' . $info_father_name);
        });
    }

    public function info_gender($info_gender)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($info_gender){
            return $hrAdministrationInfo->where('gender', $info_gender);
        });
    }

    public function info_age($info_age)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($info_age){
            return $hrAdministrationInfo->where('age', $info_age);
        });
    }

    public function info_family_state($info_family_state)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($info_family_state){
            return $hrAdministrationInfo->where('family_state', $info_family_state);
        });
    }

    public function info_date_of_birth($info_date_of_birth)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($info_date_of_birth){
            return $hrAdministrationInfo->where('date_of_birth', $info_date_of_birth);
        });
    }

    // hrAdministrationEducations
    public function education_qualification($education_qualification)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($education_qualification){
            return $hrAdministrationInfo->whereHas('hrAdministrationEducations', function ($education) use ($education_qualification){
                return $education->where('qualification', 'LIKE', "%$education_qualification%");
            });
        });
    }

    public function education_country($education_country)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($education_country){
            return $hrAdministrationInfo->whereHas('hrAdministrationEducations', function ($education) use ($education_country){
                return $education->where('country', 'LIKE', "%$education_country%");
            });
        });
    }

    public function education_academic_degree_id($education_academic_degree_id)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($education_academic_degree_id){
            return $hrAdministrationInfo->whereHas('hrAdministrationEducations', function ($education) use ($education_academic_degree_id){
                return $education->where('academic_degree_id', $education_academic_degree_id);
            });
        });
    }

    public function education_academic_position($education_academic_position)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($education_academic_position){
            return $hrAdministrationInfo->whereHas('hrAdministrationEducations', function ($education) use ($education_academic_position){
                return $education->where('position', $education_academic_position);
            });
        });
    }

    // hrAdministrationAppointment
    public function appointment($appointment)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($hrAdministrationInfo) use ($appointment){
            return $hrAdministrationInfo->whereHas('hrAdministrationAppointment', function ($query) use ($appointment){
                return $query->where('appointment', $appointment);
            });
        });
    }

    public function appointment_vacancy_command_number($appointment_vacancy_command_number)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_vacancy_command_number){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_vacancy_command_number){
                return $query->where('vacancy_command_number', 'LIKE', "%$appointment_vacancy_command_number%");
            });
        });
    }

    public function appointment_vacancy_command_number_file($appointment_vacancy_command_number_file)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_vacancy_command_number_file){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_vacancy_command_number_file){
                return $query->where('vacancy_command_number_file', 'LIKE', "%$appointment_vacancy_command_number_file%");
            });
        });
    }

    public function appointment_vacancy_command_number_date($appointment_vacancy_command_number_date)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_vacancy_command_number_date){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_vacancy_command_number_date){
                return $query->where('vacancy_command_number_date', 'LIKE', "%$appointment_vacancy_command_number_date%");
            });
        });
    }

    public function appointment_command_number($appointment_command_number)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_command_number){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_command_number){
                return $query->where('appointment_command_number', 'LIKE', "%$appointment_command_number%");
            });
        });
    }

    public function appointment_command_number_file($appointment_command_number_file)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_command_number_file){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_command_number_file){
                return $query->where('appointment_command_number_file', 'LIKE', "%$appointment_command_number_file%");
            });
        });
    }

    public function appointment_command_number_date($appointment_command_number_date)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_command_number_date){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_command_number_date){
                return $query->where('appointment_command_number_date', 'LIKE', "%$appointment_command_number_date%");
            });
        });
    }

    public function appointment_position($appointment_position)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_position){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_position){
                return $query->where('position', 'LIKE', "%$appointment_position%");
            });
        });
    }

    public function appointment_type_of_position($appointment_type_of_position)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_type_of_position){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_type_of_position){
                return $query->where('type_of_position', $appointment_type_of_position);
            });
        });
    }

    public function appointment_contract_start($appointment_contract_start)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_contract_start){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_contract_start){
                return $query->where('contract_start', $appointment_contract_start);
            });
        });
    }

    public function appointment_contract_end($appointment_contract_end)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_contract_end){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_contract_end){
                return $query->where('contract_end', $appointment_contract_end);
            });
        });
    }

    public function appointment_contract_period($appointment_contract_period)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_contract_period){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_contract_period){
                return $query->where('contract_period', 'LIKE', "%$appointment_contract_period%");
            });
        });
    }

    public function appointment_vacation($appointment_vacation)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_vacation){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_vacation){
                return $query->where('vacation', 'LIKE', "%$appointment_vacation%");
            });
        });
    }

    public function appointment_day_off($appointment_day_off)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_day_off){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_day_off){
                return $query->where('day_off', 'LIKE', "%$appointment_day_off%");
            });
        });
    }

    public function appointment_status($appointment_status)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_status){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_status){
                return $query->where('status', $appointment_status);
            });
        });
    }

    public function appointment_educational_staff($appointment_educational_staff)
    {
        return $this->builder->whereHas('hrAdministrationInfo', function ($query) use ($appointment_educational_staff){
            return $query->whereHas('hrAdministrationAppointment', function ($query) use ($appointment_educational_staff){
                return $query->where('educational_staff', $appointment_educational_staff);
            });
        });
    }



}
