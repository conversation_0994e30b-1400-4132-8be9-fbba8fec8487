<?php

namespace App\Filters\Hr;

use App\Filters\QueryFilters;
use App\Models\Own;
use Illuminate\Http\Request;

class AcademicLecturerFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request,Own::MODELS['Auditorium']);
    }

    public function keyword($keyword)
    {
        return $this->builder
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
            })
            ->orWhere('identity_number', 'LIKE', "%$keyword%")
            ->orWhere('address', 'LIKE', "%$keyword%")
            ->orWhere('phone', 'LIKE', "%$keyword%")
            ->orWhere('date_of_birth', 'LIKE', "%$keyword%")
            ->orWhere('email', 'LIKE', "%$keyword%")
            ;
    }

    public function has_photo($has_photo)
    {
        return $has_photo
            ? $this->builder->whereNotNull('photo')
            : $this->builder->whereNull('photo');
    }

    public function has_cv($has_cv)
    {
        return $has_cv
            ? $this->builder->whereNotNull('cv')
            : $this->builder->whereNull('cv');
    }

    public function user_id($user_id)
    {
        return $this->builder->where('user_id', $user_id);
    }

    public function academic_degree_id($academic_degree_id)
    {
        return $this->builder->where('academic_degree_id', $academic_degree_id);
    }

    public function lmb_id($lmb_id)
    {
        return $this->builder->where('lmb_id', $lmb_id);
    }

    public function lecturer_affiliated($lecturer_affiliated)
    {
        return $this->builder->where('affiliated', $lecturer_affiliated);
    }

    public function lecturer_do_lectures_another_university($lecturer_do_lectures_another_university)
    {
        return $this->builder->where('do_lectures_another_university', $lecturer_do_lectures_another_university);
    }

    public function lecturer_father_name($lecturer_father_name)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_father_name){
            return $hrAcademicLectureInfo->where('father_name', 'LIKE', '%' . $lecturer_father_name);
        });
    }

    public function lecturer_gender($lecturer_gender)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_gender){
            return $hrAcademicLectureInfo->where('gender', $lecturer_gender);
        });
    }

    public function lecturer_age($lecturer_age)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_age){
            return $hrAcademicLectureInfo->where('age', $lecturer_age);
        });
    }

    public function lecturer_family_state($lecturer_family_state)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_family_state){
            return $hrAcademicLectureInfo->where('family_state', $lecturer_family_state);
        });
    }

    //hrAcademicLectureEducations
    public function lecturer_education_qualification($lecturer_education_qualification)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_qualification){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureEducations', function ($education) use ($lecturer_education_qualification){
                return $education->where('qualification', 'LIKE', "%$lecturer_education_qualification%");
            });
        });
    }

    public function lecturer_education_country($lecturer_education_country)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_country){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureEducations', function ($education) use ($lecturer_education_country){
                return $education->where('country', 'LIKE', "%$lecturer_education_country%");
            });
        });
    }

    public function lecturer_education_academic_degree_id($lecturer_education_academic_degree_id)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_education_academic_degree_id){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureEducations', function ($education) use ($lecturer_education_academic_degree_id){
                return $education->where('academic_degree_id', $lecturer_education_academic_degree_id);
            });
        });
    }

    //hrAcademicLecturePosition
    public function lecturer_position_position_id($lecturer_position_position_id)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_position_id){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_position_id){
                return $position->where('lecturer_position_id', $lecturer_position_position_id);
            });
        });
    }


    public function lecturer_position_grant($lecturer_position_grant)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_grant){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_grant){
                return $position->where('grant', $lecturer_position_grant);
            });
        });
    }

    public function lecturer_position_affiliated($lecturer_position_affiliated)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_affiliated){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_affiliated){
                return $position->where('affiliated', $lecturer_position_affiliated);
            });
        });
    }

    public function lecturer_position_category_id($lecturer_position_category_id)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_category_id){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_category_id){
                return $position->where('lecturer_category_id', $lecturer_position_category_id);
            });
        });
    }

    public function lecturer_position_salary($lecturer_position_salary)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_salary){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_salary){
                return $position->where('salary', $lecturer_position_salary);
            });
        });
    }

    public function lecturer_position_paid_hours($lecturer_position_paid_hours)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_paid_hours){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_paid_hours){
                return $position->where('paid_hours', $lecturer_position_paid_hours);
            });
        });
    }

    public function lecturer_position_unpaid_hours($lecturer_position_unpaid_hours)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_unpaid_hours){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_unpaid_hours){
                return $position->where('unpaid_hours', $lecturer_position_unpaid_hours);
            });
        });
    }

    public function lecturer_position_direction($lecturer_position_direction)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_direction){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_direction){
                return $position->where('direction', 'LIKE', "%$lecturer_position_direction%");
            });
        });
    }

    public function lecturer_position_school_id($lecturer_position_school_id)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_school_id){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_school_id){
                return $position->where('school_id', $lecturer_position_school_id);
            });
        });
    }

    public function lecturer_position_appointment($lecturer_position_appointment)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_appointment){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_appointment){
                return $position->where('appointment', 'LIKE', "%$lecturer_position_appointment%");
            });
        });
    }

    public function lecturer_position_vacancy_command_number($lecturer_position_vacancy_command_number)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_vacancy_command_number){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_vacancy_command_number){
                return $position->where('vacancy_command_number', 'LIKE', "%$lecturer_position_vacancy_command_number%");
            });
        });
    }

    public function lecturer_position_vacancy_command_number_file($lecturer_position_vacancy_command_number_file)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_vacancy_command_number_file){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_vacancy_command_number_file){
                return $position->where('vacancy_command_number_file', 'LIKE', "%$lecturer_position_vacancy_command_number_file%");
            });
        });
    }

    public function lecturer_position_vacancy_command_number_date($lecturer_position_vacancy_command_number_date)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_vacancy_command_number_date){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_vacancy_command_number_date){
                return $position->where('vacancy_command_number_date', 'LIKE', "%$lecturer_position_vacancy_command_number_date%");
            });
        });
    }

    public function lecturer_position_appointment_command_number($lecturer_position_appointment_command_number)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_appointment_command_number){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_appointment_command_number){
                return $position->where('appointment_command_number', 'LIKE', "%$lecturer_position_appointment_command_number%");
            });
        });
    }

    public function lecturer_position_appointment_command_number_file($lecturer_position_appointment_command_number_file)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_appointment_command_number_file){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_appointment_command_number_file){
                return $position->where('appointment_command_number_file', 'LIKE', "%$lecturer_position_appointment_command_number_file%");
            });
        });
    }

    public function lecturer_position_appointment_command_number_date($lecturer_position_appointment_command_number_date)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_appointment_command_number_date){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_appointment_command_number_date){
                return $position->where('appointment_command_number_date', 'LIKE', "%$lecturer_position_appointment_command_number_date%");
            });
        });
    }

    public function lecturer_position_contact_start($lecturer_position_contact_start)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_contact_start){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_contact_start){
                return $position->where('contract_start', $lecturer_position_contact_start);
            });
        });
    }

    public function lecturer_position_contract_end($lecturer_position_contract_end)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_contract_end){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_contract_end){
                return $position->where('contract_end', $lecturer_position_contract_end);
            });
        });
    }

    public function lecturer_position_contract_period($lecturer_position_contract_period)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_contract_period){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_contract_period){
                return $position->where('contract_period', 'LIKE', "%$lecturer_position_contract_period%");
            });
        });
    }

    public function lecturer_position_status($lecturer_position_status)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_position_status){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLecturePosition', function ($position) use ($lecturer_position_status){
                return $position->where('status', $lecturer_position_status);
            });
        });
    }

    // hrAcademicLectureAdditional
    public function lecturer_additional_scopus_g($lecturer_additional_scopus_g)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_scopus_g){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_scopus_g){
                return $position->where('scopus_g', $lecturer_additional_scopus_g);
            });
        });
    }

    public function lecturer_additional_scopus_h($lecturer_additional_scopus_h)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_scopus_h){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_scopus_h){
                return $position->where('scopus_h', $lecturer_additional_scopus_h);
            });
        });
    }

    public function lecturer_additional_web_of_science_g($lecturer_additional_web_of_science_g)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_web_of_science_g){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_web_of_science_g){
                return $position->where('web_of_science_g', $lecturer_additional_web_of_science_g);
            });
        });
    }

    public function lecturer_additional_web_of_science_h($lecturer_additional_web_of_science_h)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_web_of_science_h){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_web_of_science_h){
                return $position->where('web_of_science_h', $lecturer_additional_web_of_science_h);
            });
        });
    }

    public function lecturer_additional_google_scholar_g($lecturer_additional_google_scholar_g)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_google_scholar_g){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_google_scholar_g){
                return $position->where('google_scholar_g', $lecturer_additional_google_scholar_g);
            });
        });
    }

    public function lecturer_additional_google_scholar_h($lecturer_additional_google_scholar_h)
    {
        return $this->builder->whereHas('hrAcademicLectureInfo', function ($hrAcademicLectureInfo) use ($lecturer_additional_google_scholar_h){
            return $hrAcademicLectureInfo->whereHas('hrAcademicLectureAdditional', function ($position) use ($lecturer_additional_google_scholar_h){
                return $position->where('google_scholar_h', $lecturer_additional_google_scholar_h);
            });
        });
    }





}
