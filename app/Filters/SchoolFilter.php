<?php


namespace App\Filters;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class SchoolFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['School']);
    }

    public function keyword($keyword)
    {
        return $this->builder->where(function ($query) use ($keyword) {
            return $query->where('name_en', 'LIKE', '%' . $keyword . '%')
                ->orWhere('name_ka', 'LIKE', '%' . $keyword . '%');
        });
    }

    public function campus($campus_id)
    {
        return $this->builder->where('campus_id', $campus_id);
    }

    public function school($school_id)
    {
        return $this->builder->find($school_id);
    }

    public function school_id($school_id)
    {
        return $this->builder->find($school_id);
    }

    public function program_id($program_id)
    {
        return $this->builder->whereHas('programs', function ($query) use ($program_id) {
            return $query->where('id', $program_id);
        });
    }

    public function program($program_id)
    {
        return $this->builder->whereHas('programs', function ($query) use ($program_id) {
            return $query->where('id', $program_id);
        });
    }

}
