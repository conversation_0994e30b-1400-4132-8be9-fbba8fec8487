<?php

namespace App\Filters;

use App\Models\Own;
use Carbon\Carbon;
use Illuminate\Http\Request;

class StudentFinanceStatusLogFilter extends QueryFilters
{
    protected ?Request $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        parent::__construct($request, Own::MODELS['School']);
    }

    public function period_from($period_from)
    {
        return $this->builder->where('created_at', '>=', Carbon::make($period_from));
    }

    public function period_to($period_to)
    {
        return $this->builder->where('created_at', '<=', Carbon::make($period_to.' 23:59:59'));
    }
}
