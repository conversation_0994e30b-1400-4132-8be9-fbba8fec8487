<?php

namespace App\Imports;

use App\Models\Reestry\Student\StudentAssignment;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class ImportStudentMarks implements ToCollection
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection): void
    {
        // ჰედერიდან შეფასების აიდების ამოყრა
        $assignmentIds = [];
        for ($i = 7; $i < count($collection[0]); $i++) {
            $assignment = explode('- ',$collection[0][$i]);
            $assignmentIds[$i] = end($assignment);
        }

        $syllabusId = $collection[0][0];
        $syllabusId = explode(': ', $syllabusId);
        $syllabusId = end($syllabusId);

        foreach ($collection as $key => $value) {
            if ($key==0)
            {
                continue;
            }
            $student = $value[5]; // სტატუკურად უნდა იჯდეს 4 არაა ბაგი
            $student = explode('- ', $student);
            $studentId = end($student);

            foreach ($assignmentIds as $assignmentAddress => $assignmentId) {
                $point = $value[$assignmentAddress];
//print_r($point);
                if(!is_numeric($point)){
                    $key++;
                    $assignmentAddress++;
                    throw new \Exception("x:$key,y:$assignmentAddress");
                }

                StudentAssignment::query()->updateOrCreate([
                    'assignment_id' => $assignmentId,
                    'student_id' => $studentId,
                ],[
                    'point' => $point,
                    'date' => now(),
                    'syllabus_id' => $syllabusId
                ]);
            }
        }
    }
}
