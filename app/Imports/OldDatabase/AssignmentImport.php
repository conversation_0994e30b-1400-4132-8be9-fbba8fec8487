<?php

namespace App\Imports\OldDatabase;

use App\Models\History;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Syllabus\Syllabus;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\DB;

class AssignmentImport implements ToCollection, WithHeadingRow
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
//        DB::beginTransaction();
        try {
            $historyData = collect();
            $insertEvery = 2;
            $students = Student::query()
                ->select(['id', 'lmb_id'])
//                ->where('lmb_id', $item['studenti'])
                ->get()
            ;

            $syllabuses = Syllabus::query()
                ->select(['id', 'lmb_id'])
//                ->where('lmb_id', $item['sagani'])
                ->get()
            ;
            $insertDataIndex = 0;
            foreach ($collection as $key => $item)
            {
                $student = $students->where('lmb_id', $item['studenti'])->first();
                $syllabus = $syllabuses->where('lmb_id', $item['sagani'])->first();

                if (!$student or !$syllabus)
                {
                    \Log::channel('cron_logs')->alert('not-found' ,[
                        'student_id' => $item['studenti'],
                        'syllabus_id' => $item['sagani'],
                    ]);
                    continue;
                }
                $studentId = $student->id;
                $syllabusId = $syllabus->id;

                $existedHistoryIndex = $historyData
                    ->search(function ($record) use ($studentId, $syllabusId) {
                        return $record['student_id'] == $studentId && $record['syllabus_id'] == $syllabusId;
                    });



                if ($existedHistoryIndex !== false) {
                    $updatedHistory = $historyData[$existedHistoryIndex];
                    $updatedHistory['point'] = $item['nishani'] ?? null;
                    $updatedHistory['is_passed'] = $item['nishani'] !== null ? round(floatval($item['nishani'])) >= 51 : null;
                    $historyData[$existedHistoryIndex] = $updatedHistory;
                }else{
                    $now = Carbon::now();
                    $historyData->push([
                        'student_id' => $studentId,
                        'syllabus_id' => $syllabusId,
                        'is_passed' => $item['nishani'] !== null ? round(floatval($item['nishani'])) >= 51 : null,
                        'point' => $item['nishani'] ?? null,
                        'is_closed' => 1,
                        'created_at' => $now,
                        'updated_at' => $now,
                        'lmb' => true
                    ]);
                }
                $insertDataIndex = $insertDataIndex + 1;

                if ($insertDataIndex % $insertEvery == 0)
                {
                    //History::query()->insert($historyData->toArray());
                    StudentSyllabusHistory::query()->insert($historyData->toArray());
                    $historyData = collect();
                    $insertDataIndex = 0;
                }
            }
            //History::query()->insert($historyData->toArray());
            StudentSyllabusHistory::query()->insert($historyData->toArray());
//            DB::commit();
        }catch (\Exception $exception) {
//            DB::rollback();
            \Log::channel('cron_logs')->alert('hard-error' ,[
                'key' => $key,
                'message' => $exception->getMessage()
            ]);
        }
    }
}
