<?php

namespace App\Imports\OldDatabase;

use App\Models\Syllabus\Syllabus;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SyllabiImport implements ToCollection, WithHeadingRow
{
    public function __construct(public $learnYearId, public $academicDegreeId)
    {
        //
    }

    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        DB::beginTransaction();
        try {
            foreach ($collection as $key => $item)
            {
                if (
                    is_null($item['semestri'])
                    or is_null($item['sakheli'])
                    or is_null($item['sakheli_english'])
                    or is_null($item['kodi'])
                ){
                    \Log::channel('cron_logs')->alert('missed-data', [
                        'key' => $key,
                    ]);
                    continue;
                }

                Syllabus::query()->updateOrCreate([
                    'lmb_id' => $item['id'],
                ],[
                    'is_training' => 0,
                    'is_profession' => 0,
                    'learn_year_id' => $this->learnYearId,
                    'semester_id' => $item['semestri'],
                    'name' => $item['sakheli'],
                    'name_en' => $item['sakheli_english'],
                    'contact_hours' => $item['saatebi'] ?? 0,
                    'credits' => $item['krediti'] ?? 0,
                    'code' => $item['kodi'],
                    'status_id' => 1,
                    'academic_degree_id' => $this->academicDegreeId,
                    'lecture_hours' => $item['saatebi'] ?? 0,
                    'seminar_hours' => 0,
                    'mid_and_final_exam_hours' => 0,
                    'independent_work_hours' => 0,
                    'total_hours' => $item['saatebi'] ?? 0,
                ]);
            }
            DB::commit();

        }catch (\Exception $exception)
        {
            DB::rollback();

            \Log::channel('cron_logs')->alert('fatal-error', [
                'key' => $key,
                'message' => $exception->getMessage()
            ]);
        }

    }
}
