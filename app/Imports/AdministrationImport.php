<?php

namespace App\Imports;

use App\Models\Reestry\Administration\Administration;
use App\Models\RoleUser;
use App\Models\User\User;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class AdministrationImport implements ToModel, WithHeadingRow, WithValidation
{
    private $skippedRows = [];
    private $importedRowCount = 0;


    public function model(array $row)
    {
        if (Administration::where('identity_number', $row['personal_id'])->exists()) {
            $this->skippedRows[] = [
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'identity_number' => $row['personal_id'],
                'email' => $row['email'],
                'administration_item_id' => $row['administration_item_id']
            ];
            return null;
        }

        $this->importedRowCount++;

        $user = User::where('email', $row['email'])->first();

        if ($user) {
            $roleUser = new RoleUser([
                'user_id' => $user->id,
                'role_id' => 1,
            ]);
            $roleUser->save();
            return new Administration([
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'user_id' => $user->id,
                'identity_number' => $row['personal_id'],
                'email' => $row['email'],
                'administration_item_id' => $row['administration_item_id']
            ]);

        }
    }

    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }


    public function rules(): array
    {
        return [
            'personal_id' => [
                'required',
                'regex:/^[0-9]+$/'
            ],
        ];
    }
}
