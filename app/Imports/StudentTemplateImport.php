<?php

namespace App\Imports;

use App\Models\Reestry\Student\Student;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class StudentTemplateImport implements ToCollection, WithHeadingRow
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        foreach ($collection as $item)
        {
            $student = Student::query()->find($item['student_id']);
            if ($student)
            {
                $student->update([
                    'enrollment_date' => Carbon::createFromFormat('d.m.Y', $item['enrollement_date']),
                    'enrollment_order' => $item['enrollement_order']
                ]);
            }
        }
    }
}
