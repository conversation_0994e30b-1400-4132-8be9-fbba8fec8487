<?php

namespace App\Imports;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\User\User;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
set_time_limit(300);
class LecturersImport implements ToModel, WithHeadingRow, WithValidation
{
    private $skippedRows = [];
    private $importedRowCount = 0;


    public function model(array $row)
    {
        if (Lecturer::where('identity_number', $row['personal_id'])->exists()) {
            $this->skippedRows[] = [
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'identity_number' => $row['personal_id'],
                'phone' => $row['phone'],
                'email' => $row['email']
            ];
            return null;
        }

        $this->importedRowCount++;

        $user = User::where('email', $row['email'])->first();

        if ($user) {
            return new Lecturer([
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'user_id' => $user->id,
                'identity_number' => $row['personal_id'],
                'phone' => $row['phone'],
                'date_of_birth' => Carbon::createFromFormat('d.m.Y', $row['date_of_birth'])->format('Y-m-d'),
                'email' => $row['email'],
                'academic_degree_id' => 1,
                'affiliated' => $row['affiliated'],
                'lmb_id' => $row['lmb_id'],
            ]);
        }
    }

    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }


    public function rules(): array
    {
        return [
            'personal_id' => [
                'required',
//                'regex:/^[0-9]+$/'
            ],
        ];
    }
}
