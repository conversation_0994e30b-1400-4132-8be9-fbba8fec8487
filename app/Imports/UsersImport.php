<?php

namespace App\Imports;

use App\Models\User\User;
use Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UsersImport implements ToModel, WithHeadingRow
{
    private $user_type_id;
    private $skippedRows = [];
    private $importedRowCount = 0;

    public function __construct($user_type_id)
    {
        $this->user_type_id = $user_type_id;
    }
    public function model(array $row)
    {
        if (empty($row['email'])) {
            $this->skippedRows[] = [
                'name' => $row['first_name'].' '.$row['last_name'],
                'email' => $row['email']
            ];
            return null;
        }

        if (User::where('email', $row['email'])->exists()) {
            $this->skippedRows[] = [
                'name' => $row['first_name'].' '.$row['last_name'],
                'email' => $row['email']
            ];
            return null;
        }

        $this->importedRowCount++;
        return new User([
            'name' => $row['first_name'].' '.$row['last_name'],
            'email' => $row['email'],
            'password' => Hash::make('gipa2023'),
            'user_type_id' => $this->user_type_id,
            'is_super_admin' => 0,
        ]);
    }


    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }

    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ]
        ];
    }
}
