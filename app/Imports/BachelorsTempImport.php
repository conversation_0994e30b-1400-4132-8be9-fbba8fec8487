<?php

namespace App\Imports;

namespace App\Imports;

use App\Models\RegisterForms\BachelorsTemp;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class BachelorsTempImport implements ToModel, WithHeadingRow, WithValidation
{
    private int $program_id;
    private int $flow_id;
    private array $skippedRows = [];
    private int $importedRowCount = 0;

    public function __construct($program_id, $flow_id)
    {
        $this->program_id = $program_id;
        $this->flow_id = $flow_id;
    }

    public function model(array $row)
    {
        if (BachelorsTemp::where('personal_id', $row['personal_id'])->where('program_id', $this->program_id)->where('flow_id', $this->flow_id)->exists()) {
            $this->skippedRows[] = [
                'personal_id' => $row['personal_id'],
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
            ];
            return null;
        }

        $this->importedRowCount++;

        return new BachelorsTemp([
            'program_id' => $this->program_id,
            'flow_id' => $this->flow_id,
            'personal_id' => $row['personal_id'],
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
        ]);
    }

    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }


    public function rules(): array
    {
        return [
            'personal_id' => [
                'required',
                'regex:/^[0-9]+$/'
            ],
        ];
    }
}
