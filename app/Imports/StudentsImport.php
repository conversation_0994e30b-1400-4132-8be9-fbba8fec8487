<?php

namespace App\Imports;

use App\Models\Reestry\Student\Student;
use App\Models\User\User;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class StudentsImport implements ToModel, WithHeadingRow, WithValidation
{
    private $program_id;
    private $flow_id;
    private $school_id;
    private $skippedRows = [];
    private $importedRowCount = 0;

    public function __construct($program_id, $flow_id, $school_id)
    {
        $this->program_id = $program_id;
        $this->flow_id = $flow_id;
        $this->school_id = $school_id;
    }

    public function model(array $row)
    {
        if (Student::where('personal_id', $row['personal_id'])->exists()) {
            $this->skippedRows[] = [
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'identity_number' => $row['personal_id'],
                'phone' => $row['phone'],
                'email' => $row['email']
            ];
            return null;
        }

        $this->importedRowCount++;

        $user = User::where('email', $row['email'])->first();

        if ($user) {
            return new Student([
                'name' => $row['first_name'],
                'name_en' => $row['first_name_en'],
                'surname' => $row['last_name'],
                'surname_en' => $row['last_name_en'],
                'user_id' => $user->id,
                'personal_id' => $row['personal_id'],
                'personal_id_number' => $row['personal_id_number'],
                'sex' => $row['gender'],
                'address' => $row['address'],
                'citizenship' => $row['citizenship'],
                'phone' => $row['phone'],
                'email' => $row['email'],
                'birthday' => Carbon::createFromFormat('d.m.Y', $row['date_of_birth']),
                'enrollment_date' => Carbon::createFromFormat('d.m.Y', $row['enrollment_date']),
                'enrollment_order' => $row['enrollment_order'],
                'mobility' => $row['mobility'],
                'status_id' => $row['status_id'],
                'basics_of_enrollement_id' => $row['basics_of_enrollement_id'],
                'school_id' => $this->school_id,
                'program_id' => $this->program_id,
                'group_id' => $row['group_id'],
                'learn_year_id' => $this->flow_id,
                'course' => $row['course'],
                'lmb_id' => $row['lmb_id'],
            ]);
        }
    }

    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }

    public function rules(): array
    {
        return [
            'personal_id' => [
                'required',
//                'regex:/^[0-9]+$/'
            ],
        ];
    }
}
