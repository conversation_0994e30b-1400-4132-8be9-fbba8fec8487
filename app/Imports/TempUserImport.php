<?php

namespace App\Imports;

use App\Models\TempUser;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class TempUserImport implements ToModel, WithHeadingRow, WithValidation
{
    private array $skippedRows = [];
    private int $importedRowCount = 0;

    public function __construct()
    {

    }
    public function model(array $row)
    {
        if (TempUser::where('personal_id', $row['personal_id'])->exists()) {
            $this->skippedRows[] = [
                'personal_id' => $row['personal_id']
            ];
            return null;
        }

        $this->importedRowCount++;

        return new TempUser([
            'personal_id' => $row['personal_id']
        ]);
    }

    public function getSkippedRows()
    {
        return $this->skippedRows;
    }

    public function getImportedRowCount()
    {
        return $this->importedRowCount;
    }


    public function rules(): array
    {
        return [
            'personal_id' => [
                'required',
                'regex:/^[0-9]+$/'
            ],
        ];
    }
}
