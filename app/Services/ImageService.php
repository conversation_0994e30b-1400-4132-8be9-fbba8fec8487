<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Class ImageService
 * @package App\Services
 */
class ImageService
{

    public function upload($file, $path, $uploadByName = null, $generateOriginalName=false): string
    {
        if (!$file) {
            return "Error: Invalid file provided.";
        }

        if($generateOriginalName) {
            // Get original file name and extension
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();

            // Check if file exists with the same name
            $exists = Storage::disk('public')->exists($path . '/' . $originalName);

            // If file exists, add timestamp to the name
            if ($exists) {
                $timestamp = time();
                $fileName = $originalName . '-' . $timestamp . '.' . $extension;
            } else {
                $fileName = $originalName;
            }
        }
        else{
            $fileName = $uploadByName ?? Str::random(16) . '.' . $file->getClientOriginalExtension();
        }

        try {
            Storage::disk('public')->putFileAs($path, $file, $fileName);
            return $path . '/' . $fileName;
        } catch (\Exception $e) {
            return "Error: " . $e->getMessage();
        }
    }



}
