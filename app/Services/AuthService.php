<?php

namespace App\Services;

use App\Mail\ForgotPasswordMail;
use App\Models\Finance\Finance;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\Role;
use App\Models\User\User;
use App\Models\User\UserType;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class AuthService
{

    public function googleAuth(): array|string
    {
        $user = Socialite::driver('google')->stateless()->user();
        if (!Str::endsWith($user->getEmail(), config('app.email_domain'))) {
            return 'ავტორიზაცია შესაძლებელია მხოლოდ ჯიპას მეილით!';
        }
        $userByEmail = User::with('roles.permissions')
            ->where('email', $user->getEmail());
        if (!$userByEmail->exists()) {
            return 'ამ მეილით მომხმარებელი არ არის რეგისტრირებული!';
        }
        $user = $userByEmail->first();
        $token = $user->createToken('MyApp')->accessToken;
        $response = [];
        $response['user'] = $userByEmail;
        $response['accessToken'] = $token;
        $response['permissions'] = $user->roles->pluck('permissions')
            ->flatten()->pluck('title')->unique();
        return $response;
    }

    public function forgotPassword(): string
    {
        $email = request()->email;
        $token = Str::random(16);
        DB::insert(
            'insert into password_resets (email,token,created_at) values(?,?,?)',
            [$email, $token, now()]
        );
        $name = User::where('email', $email)->first()->name;
        $sendMail = Mail::to($email)->send(new ForgotPasswordMail($email, $name, $token));
        if (empty($sendMail)) {
            return 'პაროლის განახლების ლინკს მიიღებთ მეილზე';
        }
        return 'Password reset function will be available soon!';
    }

    public function resetPassword(): string
    {
        $token = request()->token;
        $email = DB::table('password_resets')
            ->where('token', '=', $token)
            ->first()->email;
        User::where('email', $email)->update([
            'password' => bcrypt(request()->password)
        ]);
        DB::table('password_resets')->where('token', '=', $token)->delete();
        return 'პაროლი წარმატებით შეიცვალა!';
    }

    public function administratorInfo(int $userId): array
    {
        $response = [];
        $administration = Administration::where('user_id', $userId)->firstOrFail();
        $response['first_name'] = $administration->first_name;
        $response['last_name'] = $administration->last_name;
        $response['photo'] = $administration->photo;
        $response['identity_number'] = $administration->identity_number;
        $response['phone'] = $administration->phone;
        $response['email'] = $administration->email;
        $response['cv'] = $administration->cv;
        $response['id'] = $administration->id;
        $response['user_id'] = $administration->user_id;
        $response['position'] = $administration->administrationPosition->name_ka ?? null;
        $response['school'] = $administration->school->name_ka ?? null;
        return $response;
    }

    public function lecturerInfo(int $userId): array
    {
        $response = [];
        $lecturer = Lecturer::where('user_id', $userId)->firstOrFail();
        $response['first_name'] = $lecturer->first_name;
        $response['last_name'] = $lecturer->last_name;
        $response['photo'] = $lecturer->photo;
        $response['lecturer_id'] = $lecturer->id;
        $response['user_id'] = $lecturer->user_id;
        return $response;
    }

    public function studentInfo(int $userId): array
    {

        $response = [];
        $student = Student::where('user_id', $userId)->firstOrFail();
        $response['first_name'] = $student->name;
        $response['first_name_en'] = $student->name_en;
        $response['last_name'] = $student->surname;
        $response['last_name_en'] = $student->surname_en;
        $response['photo'] = $student->photo;
        $response['student_id'] = $student->id;
        $response['personal_id'] = $student->personal_id;
        $response['school'] = $student->school->name_ka ?? null;
        $response['program'] = $student->program->name_ka ?? null;
        $response['user_id'] = $userId;
        $response['status_id'] = $student->status_id;
        $response['is_profession'] = $student->program->academic_degree_id == 4 ? true : false;
        $response['gpa'] = round($student->gpa,3); //TODO:: get gpa

        $finance = Finance::wherePiradiNom((new FinanceService($userId))->getStudentIdentityNumber())
            ->where('ProgramID', $student->program_id)
            ->orderByDesc('id')
            ->first();

        if ($finance) {
            //$financeId = $finance->id;
            //$financeService = new FinanceService(userId: $userId, financeId: $financeId);
            $response['finance_debt'] = max((new FinanceService(userId: $userId))->studentCurrentDebtForAuth(), 0);
        } else {
            $response['finance_debt'] = 0;
        }
        //$response['finance_extra'] =  null;
        //$response['finance_extra'] = $financeService->quarterDebt() > 0 ? $financeService->quarterDebt() : null;
        return $response;
    }

}
