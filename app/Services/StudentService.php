<?php

namespace App\Services;

use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Lectures\Lecture;
use App\Models\Lectures\StudentAttendance;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Survey\SurveyActivation;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use function PHPUnit\Framework\matches;

class StudentService
{

    public function studentSubjects($semesterID, int $id = null): array
    {
        if($id) {
            $student = Student::whereId($id)->first();
        } else {
            $student = Student::whereUserId(\Auth::id())
                ->first();
        }
        if (!$student) {
            return [
                'code' => 404,
                'message' => 'Student not found!'
            ];
        }
        if (!$semesterID || $semesterID === 'undefined' || !is_numeric($semesterID)) {
            $setting = Setting::where('key', 'current_semester')->first();
            if (!$setting) {
                return [
                    'code' => 400,
                    'message' => 'No current semester defined in settings!'
                ];
            }
            $semesterID = $setting->value;
        }
//        if($semesterID==1){
//            $semesterID = Setting::where('key', '=', 'first_semester')
//                ->first()->value;
//        } elseif($semesterID==2){
//            $semesterID = Setting::where('key', '=', 'second_semester')
//                ->first()->value;
//        } else {
//            $semesterID = Setting::where('key', '=', 'current_semester')
//                ->first()->value;
//        }
        $studentId = $student->id;
        $syllabuses = Syllabus::with(['semester:id,name', 'curriculum.flow', 'status:id,name_ka,name_en'])
            //->whereHas('curriculum')
            ->whereHas('curriculum', function ($query) use ($semesterID) {
                //$currentFlowId = Setting::where('key', 'current_semester')->first()->value;
                return $query->where(
                    'flow_id',
                    $semesterID
                );
            })
            ->whereHas('students', function ($query) use ($studentId) {
                return $query->where('student_id', $studentId);
            })
            ->get();
        if (!$syllabuses->count()) {
            return [
                'code' => 404,
                'message' => "Subject's not found!"
            ];
        }
        $subjectList = $syllabuses->map(function ($syllabus) use ($studentId, $student) {
            $syllabusService = new SyllabusService();
            $previousLecture = Lecture::whereSyllabusId($syllabus->id)
                ->whereDate('lecture_date', '<', now()->format('Y-m-d'))
                ->orderByDesc('id')
                ->first();
            $response = [];
            $response['id'] = $syllabus->id;
            $response['name'] = $syllabus->name;
            $response['is_profession'] = $syllabus->is_profession;
            $response['syllabus_type_id'] = $syllabus->syllabus_type_id;
            $response['name_en'] = $syllabus->name_en;
            $response['code'] = $syllabus->code;
            $response['semester'] = $syllabus->semester->name;
            $response['credits'] = $syllabus->credits;
            $response['subjectType'] = $syllabus->status->name_ka;
            $response['subjectTypeEn'] = $syllabus->status->name_en;
            $response['totalHours'] = $syllabus->total_hours;

            $response['lecturers'] = (new SyllabusService())->setLecturers(
                $syllabus->id
            )['lecturers'];
            $response['lectureNumber'] = $previousLecture?->lecture_number ?? 'N/A';
            $response['lectureType'] = $previousLecture?->is_lecture ? ($previousLecture->is_lecture ? 'ლექცია' : 'სემინარი') : 'N/a';
            $response['lectureTypeEn'] = $previousLecture?->is_lecture ? ($previousLecture->is_lecture ? 'Lecture' : 'Seminar') : 'N/a';
            /**
             *  calculate total by is_profession
             */
            $response['total'] = round(StudentAssignment::where('student_id', $studentId)
                ->where('syllabus_id', $syllabus->id)->sum('point'),2);
            if($syllabus->is_profession)
            {
                $response['total'] = StudentAssignment::query()
                        ->with('lastCheckout')
                        ->where('student_id', $studentId)
                        ->where('syllabus_id', $syllabus->id)
                        ->first()
                        ?->lastCheckout
                        ->where('is_active', 1)
                        ->whereIn('status_id', [1,4]) // დადასტურება/გადაბარება
                        ->count()
                    > 0
                ;
            }
            $lectureIds = Lecture::whereSyllabusId($syllabus->id)
                ->pluck('id')->toArray();
            $response['missedLectures'] = StudentAttendance::whereIn('lecture_id', $lectureIds)
                ->where('student_id', $studentId)
                ->where('is_present', 0)->count();
            $response['missedLecturesInPercent'] = number_format((
                    $response['missedLectures'] / ($syllabus->lecture_hours + $syllabus->seminar_hours + $syllabus->mid_and_final_exam_hours)
                ) * 100, 2);
            $response['assignments'] = $syllabusService->studentAssignments(
                syllabusId: $syllabus->id,
                studentId: $studentId
            );
            $headings = $syllabusService->subjectHeadings(
                syllabus: $syllabus
            );
            //TODO: დავაბრუნე ძველზე რადგან გაცდენების გრაფა აღარ მოყვებოდა
            unset($headings[0], $headings[1], $headings[2]);
            $response['headings'] = $headings;

            $response['survey'] = SurveyActivation::query()
                ->with('lecturer')
                ->where('user_id', $student->user_id)
                ->where('syllabus_id', $syllabus->id)
                ->where('answered', 0)
                ->where('status', 1)
                ->get()
            ;
            $response['semester_id'] = Curriculum::where('syllabus_id', $syllabus->id)
                ->first()->flow_id;

            return $response;
        });
        if (!$semesterID || $semesterID === 'undefined' || !is_numeric($semesterID)) {
            $setting = Setting::where('key', 'current_semester')->first();
            if (!$setting) {
                return [
                    'code' => 400,
                    'message' => 'No current semester defined in settings!'
                ];
            }
            $current_semester = $setting->value;
        } else {
            $current_semester = $semesterID;
        }
        return [
            'code' => 200,
            'data' => $subjectList,
            'title' => Flow::whereId($current_semester)->first()->name
        ];
    }

    public function todayLectures(): array
    {
        $student = Student::whereUserId(auth()->id())->first();
        if (!$student) {
            return [
                'code' => 400,
                'message' => 'Student with the following user id doesnt exists!'
            ];
        }
        $studentId = $student->id;
        $studentLectures = $student->lectures()
            ->with([
                'syllabus:id,name,name_en,status_id,total_hours,credits' => [
                    'status:id,name_ka,name_en'
                ],
                'lecturer:id,first_name,last_name,photo',
                'auditorium:id,name'
            ])
            ->where('lecture_date', now()->format('Y-m-d'))
            ->get()->map(function ($lecture) {
                unset(
                    $lecture['payment_per_hour'],
                    $lecture['week_day'],
                    $lecture['created_at'],
                    $lecture['updated_at'],
                );
                $syllabusId = $lecture['syllabus_id'];
                $lecture['studentGroups'] = CurriculumLectureTime::whereLecturerId(
                    $lecture['lecturer_id']
                )->whereHas('curriculumLecture.curriculum', function ($query) use ($syllabusId) {
                    return $query->where('syllabus_id', $syllabusId);
                })->first()->studentGroups->pluck('name_ka','name_en') ?: [];

                return $lecture;
            });
        return ['data' => $studentLectures];
    }

    public function missedLectures(int $syllabusId, int $id = null): array
    {
        if($id) {
            $student = Student::whereId($id)->first();
        } else {
            $student = Student::whereUserId(auth()->id())
                ->first();
        }
        if (!$student) {
            return [
                'code' => 404,
                'message' => 'Student not found'
            ];
        }
        $studentId = $student->id;
        $studentAttandances = StudentAttendance::with(['lecture:id,lecture_number'])
            ->whereStudentId($studentId)
            ->whereHas('lecture', function ($query) use ($syllabusId) {
                return $query->where('syllabus_id', $syllabusId);
            })
            ->where('is_present', '=', 0)
            ->get()->map(function ($attendance) {
                $attendance['date'] = $attendance->created_at->format('Y-m-d');
                unset(
                    $attendance['created_at'],
                    $attendance['updated_at'],
                    $attendance['is_present']
                );
                return $attendance;
            });
        return [
            'code' => 200,
            'data' => $studentAttandances
        ];
    }

    public function passedSubjects(): array
    {
        $student = Student::whereUserId(auth()->id())
            ->first();
        if (!$student) {
            return [
                'code' => 404,
                'message' => 'Student not found'
            ];
        }
        $studentId = $student->id;
        $passedSyllabusIds = StudentSyllabusHistory::whereStudentId($studentId)
            ->where('is_passed', '=', 1)
            ->pluck('syllabus_id')
            ->toArray();
        $subjects = Syllabus::with(['status:id,name_ka,name_en', 'semester:id,name'])
            ->select(['id', 'name', 'name_en', 'credits', 'status_id', 'semester_id', 'total_hours'])
            ->findMany($passedSyllabusIds)
            ->map(function ($item) use ($studentId) {
                $syllabusService = new SyllabusService();
                $headings = $syllabusService->subjectHeadings(
                    syllabus: $item
                );
                unset($headings[0], $headings[1], $headings[2]);
                $item['headings'] = $headings;
                $item['assignments'] = $syllabusService->studentAssignments(
                    studentId: $studentId, syllabusId: $item->id
                );
                $item['lecturers'] = $syllabusService->setLecturers(
                    $item->id
                )['lecturers'];
                $item['total'] = StudentAssignment::where('student_id', $studentId)
                    ->where('syllabus_id', $item->id)->sum('point');
                unset($item['status_id']);
                return $item;
            })->groupBy('semester.name');
        return [
            'code' => 200,
            'data' => $subjects
        ];
    }

    public function weekSchedule(): array
    {
        $student = Student::whereUserId(auth()->id())->first();
        if (!$student) {
            return [
                'code' => 400,
                'message' => 'Student with the following user id doesnt exists!'
            ];
        }
        $studentId = $student->id;
        $studentLectures = $student->lectures()
            ->with([
                'syllabus:id,name,name_en,status_id,learn_year_id,semester_id' => [
                    'status:id,name_ka,name_en',
                    'semester:id,name',
                    'learnYear:id,program_id' => [
                        'program:id,name_ka,name_en'
                    ]
                ],
                'lecturer:id,first_name,last_name,photo',
                'auditorium:id,name'
            ])
            ->whereBetween('lecture_date', [
                now()->startOfWeek()->format('Y-m-d'),
                now()->endOfWeek()->format('Y-m-d')
            ])
            ->orderBy('week_day')
            ->get()->map(function ($lecture) {
                $syllabusService = new SyllabusService();
                unset(
                    $lecture['payment_per_hour'],
                    $lecture['created_at'],
                    $lecture['updated_at'],
                );
                $syllabusId = $lecture['syllabus_id'];
                $lecture['studentGroups'] = CurriculumLectureTime::whereLecturerId(
                    $lecture['lecturer_id']
                )->whereHas('curriculumLecture.curriculum', function ($query) use ($syllabusId) {
                    return $query->where('syllabus_id', $syllabusId);
                })->first()->studentGroups->pluck('name_ka', 'id');
                return $lecture;
            })->groupBy(function ($query) {
                $dateService = new DateService();
                return $dateService->getWeekDayName($query->week_day);
            });
        return [
            'code' => 200,
            'data' => $studentLectures
        ];
    }

    public function calculateGpa(int $studentId): float
    {
        $studentPassedSubjects = StudentSyllabusHistory::whereStudentId($studentId)
            ->where('is_passed', '=', 1)
            ->get();
        $gpaCalculator = [];
        foreach ($studentPassedSubjects as $studentPassedSubject) {
            $point = round($studentPassedSubject->point);
            $assessment = match (true) {
                $point >= 91 => [
                    'grade' => 'A',
                    'point' => 4
                ],
                $point >= 81 && $point < 91 => [
                    'grade' => 'B',
                    'point' => 3.2
                ],
                $point >= 71 && $point < 81 => [
                    'grade' => 'C',
                    'point' => 2.4
                ],
                $point >= 61 && $point < 71 => [
                    'grade' => 'D',
                    'point' => 1.6
                ],
                $point >= 51 && $point < 61 => [
                    'grade' => 'E',
                    'point' => 0.8
                ],
                $point < 51 => [
                    'grade' => 'F',
                    'point' => 0
                ]
            };
            $credits = Syllabus::select('id', 'credits')
                ->find($studentPassedSubject->syllabus_id)->credits;
            $gpaCalculator[] = $assessment['point'] * $credits;
        }
        $syllabusIds = $studentPassedSubjects->pluck('syllabus_id')->toArray();
        $totalCredits = Syllabus::whereIn('id', $syllabusIds)->sum('credits');
        return round(array_sum($gpaCalculator) / $totalCredits, 3);
    }

}
