<?php

namespace App\Services;

use App\Models\RegisterForms\Doctor\DoctorEducation;
use App\Models\RegisterForms\Doctor\DoctorRegisterInfo;
use App\Models\RegisterForms\Master\MasterRegisterInfo;

class DoctorRegisterService
{
    public function storeEducations($id, $data): void
    {
        $data['doctor_register_id'] = $id;
        DoctorEducation::create($data);
    }

    public function storeInfo($id, $data): void
    {
        $data['doctor_register_id'] = $id;
        DoctorRegisterInfo::create($data);
    }
}
