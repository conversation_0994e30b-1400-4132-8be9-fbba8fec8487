<?php

namespace App\Services\Syllabus;

use App\Filters\Syllabus\SyllabusFilter;
use App\Models\Assignment;
use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Curriculum\CurriculumStudentGroup;
use App\Models\Curriculum\LectureStudent;
use App\Models\Lectures\Lecture;
use App\Models\Lectures\StudentAttendance;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Severity;
use App\Models\Syllabus\LecturerContactTime;
use App\Models\Syllabus\Syllabus;
use App\Models\SyllabusStudentGuest;
use App\Models\User\User;
use App\Models\User\UserType;
use App\Models\Week;
use App\Services\NotificationService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyllabusService
{

    private Collection $syllabusStudents;

    private array $events = [
        'excel'
    ];

    protected $subject;

    protected array $rows;

    public function __construct()
    {
        $this->syllabusStudents = collect([]);
        $this->subject = collect([]);
        $this->rows = [
            [
                'name_ka' => 'N',
                'name_en' => 'N',
                'column_name' => 'n',
                'rowSpan' => 2
            ],
            [
                'name_ka' => 'სტუდენტი',
                'name_en' => 'Student',
                'column_name' => 'student',
                'rowSpan' => 2
            ],
            [
                'name_ka' => 'ჯამი',
                'name_en' => 'Total',
                'column_name' => 'total',
                'rowSpan' => 2
            ],
            [
                'name_ka' => 'გაცდენა',
                'name_en' => 'Missed lectures',
                'column_name' => 'missedLectures',
                'rowSpan' => 2
            ],
        ];
    }

    public function StoreLecturerContactHours($syllabus_id, $lecturer)
    {
        $contact = new LecturerContactTime;
        $contact->lecturer_id = $lecturer['lecturer_id'];
        $contact->syllabus_id = $syllabus_id;
        $contact->week_day = $lecturer['week_day'];
        $contact->start_time = $lecturer['start_time'];
        $contact->end_time = $lecturer['end_time'];
        $contact->save();
    }

    public function deleteLecturerContactHours($syllabus_id)
    {
        $existingContacts = LecturerContactTime::where('syllabus_id', $syllabus_id)->get();
        if ($existingContacts->isNotEmpty()) {
            foreach ($existingContacts as $existingContact) {
                $existingContact->delete();
            }
        }
    }

    /**
     * @param $syllabus_id
     * @param $week
     * @return void
     */

    public function StoreWeek($syllabus_id, $week)
    {
        $wk = Week::where('syllabus_id', $syllabus_id)
            ->where('number', $week['number'])
            ->first();

        if (!$wk) {
            $wk = new Week;
            $wk->syllabus_id = $syllabus_id;
            $wk->number = $week['number'];
        }

        $wk->title = $week['title'];
        $wk->title_en = $week['title_en'] ?? '';
        $wk->main_literature = $week['main_literature'];
        $wk->main_literature_en = $week['main_literature_en'] ?? '';
        $wk->secondary_literature = $week['secondary_literature'] ?? '';
        $wk->secondary_literature_en = $week['secondary_literature_en'] ?? '';
        $wk->save();
    }

    /**
     *
     * @param $syllabus_id
     * @param $exam
     * @return Assignment|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function StoreExam($syllabus_id, $exam)
    {

        $assignment = new Assignment;
        $assignment->syllabus_id = $syllabus_id;
        $assignment->assessment_component_id = $exam['id'];
        $assignment->parent_id = $exam['parent_id'] ?? 0;
        $assignment->calculation_type = $exam['calculation_type'] ?? 0;
        $assignment->score = $exam['score'] ?? 1;
        $assignment->min_score = $exam['min_score'] ?? 0;
        $assignment->description = $exam['description'] ?? '';
        $assignment->description_en = $exam['description_en'] ?? '';
        $assignment->save();
        return $assignment;
    }

    public function setSyllabusStudents(Collection $syllabusStudents): void
    {
        $this->syllabusStudents = $syllabusStudents;
    }

    /**
     * @return Collection
     */

    public function getSubject(): Collection
    {
        return $this->subject;
    }

    /**
     * @param Syllabus $syllabus
     * @param Date|null $lectureDate
     * @return Collection
     */

    public function syllabusStudents(
        Syllabus    $syllabus,
        string|null $lectureDate,
        string|null $studentGroup = null,
        string|null $lecturer = null
    )
    {
        $curriculumId = Curriculum::whereSyllabusId($syllabus->id)->first()->id;
        $lecturesCount = CurriculumLecture::whereCurriculumId($curriculumId)
            ->first()?->lectures_count ?? 0;
        $lectureId = Lecture::whereSyllabusId($syllabus->id)
            ->whereDate('lecture_date', '=',
                $lectureDate ?? now()->format('Y-m-d'))
            ->OrderBy('id')
            ->first()?->id;
        $studentGroups = StudentGroup::findMany(self::setStudentGroups($curriculumId));
        $syllabus['studentGroups'] = $studentGroups->map(function ($studentGroup) {
            return [
                'id' => $studentGroup->id,
                'name' => $studentGroup->name_ka
            ];
        })->all();
        $subject = [
            'subject' => $syllabus->name,
            'syllabusId' => $syllabus->id,
            'hours' => $syllabus->lecture_hours + $syllabus->seminar_hours + $syllabus->mid_and_final_exam_hours,
            'is_profession' => $syllabus->is_profession,
            'academic_degree_id' => $syllabus->academic_degree_id,
            'course' => $syllabus->semester->name,
            'current_semester' => $syllabus->curriculum->flow->name,
            'current_semester_id' => $syllabus->curriculum->flow->id,
            'is_lecture' => !is_null($lectureId),
            'studentGroups' => $syllabus['studentGroups'],
            'lecturers' => self::setLecturers($syllabus->id)['lecturers'],
            'lectureDates' => self::setLectureDates($syllabus->id),
            'headings' => self::subjectHeadings($syllabus, $lecturesCount, isLecture: !is_null($lectureId)),
        ];
        if (isset($lectureId)) {
            $subject['lectureId'] = $lectureId;
            /**
             * Duration of the lectures in hours.
             */
            $subject['duration_in_hours'] = $lecturesCount;
        }
        $this->subject = collect($subject);
        $syllabusStudents = $syllabus->students->load('student:id,name,surname,group_id,photo');
        /**
         * If searching student attendances and points by student group
         */
        if (!is_null($studentGroup)) {
            $syllabusStudents = $syllabusStudents->filter(function ($item) use ($studentGroup) {
                $guest = SyllabusStudentGuest::where('student_id', $item->student_id)
                    ->where('syllabus_id', $item->syllabus_id)
//                    ->where('student_group_id', $studentGroup)
                    ->first();

                if ($guest) {
                    return $guest->student_group_id == $studentGroup;
                }

                return $item->student->group_id == $studentGroup;
            });
        }

        $syllabusStudents = $syllabusStudents->values();
        /**
         * If searching student attendances and points by lecturer
         */
        if (!is_null($lecturer)) {
            $curriculumLectureTimeIds = CurriculumLectureTime::whereHas('curriculumLecture', function ($item) use ($curriculumId) {
                return $item->where('curriculum_id', $curriculumId);
            })->where('lecturer_id', $lecturer)->pluck('id')->toArray();
            $studentGroupIds = CurriculumStudentGroup::whereIn('curriculum_lecture_time_id', $curriculumLectureTimeIds)
                ->distinct('student_group_id')
                ->pluck('student_group_id')
                ->toArray();
            $studentIds = Student::whereIn('group_id', $studentGroupIds)->pluck('id')->toArray();
            $studentIds = [
                ...$studentIds,
                SyllabusStudentGuest::where('syllabus_id', $syllabus->id)
                    ->whereIn('student_group_id', $studentGroupIds)
                    ->pluck('student_id')
                    ->toArray()
            ];
            $syllabusStudents = $syllabusStudents->whereIn('student_id', $studentIds);
        }
        $syllabusStudents = $syllabusStudents->values();
        /**
         * If not searching students by student group and lecturer and today is lecture...
         */
        if (
            (is_null($studentGroup) && is_null($lecturer) && auth()->user()->user_type_id != UserType::ADMINISTRATION)
            &&
            !is_null($lectureId)
        ) {
            $studentIds = Lecture::with('students')
                ->find($lectureId)->students()->pluck('student_id')->toArray();
            $syllabusId = $syllabus->id;
            $lecture = Lecture::find($lectureId);
            $curriculumLectureTimeId = CurriculumLectureTime::query()
                ->where(function ($query)  use ($lecture, $syllabusId) {
                    $query->whereHas('curriculumLecture.curriculum', function ($query) use ($syllabusId) {
                        return $query->where('syllabus_id', $syllabusId);
                    })
                    ->where('lecturer_id', $lecture->lecturer_id);
                    })
                ->orWhere('week_day', $lecture->week_day)
                ->orWhere('start_time', $lecture->start_time)
                ->orWhere('end_time', $lecture->end_time)
                ->orWhere('auditorium_id', $lecture->auditorium_id)
                ->first()->id ?? null;
            $curriculumLectureStudentGroupIds = CurriculumStudentGroup::where('curriculum_lecture_time_id', $curriculumLectureTimeId)
                ->pluck('student_group_id')->toArray();
            $studentIds = [
                ...$studentIds,
                SyllabusStudentGuest::where('syllabus_id', $syllabusId)
                    ->whereIn('student_group_id', $curriculumLectureStudentGroupIds)
                    ->pluck('student_id')->toArray()
            ];
            $syllabusStudents = $syllabusStudents->whereIn('student_id', $studentIds)
                ->load('student:id,name,surname,group_id,photo');
        }
        $this->syllabusStudents = $syllabusStudents->map(function ($item, $key) use ($lectureDate, $lecturesCount, $syllabus) {
            $result = [];
            $result['n'] = $key + 1;
            $syllabus = $item->syllabus;
            $syllabusId = $item->syllabus_id;
            $studentId = $item->student_id;
            $lectureId = Lecture::whereLectureDate($lectureDate ?? now()->format('Y-m-d'))
                ->whereSyllabusId($syllabusId)
                ->pluck('id')->toArray();
            /**
             * If today is lecture or filtered by lecture date , set attendance columns.
             */
            if (!empty($lectureId)) {
                foreach (range(1, $lecturesCount) as $lectureIdd) {
                    $lectureN = $lectureIdd;
                    $result["attendance$lectureN"] = StudentAttendance
                        ::whereStudentId($studentId)
                        ->whereIn('lecture_id', $lectureId)
                        ->whereNthLecture($lectureIdd)
                        ->exists();
                }
            }
            $result['studentId'] = $studentId;
            $result['student'] = ($item->student->surname ?? '') . ' ' . ($item->student->name ?? '');
            $result['studentPhoto'] = $item->student->photo ?? '';
            $result['total'] = round(StudentAssignment::where('student_id', $studentId)
                ->where('syllabus_id', $syllabusId)->sum('point'),2);
            $lectureIds = Lecture::whereSyllabusId($syllabusId)
                ->pluck('id')->toArray();
            $result['missedLectures'] = StudentAttendance::whereIn('lecture_id', $lectureIds)
                ->where('student_id', $studentId)
                ->where('is_present', 0)->count();

            $totalHours = $syllabus->lecture_hours + $syllabus->seminar_hours + $syllabus->mid_and_final_exam_hours;

            if ($totalHours > 0) {
                $result['missedLecturesInPercent'] = number_format(($result['missedLectures'] / $totalHours) * 100, 2);
            } else {
                $result['missedLecturesInPercent'] = 0;
            }

            /**
             * override @var $result['total']
             */
            if($syllabus->is_profession)
            {
                $studentAssignment = StudentAssignment::query()
                    ->with(['lastCheckout' => function ($lastCheckout){
                        return $lastCheckout->where('is_active', 1);
                    }])
                    ->where('syllabus_id', $syllabus->id)
                    ->where('student_id', $studentId)
                    ->get()
                    ->map(function ($item) {
                        return $item->lastCheckout
                            ->sortByDesc('created_at')
                            ->first()
                            ?->status_id;
                    })
                ;

                $result['total'] = !($result['missedLecturesInPercent'] > 33.33) && !$studentAssignment->contains(function ($item) {
                        return $item == 2 or $item == 3 or $item == null;
                    });

            }

            $result['assignments'] = $this->studentAssignments($syllabusId, $studentId);
            return $result;
        })->sortBy(function ($item) {
            return $item['student'];
        })->values();
        $this->subject = $this->subject->put('students', $this->syllabusStudents->toArray());
        return $this->subject;
    }

    public function getStatusName($status): array
    {
        $name_ka = '';
        $name_en = '';
        switch ($status) {
            case 1: $name_ka = 'დადასტურება'; $name_en = 'Passed'; break;
            case 2: $name_ka = 'არ გამოცხადდა'; $name_en = 'No attendance'; break;
            case 3: $name_ka = 'ვერ დაადასტურა'; $name_en = 'Not Passed'; break;
            case 4: $name_ka = 'გადაბარება'; $name_en = 'Re - Passed'; break;
        }
        return [$name_ka, $name_en];
    }

    /**
     * @param int $syllabusId
     * @param int $studentId
     * @return array
     */
    public
    function studentAssignments(int $syllabusId, int $studentId): array
    {
        $result = [];
        $syllabusAssignments = Assignment::with('assessmentComponent')
            ->whereSyllabusId($syllabusId);
        $checkedAssignments = collect([]);
        foreach ($syllabusAssignments->get()->toArray() as $syllabusAssignment) {
            if ($checkedAssignments->contains($syllabusAssignment['id'])) {
                continue;
            }
            if ((int)$syllabusAssignment['parent_id'] === 0) {
                $childAssignments = Assignment::with('assessmentComponent')
                    ->where('syllabus_id', $syllabusId)
                    ->where('parent_id', $syllabusAssignment['assessment_component_id']);
                if ($childAssignments->count() === 0) {
                    $data = StudentAssignment::select(
                        'id',
                        'point as takenScore',
                        'date as takenScoreDate'
                    )
                        ->where('assignment_id', $syllabusAssignment['id'])
                        ->where('syllabus_id', $syllabusId)
                        ->where('student_id', $studentId)
                        ->first();
                    $takenScore = $data ? $data->takenScore : 0;
                    $takenScoreDate = $data ? $data->takenScoreDate : 0;
//                    switch ($data?->lastCheckout->sortByDesc('id')->first()?->status_id) {
//                        case 1: $name_ka = 'დადასტურება'; $name_en = 'Passed'; break;
//                        case 2: $name_ka = 'არ გამოცხადდა'; $name_en = 'No attendance'; break;
//                        case 3: $name_ka = 'ვერ დაადასტურა'; $name_en = 'Not Passed'; break;
//                        case 4: $name_ka = 'გადაბარება'; $name_en = 'Re - Passed'; break;
//                    }
                    $statusNames = $this->getStatusName($data?->lastCheckout->sortByDesc('id')->first()?->status_id);
                    $result[] = [
                        'assignmentId' => $syllabusAssignment['id'],
                        'titleGeo' => $syllabusAssignment['assessment_component']['name_ka'],
                        'titleEn' => $syllabusAssignment['assessment_component']['name_en'],
                        'score' => $syllabusAssignment['score'],
                        'hasChild' => false,
                        'minScore' => $syllabusAssignment['min_score'],
                        'takenScore' => $takenScore,
                        'takenScoreDate' => $takenScoreDate,
                        'lastCheckout' => [
                            [
                                'id' => 1,
                                'name_ka' => 'დადასტურება',
                                'name_en' => 'Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 1)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 2,
                                'name_ka' => 'არ გამოცხადდა',
                                'name_en' => 'No attendance',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 2)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 3,
                                'name_ka' => 'ვერ დაადასტურა',
                                'name_en' => 'Not Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 3)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 4,
                                'name_ka' => 'გადაბარება',
                                'name_en' => 'Re - Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 4)->sortByDesc('id')->first()?->created_at,
                            ]
                        ],
                        'lastCheckoutFinal' => [
                            [
                                'id' => $data?->lastCheckout->sortByDesc('id')->first()?->status_id,
                                'name_ka' => $statusNames[0],
                                'name_en' => $statusNames[1],
                                'date' => $data?->lastCheckout->sortByDesc('id')->first()?->created_at,
                            ]
                        ]
                    ];
                } else {
                    $checkedAssignments->push(...$childAssignments->pluck('id')->toArray());
                    $childAssignmentsList = $childAssignments->get()->map(function ($assignment) use ($syllabusId, $studentId, $childAssignments): array {
                        $result = [];
                        $data = StudentAssignment::select(
                            'id',
                            'point as takenScore',
                            'date as takenScoreDate'
                        )
                            ->where('assignment_id', $assignment->id)
                            ->where('syllabus_id', $syllabusId)
                            ->where('student_id', $studentId)
                            ->first();
                        $takenScore = $data ? $data->takenScore : 0;
                        $takenScoreDate = $data ? $data->takenScoreDate : 0;
                        $result['assignmentId'] = $assignment->id;
                        $result['titleGeo'] = $assignment->assessmentComponent->name_ka;
                        $result['titleEn'] = $assignment->assessmentComponent->name_en;
                        $result['score'] = $assignment->score;
                        $result['minScore'] = $assignment->min_score;
                        $result['takenScoreDate'] = $takenScoreDate;
                        $result['takenScore'] = $takenScore;
                        $result['lastCheckout'] = [
                            [
                                'id' => 1,
                                'name_ka' => 'დადასტურება',
                                'name_en' => 'Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 1)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 2,
                                'name_ka' => 'არ გამოცხადდა',
                                'name_en' => 'No attendance',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 2)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 3,
                                'name_ka' => 'ვერ დაადასტურა',
                                'name_en' => 'Not Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 3)->sortByDesc('id')->first()?->created_at,
                            ],[
                                'id' => 4,
                                'name_ka' => 'გადაბარება',
                                'name_en' => 'Re - Passed',
                                'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 4)->sortByDesc('id')->first()?->created_at,
                            ]
                        ];
                        $lastCheckoutFinal = $data?->lastCheckout->sortByDesc('id')->first();

                        $statusNames = $this->getStatusName($lastCheckoutFinal?->status_id);
                        $result['lastCheckoutFinal'] = [
                            [
                                'id' => $lastCheckoutFinal?->status_id,
                                'name_ka' => $statusNames[0],
                                'name_en' => $statusNames[1],
                                'date' => $lastCheckoutFinal?->created_at,
                            ]
                        ];
                        return $result;
                    })->toArray();
                    $result[] = [
                        'id' => $syllabusAssignment['id'],
                        'parentId' => $syllabusAssignment['id'],
                        'isParent' => true,
                        'titleGeo' => $syllabusAssignment['assessment_component']['name_ka'],
                        'titleEn' => $syllabusAssignment['assessment_component']['name_en'],
                        'score' => $syllabusAssignment['score'],
                        'minScore' => $syllabusAssignment['min_score'],
                        'hasChild' => true,
                        'childAssignments' => $childAssignmentsList
                    ];
                }
            } else {
                $childAssignments = Assignment::whereSyllabusId($syllabusId)->where('parent_id', $syllabusAssignment['parent_id']);
                $childAssignmentsList = $childAssignments->get()->map(function ($assignment) use ($syllabusId, $studentId, $childAssignments): array {
                    $data = StudentAssignment::select(
                        'id',
                        'point as takenScore',
                        'date as takenScoreDate',
                    )
                        ->where('assignment_id', $assignment['id'])
                        ->where('syllabus_id', $syllabusId)
                        ->where('student_id', $studentId)
                        ->first();
                    $takenScore = $data ? $data->takenScore : 0;
                    $takenScoreDate = $data ? $data->takenScoreDate : 0;
                    $result = [];
                    $result['titleGeo'] = $assignment->assessmentComponent->name_ka;
                    $result['titleEn'] = $assignment->assessmentComponent->name_en;
                    $result['score'] = $assignment->score;
                    $result['minScore'] = $assignment->min_score;
                    $result['takenScoreDate'] = $takenScoreDate;
                    $result['takenScore'] = $takenScore;
                    $result['lastCheckout'] = [
                        [
                            'id' => 1,
                            'name_ka' => 'დადასტურება',
                            'name_en' => 'Passed',
                            'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 1)->sortByDesc('id')->first()?->created_at,
                        ],[
                            'id' => 2,
                            'name_ka' => 'არ გამოცხადდა',
                            'name_en' => 'No attendance',
                            'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 2)->sortByDesc('id')->first()?->created_at,
                        ],[
                            'id' => 3,
                            'name_ka' => 'ვერ დაადასტურა',
                            'name_en' => 'Not Passed',
                            'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 3)->sortByDesc('id')->first()?->created_at,
                        ],[
                            'id' => 4,
                            'name_ka' => 'გადაბარება',
                            'name_en' => 'Re - Passed',
                            'date' => $data?->lastCheckout->where('is_active', 1)->where('status_id', 4)->sortByDesc('id')->first()?->created_at,
                        ]
                    ];
                    $lastCheckoutFinal = $data?->lastCheckout->sortByDesc('id')->first();
                    $statusNames = $this->getStatusName($lastCheckoutFinal?->status_id);
                    $result['lastCheckoutFinal'] = [
                        [
                            'id' => $lastCheckoutFinal?->status_id,
                            'name_ka' => $statusNames[0],
                            'name_en' => $statusNames[1],
                            'date' => $lastCheckoutFinal?->created_at,
                        ]
                    ];
                    return $result;
                })->toArray();
                $parentAssignment = Assignment::whereSyllabusId($syllabusId)->where('assessment_component_id', '=', $syllabusAssignment['parent_id'])
                    ->first();
                $result[] = [
                    'id' => $syllabusAssignment['id'],
                    'parentId' => $parentAssignment->id ?? 0,
                    'isParent' => true,
                    'titleGeo' => $parentAssignment?->assessmentComponent?->name_ka ?? 0,
                    'titleEn' => $parentAssignment?->assessmentComponent?->name_en ?? 0,
                    'score' => $parentAssignment?->assessmentComponent?->score ?? 0,
                    'hasChild' => true,
                    'minScore' => $parentAssignment?->assessmentComponent?->min_score ?? 0,
                    'childAssignments' => $childAssignmentsList
                ];
                $checkedAssignments->push(...$childAssignments->pluck('id')->toArray());
                $checkedAssignments->push($parentAssignment?->id);
            }
        }
        return $result;
    }

    /**
     * @param Syllabus $syllabus
     * @param int $lecturesCount
     * @param string $listener
     * @return array
     * @depends if listener is presented , set up heading rows for excel and etc...
     */
    public
    function subjectHeadings(
        Syllabus $syllabus,
        int      $lecturesCount = 0,
        string   $listener = '',
        bool     $isLecture = false
    )
    {
        $assignments = $syllabus->assignments
            ->load('assignments')
            ->loadCount('assignments')
            ->toArray();
        $childRows = [];
        $parentRows = [];
        $serviceRows = [];
        if ($syllabus->is_profession === 0) {
            foreach ($assignments as $assignment) {
                $childrenCount = Assignment::whereSyllabusId($syllabus->id)
                    ->where('parent_id', $assignment['assessment_component_id'])
                    ->count();
                if ($assignment['parent_id'] != 0) {
                    $childRows[] = [
                        "name_ka" => $assignment['assessment_component']['name_ka'],
                        "name_en" => $assignment['assessment_component']['name_en'],
                        "score" => $assignment['score'],
                        "minScore" => $assignment['min_score'],
                        "colSpan" => 2,
                        "isChild" => true
                    ];
                    $serviceRows[] = [
                        "name_ka" => $assignment['assessment_component']['name_ka'],
                        "id" => $assignment['id']
                    ];
                } else {
                    $childrenCount = Assignment::whereSyllabusId($syllabus->id)
                        ->where('parent_id', $assignment['assessment_component_id'])
                        ->count();
                    if ($childrenCount > 0) {
                        if (!in_array($listener, $this->events)) {
                            $parentRows[] = [
                                "name_ka" => $assignment['assessment_component']['name_ka'],
                                "name_en" => $assignment['assessment_component']['name_en'],
                                "score" => $assignment['score'],
                                "minScore" => $assignment['min_score'],
                                "colSpan" => 2 * $childrenCount,
                                "isChild" => false
                            ];
                            $parentRows[] = [
                                'name_ka' => 'ჯამი',
                                'name_en' => 'Total',
                                'rowSpan' => 2
                            ];
                        }
                    } else {
                        $serviceRows[] = [
                            "name_ka" => $assignment['assessment_component']['name_ka'],
                            "id" => $assignment['id']
                        ];
                        $parentRows[] = [
                            "name_ka" => $assignment['assessment_component']['name_ka'],
                            "name_en" => $assignment['assessment_component']['name_en'],
                            "score" => $assignment['score'],
                            "minScore" => $assignment['min_score'],
                            "colSpan" => 2,
                            "rowSpan" => 2,
                            "isChild" => false
                        ];
                    }
                }
            }
        } else {
            foreach ($assignments as $assignment) {
                $parentRows[] = [
                    "name_ka" => $assignment['assessment_component']['name_ka'],
                    "name_en" => $assignment['assessment_component']['name_en'],
                    "score" => $assignment['score'],
                    "minScore" => $assignment['min_score'],
                    "colSpan" => 2,
                    "rowSpan" => 2,
                    "isChild" => false
                ];
            }
        }

        $lectureRows = [];
        if ($lecturesCount > 0 && !in_array($listener, $this->events) && $isLecture) {
            foreach (range(0, $lecturesCount - 1) as $item) {
                $item++;
                $lectureRows[] = [
                    'name_ka' => "დასწრება $item",
                    'name_en' => "Attendance $item",
                    'column_name' => "attendance$item",
                    'rowSpan' => 2
                ];
            }
        }
        $this->rows = [...$this->rows, ...$lectureRows, ...$parentRows, ...$childRows];
        return in_array($listener, $this->events) ? $serviceRows : $this->rows;
    }

    public
    function setStudentGroups(int $curriculumId, $init = false): array
    {
        $curriculumLectureIds = CurriculumLectureTime::whereHas('curriculumLecture', function ($item) use ($curriculumId) {
            return $item->where('curriculum_id', $curriculumId);
        })->pluck('id');
        $studentGroups = implode(',', CurriculumStudentGroup::whereIn('curriculum_lecture_time_id', $curriculumLectureIds)
            ->distinct('student_group_id')
            ->get()
            ->pluck('student_group_id')
            ->toArray()
        );
        if($init)
        {
            $studentGroups = StudentGroup::whereIn('id', explode(',', $studentGroups))
                ->get()
                ->pluck('name_ka')
                ->toArray();
        } else {
            $studentGroups = StudentGroup::whereIn('id', explode(',', $studentGroups))
                ->get()
                ->pluck('id')
                ->toArray();
        }

        return $studentGroups;
    }

    public
    function setLecturers(int $syllabusId): array
    {
        $curriculumId = Curriculum::whereSyllabusId($syllabusId)->first()->id;
        $curriculumLectureIds = CurriculumLectureTime::whereHas('curriculumLecture', function ($item) use ($curriculumId) {
            return $item->where('curriculum_id', $curriculumId);
        })->distinct('lecturer_id')->pluck('lecturer_id');
        $lecturers = Lecturer::select(['id', 'first_name', 'last_name', 'photo'])
            ->findMany($curriculumLectureIds)->toArray();
        return [
            'lecturer_names' => array_reduce($lecturers, function ($carry, $item) {
                $firstName = $item['first_name'];
                $lastName = $item['last_name'];
                if ($carry == '') {
                    return $carry . "$firstName $lastName";
                }
                return $carry . ",$firstName $lastName";
            }, ''),
            'lecturers' => $lecturers
        ];

    }

    public function checkExpiration(Assignment $assignment)
    {
        if (isset($assignment->expiration_date)) {
            if (Date::createFromFormat('Y-m-d H:i:s', $assignment->expiration_Date)
                < now()->format('Y-m-d H:i:s')) {
                return [
                    'message' => 'ქულის დაწერა აღარ არის შესაძლებელი, შეფასების კრიტერიუმი დახურულია!',
                    'status' => 403
                ];
            }
        }
    }

    public function setStudentMark(
        int   $syllabusId,
        int   $assignmentId,
        float $point,
        int   $studentId,
        bool  $isPercent,
              $lastCheckoutStatusId = null
    ): array
    {
        try {
            DB::beginTransaction();

            if (auth()->user()->user_type_id == UserType::STUDENT) {
                return [
                    'message' => 'Forbidden',
                    'status' => 403
                ];
            }
//            if (Student::query()->find($studentId)->status_id == StudentStatusList::FINANCE) {
//                return [
//                    'message' => 'სტუდენტს სტატუსი აქვს შეჩერებული, ქულის შეტანა ვერ მოხერხდა!',
//                    'status' => 403
//                ];
//            }

            $curriculumTimeExpired = Curriculum::query()->where('syllabus_id', $syllabusId)->first()?->end_date < today();
            if ($curriculumTimeExpired && auth()->user()->user_type_id == UserType::LECTURER) {
                return [
                    'message' => 'საგანი დახურულია!',
                    'status' => 403
                ];
            }

            $assignment = Assignment::find($assignmentId);
            $this->checkExpiration($assignment);
            if (!$this->studentExistInSyllabus($studentId, $syllabusId)) {
                return [
                    'message' => 'სტუდენტი ამ საგანზე არ მოიძებნა',
                    'status' => 404
                ];
            }

            if ($isPercent) {
                $point = Assignment::find($assignmentId)->score * $point / 100;
            }

            // Fetch or create the student assignment record
            $studentAssignment = StudentAssignment::where([
                'syllabus_id' => $syllabusId,
                'assignment_id' => $assignmentId,
                'student_id' => $studentId,
            ])->first();

            if ($studentAssignment) {
                $studentAssignment->update([
                    'point' => $point,
                    'date' => now()->format('Y-m-d H:i:s'),
                ]);
            } else {
                $cacheQuery = "temp-setStudentMark-$syllabusId-$assignmentId-$studentId";
                $studentAssignment = Cache::lock($cacheQuery, 10)->block(3, function () use ($syllabusId, $assignmentId, $studentId, $point) {
                    return StudentAssignment::updateOrCreate([
                        'syllabus_id' => $syllabusId,
                        'assignment_id' => $assignmentId,
                        'student_id' => $studentId,
                    ], [
                        'point' => $point,
                        'date' => now()->format('Y-m-d H:i:s'),
                    ]);
                });
            }

            // Now $studentAssignment is guaranteed to be an object, handle lastCheckout
            if ($studentAssignment->lastCheckout()->exists()) {
                $activeCheckout = $studentAssignment->lastCheckout()->where('is_active', 1)->first();
                if ($activeCheckout && $activeCheckout->status_id == $lastCheckoutStatusId) {
                    $activeCheckout->delete();
                } else {
                    $studentAssignment->lastCheckout()->create([
                        'student_assignment_id' => $studentAssignment->id,
                        'status_id' => $lastCheckoutStatusId,
                        'is_active' => true
                    ]);
                }
            } elseif ($lastCheckoutStatusId) {
                $studentAssignment->lastCheckout()->create([
                    'student_assignment_id' => $studentAssignment->id,
                    'status_id' => $lastCheckoutStatusId,
                    'is_active' => true
                ]);
            }

            $syllabus = Syllabus::select('id', 'name')->find($syllabusId);
            $assignment = Assignment::select('id', 'assessment_component_id')
                ->with('assessmentComponent')
                ->find($assignmentId);

            if ($curriculumTimeExpired) {
                $assignmentSum = StudentAssignment::query()
                    ->where('syllabus_id', $syllabusId)
                    ->where('student_id', $studentId)
                    ->sum('point');
                StudentSyllabusHistory::query()->updateOrCreate([
                    'syllabus_id' => $syllabusId,
                    'student_id' => $studentId,
                ], [
                    'point' => $assignmentSum,
                    'is_passed' => round($assignmentSum) >= 51
                ]);
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
            \Illuminate\Support\Facades\Log::error('Error in setStudentMark: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString(),
                'syllabusId' => $syllabusId,
                'assignmentId' => $assignmentId,
                'studentId' => $studentId,
                'point' => $point,
                'isPercent' => $isPercent,
                'lastCheckoutStatusId' => $lastCheckoutStatusId,
            ]);
            return [
                'message' => 'internal server error',
                'status' => 500
            ];
        }

        return [
            'message' => 'ქულა წარმატებით დაიწერა!',
            'status' => 201
        ];
    }

    public
    function setStudentMarkOld(
        int   $syllabusId,
        int   $assignmentId,
        float $point,
        int   $studentId,
        bool  $isPercent,
        $lastCheckoutStatusId=null
    ): array
    {
        try {
            DB::beginTransaction();

            if (auth()->user()->user_type_id == UserType::STUDENT) {
                return [
                    'message' => 'Forbidden',
                    'status' => 403
                ];
            }
            if (Student::query()->find($studentId)->status_id == StudentStatusList::FINANCE) {
                return [
                    'message' => 'სტუდენტს სტატუსი აქვს შეჩერებული, ქულის შეტანა ვერ მოხერხდა!',
                    'status' => 403
                ];
            }

            // check if date expired
            $curriculumTimeExpired = Curriculum::query()->where('syllabus_id', $syllabusId)->first()?->end_date < today();
            //$curriculumTimeExpired = false;
            if($curriculumTimeExpired)
            {
                if (auth()->user()->user_type_id == UserType::LECTURER)
                {
                    return [
                        'message' => 'საგანი დახურულია!',
                        'status' => 403
                    ];
                }
            }

            $assignment = Assignment::find($assignmentId);
            $this->checkExpiration($assignment);
            if (!$this->studentExistInSyllabus(
                $studentId,
                $syllabusId
            )) {
                return [
                    'message' => 'სტუდენტი ამ საგანზე არ მოიძებნა',
                    'status' => 404
                ];

            }

            if ($isPercent) {
                $point = Assignment::find($assignmentId)->score * $point / 100;
            }
            $studentAssignment = StudentAssignment::where([
                'syllabus_id' => $syllabusId,
                'assignment_id' => $assignmentId,
                'student_id' => $studentId,
            ])->first();

            if ($studentAssignment) {
                $studentAssignment->update([
                    'point' => $point,
                    'date' => now()->format('Y-m-d H:i:s'),
                ]);
            } else {
                $cacheQuery = "temp-setStudentMark-$syllabusId-$assignmentId-$studentId";
                Cache::lock($cacheQuery, 10)->block(3, function () use ($syllabusId, $assignmentId, $studentId, $point) {
                    StudentAssignment::updateOrCreate([
                        'syllabus_id' => $syllabusId,
                        'assignment_id' => $assignmentId,
                        'student_id' => $studentId,
                    ], [
                        'point' => $point,
                        'date' => now()->format('Y-m-d H:i:s'),
                    ]);
                });
            }

            if ($studentAssignment?->lastCheckout->isNotEmpty()) {
                if ($studentAssignment->lastCheckout->where('is_active', 1)->contains('status_id', $lastCheckoutStatusId)) {
                    $studentAssignment
                        ->lastCheckout()
                        ->where('is_active', 1)
                        ->orderByDesc('id')
                        ->first()
                        ?->delete()
                    ;
                } else {
                    $studentAssignment->lastCheckout()->create([
                        'student_assignment_id' => $studentAssignment->id,
                        'status_id' => $lastCheckoutStatusId,
                        'is_active' => true
                    ]);
                }
            } elseif ($lastCheckoutStatusId) {
                $studentAssignment->lastCheckout()->create([
                    'student_assignment_id' => $studentAssignment->id,
                    'status_id' => $lastCheckoutStatusId,
                    'is_active' => true
                ]);
            }

            $syllabus = Syllabus::select('id', 'name')->find($syllabusId);
            $assignment = Assignment::select('id', 'assessment_component_id')
                ->with('assessmentComponent')
                ->find($assignmentId);
    //        $notificationService = (new NotificationService());
    //        $notification = $notificationService->sendNotification(
    //            $userId,
    //            Severity::HIGH,
    //            "საგანში: {$syllabus->name} გამოცდაზე : {$assignment->assessmentComponent->name_ka}
    //            მიიღე : {$point} ქულა"
    //        );
    //        $notificationService->publish($notification);
            if ($curriculumTimeExpired)
            {
                $assignmentSum = StudentAssignment::query()
                    ->where('syllabus_id', $syllabusId)
                    ->where('student_id', $studentId)
                    ->sum('point')
                ;
                StudentSyllabusHistory::query()->updateOrCreate([
                    'syllabus_id' => $syllabusId,
                    'student_id' => $studentId,
                ],[
                    'point' => $assignmentSum,
                    'is_passed' => round($assignmentSum) >= 51
                ]);
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
            \Illuminate\Support\Facades\Log::error('Error in setStudentMark: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString(),
                'syllabusId' => $syllabusId,
                'assignmentId' => $assignmentId,
                'studentId' => $studentId,
                'point' => $point,
                'isPercent' => $isPercent,
                'lastCheckoutStatusId' => $lastCheckoutStatusId,
            ]);
            return [
                'message' => 'internal server error',
                'status' => 500
            ];
        }

        return [
            'message' => 'ქულა წარმატებით დაიწერა!',
            'status' => 201
        ];
    }

    public
    function setNotPresentedStudents(
        Lecture $lecture,
        int     $studentId,
        int     $nthLecture
    ): array
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        $userId = Student::select('id', 'user_id')->find($studentId)->user_id;
        if ($lecture->lecture_date > now()->format('Y-m-d')) {
            return [
                'message' => 'ლექცია არ მიმდინარეობს მოცემულ თარიღზე!',
                'status' => 403
            ];
        }
        if (!$this->studentExistInSyllabus(
            $studentId,
            $lecture->syllabus_id
        )) {
            return [
                'message' => 'სტუდენტი ამ ლექციაზე არ მოიძებნა!',
                'status' => 404
            ];
        }

        $attendance = StudentAttendance::where([
            'student_id' => $studentId,
            'lecture_id' => $lecture->id,
            'nth_lecture' => $nthLecture
        ])->first();

        if ($attendance) {
            if (
                auth()->user()->user_type_id == UserType::LECTURER
                and $attendance->cron_marker == 0
                and Carbon::parse($attendance->created_at)->isToday()
            ) {
                $attendance->delete();
                return [
                    'message' => 'სტუდენტის გაცდენა წაიშალა!',
                    'status' => 200
                ];
            }
            elseif(auth()->user()->user_type_id == UserType::ADMINISTRATION){
                $attendance->delete();
                return [
                    'message' => 'სტუდენტის გაცდენა წაიშალა!',
                    'status' => 200
                ];
            }

            return [
                'message' => 'წინა ლექციების გაცდენების წაშლის უფლება არ გაქვთ!',
                'status' => 200
            ];
//            $notificationService = new NotificationService();
//            $notification = $notificationService->sendNotification(
//                $userId,
//                Severity::MEDIUM,
//                "
//                {$lecture->lecture_number} ლექციაზე
//                 {$nthLecture} საათზე საგანში : {$lecture->syllabus->name}
//                  გაუქმდა არა.
//                  "
//            );
//            $notificationService->publish($notification);
        } else {
            $newAttendance = new StudentAttendance();
            $newAttendance->student_id = $studentId;
            $newAttendance->lecture_id = $lecture->id;
            $newAttendance->nth_lecture = $nthLecture;
            $newAttendance->is_present = 0;
            $newAttendance->syllabus_id = $lecture->syllabus->id;
            $newAttendance->save();
//            $notificationService = new NotificationService();
//            $notification = $notificationService->sendNotification(
//                $userId,
//                Severity::MEDIUM,
//                "
//                {$lecture->lecture_number} ლექციაზე
//                 {$nthLecture} საათზე საგანში : {$lecture->syllabus->name}
//                  დაგეწერა არა.
//                  "
//            );
//            $notificationService->publish($notification);
            return [
                'message' => 'გაცდენა, წარმატებით დაფიქსირდა!',
                'status' => 201
            ];
        }


//TODO: გაცდენის სტატუსი ვცვალოთ სამომავლოდ, ბაზიდან არ ამოვშალოთ

//        $attendance = StudentAttendance::firstOrNew(
//            [
//                'student_id' => $studentId,
//                'lecture_id' => $lecture->id,
//                'nth_lecture' => $nthLecture
//            ]
//        );
//
//        if (!$attendance->exists) {
//            $attendance->is_present = 0;
//            $attendance->save();
//            return [
//                'message' => 'დასწრება შეიქმნა',
//                'status' => 201
//            ];
//        } elseif ($attendance->is_present == 0) {
//            $attendance->is_present = 1;
//            $attendance->save();
//            return [
//                'message' => 'დასწრება განახლდა',
//                'status' => 200
//            ];
//        } elseif ($attendance->is_present == 1) {
//            $attendance->is_present = 0;
//            $attendance->save();
//            return [
//                'message' => 'დასწრება აღრიცხულია',
//                'status' => 200
//            ];
//        }

    }

    public
    function studentExistInSyllabus(
        int $studentId,
        int $syllabusId
    ): bool
    {
        if (!StudentSyllabusHistory::whereSyllabusId($syllabusId)
            ->where('student_id', $studentId)->exists()) {
            return false;
        }
        return true;
    }

    public function subjectList()
    {
        $syllabusFilter = new SyllabusFilter(request());
        $syllabuses = Syllabus::filter($syllabusFilter)
            ->has('curriculum.lecture')
            ->with('semester:id,name')
            ->select(['id', 'code', 'name', 'semester_id', 'is_profession', 'academic_degree_id', 'is_training', 'syllabus_type_id'])
            ->withCount('students')
            ->paginate(50);

        $syllabuses->getCollection()->transform(function ($syllabus) {
            $curriculumId = Curriculum::whereSyllabusId($syllabus->id)->first()->id;
            $syllabus['lecturers'] = $this->setLecturers($syllabus->id)['lecturers'];
            $studentGroupIds = $this->setStudentGroups($curriculumId);
            $studentGroups = StudentGroup::findMany($studentGroupIds);
            $syllabus['studentGroups'] = $studentGroups->map(function ($studentGroup) {
                return [
                    'id' => $studentGroup->id,
                    'name' => $studentGroup->name_ka
                ];
            })->all();
            unset($syllabus['semester_id']);
            return $syllabus;
        });

        return $syllabuses;
    }
//    public function subjectList(): array
//    {
//        $syllabusFilter = new SyllabusFilter();
//        return Syllabus::filter($syllabusFilter)
////            ->has('curriculum')
//            ->with('semester:id,name')
//            ->select(['id', 'code', 'name', 'semester_id'])
//            ->get()->toArray();
//    }

    public function setLectureDates(int $syllabusId): array
    {
        return Lecture::whereSyllabusId($syllabusId)
            ->pluck('lecture_date', 'id')
            ->toArray();
    }

    public function removeStudentFromSyllabus(int $syllabusId, int $studentId): array
    {
        try {
            $student = Student::find($studentId);
            $studentSyllabusHistory = StudentSyllabusHistory::whereStudentId($studentId)
                ->where('syllabus_id', $syllabusId);
            if (!$studentSyllabusHistory->exists()) {
                return [
                    'success' => false,
                    'message' => __("სტუდენტი სილაბუსში არ მოიძებნა.")
                ];
            }
            $studentSyllabusHistory->delete();
            $lectureIds = Lecture::whereSyllabusId($syllabusId)->pluck('id')->toArray();
            LectureStudent::whereIn('lecture_id', $lectureIds)->where('student_id', $studentId)
                ->delete();
            SyllabusStudentGuest::where('syllabus_id', $syllabusId)->where('student_id', $studentId)
                ->delete();
            return [
                'success' => true,
                'message' => __('სტუდენტი სილაბუსიდან ამოიშალა.')
            ];
        } catch (\Exception $e) {
            \Log::debug($e->getMessage());
            return [
                'success' => false,
                'message' => __('სტუდენტის წაშლისას დაფიქსირდა შეცდომა, მიმართეთ აიტი სამსახურს.')
            ];
        }
    }

    public function busyLecture(
        string   $lectureDate,
        string   $startTime,
        string   $endTime,
        int      $syllabusId,
        int|null $auditoriumId = null,
        int|null $lecturerId = null,
        int|null $currentLectureId = null
    ): bool
    {
        $lecture = Lecture::where('lecture_date', $lectureDate)
            ->where('syllabus_id', $syllabusId);

        if ($auditoriumId) {
            $lecture->where('auditorium_id', $auditoriumId);
        }

        if ($lecturerId) {
            $lecture->where('lecturer_id', $lecturerId);
        }

        if ($currentLectureId) {
            $lecture->where('id', '!=', $currentLectureId);
        }

        return $lecture->where(function ($query) use ($startTime, $endTime) {
            return $query->where(function ($query) use ($startTime, $endTime) {
                $query->where('start_time', '<', $endTime)
                    ->where('end_time', '>', $startTime);
            });
        })->exists();
    }


    /**
     * @param array $lectures Lecture ids
     * @throws \Throwable
     */

    public function updateCurriculumLectures(array $lectures): array
    {
        DB::beginTransaction();
        try {
            foreach ($lectures as $lecture) {
                $auditoriumBusyLecture = $this->busyLecture(
                    $lecture['lecture_date'],
                    $lecture['start_time'],
                    $lecture['end_time'],
                    $lecture['syllabus_id'],
                    auditoriumId: $lecture['auditorium_id'],
                    currentLectureId: $lecture['id']
                );
                $lecturerBusyLecture = $this->busyLecture(
                    $lecture['lecture_date'],
                    $lecture['start_time'],
                    $lecture['end_time'],
                    $lecture['syllabus_id'],
                    lecturerId: $lecture['lecturer_id'],
                    currentLectureId: $lecture['id'],
                );

                $accountingCodeInUse = Curriculum::query()
                    ->where(function ($query){
                        $flowId = Setting::query()
                            ->where('key', 'current_semester')
                            ->first()
                            ->value ?? null
                        ;

                        $query->where('flow_id', $flowId);
                    })
                    ->whereNot('syllabus_id', $lecture['syllabus_id'])
                    ->whereHas('lecture.times', function ($query) use ($lecture){
                        $query->where('lecturer_accounting_code', $lecture['lecturer_accounting_code']);
                    })
                    ->exists()
                ;

                if ($lecturerBusyLecture || $auditoriumBusyLecture || $accountingCodeInUse) {
                    DB::rollBack();
                    if ($lecturerBusyLecture) {
                        return [
                            'code' => 403,
                            'message' => 'Lecturer is already busy'
                        ];
                    }elseif($accountingCodeInUse){
                        return [
                            'code' => 403,
                            'message' => 'Lecturer accounting code is in use'
                        ];
                    }
                    return [
                        'code' => 403,
                        'message' => 'Auditorium is already busy'
                    ];
                }
                $this->updateLectureWorkHours($lecture);
            }
            DB::commit();
            return [
                'code' => 200,
                'message' => 'Changes applied!'
            ];
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug(json_encode([
                'message' => $exception->getMessage(),
                'line' => $exception->getLine()
            ]));
            return [
                'code' => '400',
                'debug' => $exception->getLine(),
                'message' => 'Something went wrong'
            ];
        }
    }

    public function updateLectureWorkHours(array $lecture): void
    {
        $currentLecture = Lecture::find($lecture['id']);
        $curriculumId = Curriculum::whereSyllabusId($currentLecture->syllabus_id)->first()->id;
        $curriculumLectureId = CurriculumLecture::whereCurriculumId($curriculumId)
            ->first()->id;
        $lectureHours = CurriculumLecture::whereCurriculumId($curriculumId)->first()->lectures_count;
        //dd($lecture);
        if ($lecture['lecturer_id'] != $currentLecture['lecturer_id']) {
            $lectures = Lecture::whereSyllabusId($currentLecture->syllabus_id);
            $lectureStartDate = $lectures->latest('lecture_date')->first()->lecture_date;
            $lectureEndDate = $lectures->first()->lecture_date;
            $lecturerCurriculum = CurriculumLectureTime::whereLecturerId(
                $lecture['lecturer_id']
            )->where('curriculum_lecture_id', $curriculumLectureId);
            if ($lecturerCurriculum->exists()) {
                $updatedData = [
                    'missed_lectures' => $lecturerCurriculum->first()->missed_lectures - $lectureHours
                ];
                if (Carbon::parse($lectureStartDate)->gt(Carbon::parse($lecture['lecture_date']))) {
                    $updatedData['lecturer_start_date'] = $lecture['lecture_date'];
                }
                if (Carbon::parse($lectureEndDate)->lt(Carbon::parse($lecture['lecture_date']))) {
                    $updatedData['lecturer_end_date'] = $lecture['lecture_date'];
                }
                $lecturerCurriculum->first()->update($updatedData);
            } else {
                CurriculumLectureTime::create([
                    'curriculum_lecture_id' => $curriculumLectureId,
                    'week_day' => $lecture['week_day'],
                    'lecturer_id' => $lecture['lecturer_id'],
                    'start_time' => $lecture['start_time'],
                    'end_time' => $lecture['end_time'],
                    'auditorium_id' => $lecture['auditorium_id'],
                    'payment_per_hour' => $lecture['payment_per_hour'],
                    'is_lecture' => $lecture['is_lecture'],
                    'lecturer_start_date' => $lecture['lecture_date'],
                    'lecturer_end_date' => $lecture['lecture_date'],
                    'lecturer_accounting_code' => $lecture['lecturer_accounting_code'],
                    'missed_lectures' => $lectureHours * -1
                ]);
            }
            CurriculumLectureTime::whereLecturerId($currentLecture['lecturer_id'])
                ->where('curriculum_lecture_id', '=', $curriculumLectureId)
                ->first()->increment('missed_lectures', $lectureHours);
        }
        $dd = CurriculumLectureTime::where('lecturer_id', $currentLecture['lecturer_id'])
            ->where('curriculum_lecture_id', $curriculumLectureId)
            ->first();
        $dd->lecturer_accounting_code = $lecture['lecturer_accounting_code'];
        $dd->update();
        Lecture::find($lecture['id'])->update($lecture);
    }

    public function setProfessionStudentMark(
        int $syllabusId,
        int $assignmentId,
        int $mark,
        int $studentId,
    ): array
    {
        if (auth()->user()->user_type_id == UserType::STUDENT) {
            return [
                'message' => 'Forbidden',
                'status' => 403
            ];
        }
        $userId = Student::select('id', 'user_id')->find($studentId)->user_id;
        $assignment = Assignment::find($assignmentId);
        $this->checkExpiration($assignment);
        if (!$this->studentExistInSyllabus(
            $studentId,
            $syllabusId
        )) {
            return [
                'message' => 'სტუდენტი ამ საგანზე არ მოიძებნა',
                'status' => 404
            ];

        }
        StudentAssignment::updateOrCreate(
            [
                'syllabus_id' => $syllabusId,
                'assignment_id' => $assignmentId,
                'student_id' => $studentId,
            ],
            [
                'point' => $mark,
                'date' => now()->format('Y-m-d H:i:s')
            ]
        );
        return [
            'message' => 'ქულა წარმატებით დაიწერა!',
            'status' => 201
        ];
    }

    public function copySyllabusRelationData($newSyllabus, $originalSyllabus)
    {
        $this->replicateRelation('prerequisite_syllabus', $originalSyllabus->id, $newSyllabus->id);
        $this->replicateRelation('lecturer_syllabus', $originalSyllabus->id, $newSyllabus->id);
        $this->replicateRelation('method_syllabus', $originalSyllabus->id, $newSyllabus->id);
        $this->replicateRelation('lecturer_contact_times', $originalSyllabus->id, $newSyllabus->id);
        if ($newSyllabus->is_profession==0)
        {
            $this->replicateRelation('weeks', $originalSyllabus->id, $newSyllabus->id);
        }
        $this->replicateRelation('assignments', $originalSyllabus->id, $newSyllabus->id);

    }

    public function replicateRelation($tableName, $oldSyllabusId, $newSyllabusId)
    {
        $relationData = DB::table($tableName)
            ->where('syllabus_id', $oldSyllabusId)
            ->get();

        if ($relationData->isNotEmpty()) {
            foreach ($relationData as $item) {
                // Convert stdClass object to array to replicate
                $itemArray = (array) $item;
                unset($itemArray['id']); // Remove the ID field to avoid conflicts
                $itemArray['syllabus_id'] = $newSyllabusId;
                DB::table($tableName)->insert($itemArray);
            }
        }
    }

}
