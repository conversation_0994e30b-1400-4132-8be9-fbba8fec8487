<?php

namespace App\Services;

use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Curriculum\LectureStudent;
use App\Models\Lectures\Lecture;
use App\Models\Lectures\StudentAttendance;
use App\Models\Reestry\Flow;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Setting;
use App\Models\Syllabus\Syllabus;
use App\Services\Syllabus\SyllabusService;
use Hamcrest\Core\Set;
use Illuminate\Support\Facades\Log;

class LecturerService
{

    public function lecturerSubjects(): array
    {
        $lecturerSubjects = Syllabus::with(['semester:id,name', 'academicDegree'])
            ->whereHas('curriculum')
//            ->whereHas('curriculum', function ($query) {
//                $currentFlowId = Setting::where('key', 'current_semester')
//                    ->first()->value;
//                return $query->where(
//                    'flow_id',
//                    $currentFlowId
//                );
//            })
            ->whereHas('lecturers', function ($query) {
                return $query->where('user_id', \Auth::id());
            })->get()->map(function ($subject) {
                $response = [];
                $response['syllabus_id'] = $subject->id;
                $response['name'] = $subject->name;
                $response['code'] = $subject->code;
                $response['credits'] = $subject->credits;
                $response['semester'] = $subject->semester->name;
                $response['contact_hours'] = $subject->contact_hours;
                $response['lecture_hours'] = $subject->lecture_hours;
                $response['seminar_hours'] = $subject->seminar_hours;
                $response['total_hours'] = $subject->total_hours;
                $response['academic_degree'] = $subject->academicDegree->name_ka;
                return $response;
            })->toArray();
        return $lecturerSubjects;
    }

    public function todayLectures(): array
    {
        $lecturer = Lecturer::whereUserId(\Auth::id())
            ->first();
        if (!$lecturer) {
            return [
                'code' => 404,
                'message' => 'Lecturer not found'
            ];
        }
        $lecturerId = $lecturer->id;
        $lectures = Lecture::with(['syllabus:id,name,learn_year_id,is_profession,syllabus_type_id' => [
            'curriculum:id,syllabus_id,flow_id' => [
                'flow:id,program_id' => [
                    'program:id,name_ka,name_en,school_id,academic_degree_id' => [
                        'school:id,name_ka,name_en',
                        'academicDegree:id,name_ka,name_en,url'
                    ]
                ]
            ]
        ],
            'auditorium:id,name'
        ])
            ->whereLectureDate(now()->format('Y-m-d'))
            ->where('lecturer_id', '=', $lecturerId)
            ->get()->map(function ($lecture) {
                $lectureFlow = $lecture->syllabus->learnYear;
                $lecture['semester'] = $lecture->syllabus->curriculum->flow->name;
//                $lecture['semester'] = Setting::where('key', '=', 'current_semester')
//                    ->first()->value;
                $lecture['subject'] = $lecture->syllabus->name;
                $lecture['syllabus_id'] = $lecture->syllabus_id;
                $lecture['is_profession'] = $lecture->syllabus->is_profession;
                $lecture['syllabus_type_id'] = $lecture->syllabus->syllabus_type_id;
                $lecture['program'] = $lectureFlow->program->name_ka;
                $lecture['school'] = $lectureFlow->program->school->name_ka;
                $lecture['academicDegree'] = $lectureFlow->program->academicDegree->name_ka;
                $lecture['academicDegreeUrl'] = $lectureFlow->program->academicDegree->url;
                $lecture['lectureType'] = $lecture->is_lecture ? 'ლექცია' : 'სემინარი';

                $filteredStudentGroupIds = $lecture->syllabus->curriculum->lecture->times->filter(function ($time) use ($lecture) {
                    return $time->week_day == $lecture->week_day && $time->start_time == $lecture->start_time;
                })->flatMap(function ($time) {
                    return $time->studentGroups->pluck('id');
                });
                $lecture['studentGroups'] = StudentGroup::whereIn('id', $filteredStudentGroupIds)
                    ->pluck('name_ka', 'id');
                /**
                 * Unset additional columns from lecture object.
                 */

                unset(
                    $lecture->syllabus,
                    $lecture->auditorium->id,
                    $lecture->payment_per_hour,
                    $lecture->is_current,
                    //$lecture->syllabus_id,
                    $lecture->lecturer_id,
                    $lecture->week_day,
                    $lecture->auditorium_id,
                    $lecture->created_at,
                    $lecture->updated_at,
                    $lecture->id,
                    $lecture->is_lecture,
                );
                $auditoriumName = $lecture->auditorium->name;
                unset($lecture->auditorium);
                $lecture['auditorium'] = $auditoriumName;
                return $lecture;
            });
        $semester_name = Flow::whereId(Setting::where('key', '=', 'current_semester')
            ->first()->value)->first();
        return [
            'code' => 200,
            'data' => $lectures,
            'parameters' => [
                'semester' => $semester_name->name,
                'semester_en' => $semester_name->name_en
            ]
        ];
    }

    public function weekSchedule(): array
    {
        try {
            $lecturer = Lecturer::whereUserId(auth()->id())
                ->first();
            if (!$lecturer) {
                return [
                    'code' => 404,
                    'message' => 'Lecturer not found'
                ];
            }
            $lecturerId = $lecturer->id;
            $weekLectures = Lecture::whereLecturerId($lecturerId)
                ->whereBetween('lecture_date', [
                    now()->startOfWeek()->format('Y-m-d'),
                    now()->endOfWeek()->format('Y-m-d')
                ])
                ->whereHas('syllabus.curriculum')
//                ->whereHas('syllabus.curriculum', function ($query) {
//                    return $query->where('flow_id', Setting::where('key', '=', 'current_semester')
//                        ->first()->id);
//                })
                ->with([
                    'syllabus:id,name,status_id,learn_year_id,semester_id,academic_degree_id,is_profession,syllabus_type_id' => [
                        'status:id,name_ka',
                        'semester:id,name',
                        'learnYear:id,program_id' => [
                            'program:id,name_ka'
                        ]
                    ],
                    'auditorium:id,name'
                ])
                ->orderBy('lecture_date')->get()
                ->map(function ($lecture) {
                    $lectureFlow = $lecture->syllabus->curriculum?->flow;
                    $syllabusId = $lecture['syllabus_id'];

                    $filteredStudentGroupIds = $lecture->syllabus->curriculum->lecture->times->filter(function ($time) use ($lecture) {
                        return $time->week_day == $lecture->week_day && $time->start_time == $lecture->start_time;
                    })->flatMap(function ($time) {
                        return $time->studentGroups->pluck('id');
                    });

//                    $studentGroupIds = Student::whereIn(
//                        'id',
//                        LectureStudent::where('lecture_id', $lecture['id'])
//                            ->pluck('student_id')->toArray()
//                    )->distinct('group_id')->pluck('group_id')->toArray();
                    $lecture['studentGroups'] = StudentGroup::whereIn('id', $filteredStudentGroupIds)
                        ->pluck('name_ka', 'id');
                    $lecture['is_profession'] = $lecture->syllabus->is_profession;
                    $lecture['syllabus_type_id'] = $lecture->syllabus->syllabus_type_id;
                    $lecture['academic_degree_id'] = $lecture->syllabus->academic_degree_id;

                    /**
                     * Unset additional columns from lecture object.
                     */

                    unset(
                        $lecture->auditorium->id,
                        $lecture->payment_per_hour,
                        $lecture->syllabus->curriculum,
                        $lecture->created_at,
                        $lecture->updated_at,
                    );
                    return $lecture;
                })->groupBy(function ($item) {
                    $dateService = new DateService();
                    return $dateService->getWeekDayName($item->week_day);
                });
            return [
                'code' => 200,
                'data' => $weekLectures
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => 'დაფიქსირდა შეცდომა!'
            ];
        }
    }

    /**
     * @param int $syllabusId
     * @param string $lectureIds filter by lecture ids , if is in response
     * @return array
     */
    public function syllabusLectures(int $syllabusId, string $lectureIds = ''): array
    {
        try {
            $lectures = Lecture::whereSyllabusId($syllabusId);
            if (strlen($lectureIds) > 0) {
                $lectureIdList = explode(',', $lectureIds);
                $lectures = $lectures->select(
                    'id',
                    'lecture_date',
                    'lecture_number',
                    'start_time',
                    'end_time',
                    'payment_per_hour',
                    'auditorium_id',
                    'lecturer_id',
                    'is_lecture',
                    'week_day'
                )->with('lecturer:id,first_name,last_name', 'auditorium:id,name')
                    ->whereIn('id', $lectureIdList);
            } else {
                $lectures = $lectures->select(
                    'id',
                    'lecture_date',
                    'lecture_number',
                    'start_time',
                    'end_time',
                    'lecturer_id',
                    'week_day',
                    'is_lecture'
                )->with('lecturer:id,first_name,last_name');
            }
            if ($lectures->exists()) {
                $lectures = $lectures->get()->map(function ($item) use ($syllabusId, $lectureIds) {
                    if (strlen($lectureIds) > 0) {
                        $curriculumId = Curriculum::whereSyllabusId($syllabusId)->first()?->id;
                        $curriculumLectureId = CurriculumLecture::whereCurriculumId($curriculumId)
                            ->first()->id;
                        $curriculum_lecture_time = CurriculumLectureTime::whereCurriculumLectureId($curriculumLectureId)
                            // ->where('is_lecture', $item->is_lecture)
                            // ->where('start_time', $item->start_time)
                            //->where('end_time', $item->end_time)
                            ->where('lecturer_id', $item->lecturer_id)
                            ->first();
                        $item['accounting_code'] = $curriculum_lecture_time->lecturer_accounting_code;
                        $item['is_lecture'] = $item->is_lecture;
                        $item['week_day'] = $item->week_day;
                        unset($item['auditorium_id']);
                    }
                    unset($item['lecturer_id']);
                    return $item;
                });
                $curriculum = Curriculum::whereSyllabusId($syllabusId)->first();

                $studentFlowIds = $curriculum->student_flow_ids;
                $flowData = [];
                if ($studentFlowIds)
                {
                    $flows = \App\Models\Reestry\LearnYear::query()
                        ->with('program.school')
                        ->whereIn('id', $studentFlowIds)
                        ->get()
                        ->groupBy('program_id')
                    ;

                    foreach ($flows as $programId => $flow)
                    {
                        $flowData[] = [
                            'flow_id' => $flow->pluck('id')->toArray(),
                            'program_id' => $programId,
                            'school_id' => $flow->first()->program->school->id ?? null
                        ];
                    }
//                    $curriculum->student_flow_ids = $flowData;
                }

                $curriculumLecture = CurriculumLecture::whereCurriculumId($curriculum->id)->first();
                $parameters['start_date'] = $curriculum->start_date;
                $parameters['student_flow_ids'] = $flowData;
                $parameters['end_date'] = $curriculum->end_date;
                $parameters['lectures_count'] = $curriculumLecture->lectures_count;
                $parameters['registration_start_date'] = $curriculum->registration_start_date;
                $parameters['registration_end_date'] = $curriculum->registration_end_date;
                $parameters['allowed_amount_of_students'] = $curriculum->allowed_amount_of_students;
                $parameters['minimum_amount_of_students'] = $curriculum->minimum_amount_of_students;

                return [
                    'code' => 200,
                    'data' => $lectures,
                    'parameters' => $parameters
                ];
            } else {
                return [
                    'code' => 404,
                    'message' => __('Lectures not found')
                ];
            }
        } catch (\Exception $exception) {
            Log::debug($exception->getMessage());
            return [
                'code' => 500,
                'message' => 'Something went wrong!'
            ];
        }
    }

}
