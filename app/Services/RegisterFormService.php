<?php

namespace App\Services;

use App\Models\RegisterForms\RegisterFormActivate;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class RegisterFormService
{

    public static function restrictedAccessForm($url, $modelId)
    {
        $bachelorActivation = RegisterFormActivate::whereUrl($url)
            ->whereHas('learnYear.program.academicDegree',
                function ($query) use ($modelId) {
                    return $query->where('id', $modelId);
                });
        $startDate = Carbon::createFromDate(
            $bachelorActivation
                ->first()
                ?->start_date
        );
        $endDate = Carbon::createFromDate($bachelorActivation
            ->first()
            ?->end_date);
        abort_if(
            !$bachelorActivation->exists()
            || (Carbon::now()->gt($endDate)
                || Carbon::now()->lt($startDate)), Response::HTTP_FORBIDDEN);
    }

}
