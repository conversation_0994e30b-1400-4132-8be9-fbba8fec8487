<?php

namespace App\Services;

use App\Models\Master\MasterEducation;
use App\Models\RegisterForms\Master\MasterRegisterInfo;

class MasterRegisterService
{

    public function storeEducations($id, $data): void
    {
        $data['master_register_id'] = $id;
        MasterEducation::create($data);
    }

    public function storeInfo($id, $data): void
    {
        $data['master_register_id'] = $id;
        MasterRegisterInfo::create($data);
    }

}
