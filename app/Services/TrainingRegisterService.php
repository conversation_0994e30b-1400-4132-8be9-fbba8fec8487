<?php

namespace App\Services;

use App\Models\RegisterForms\Training\TrainingRegisterAddress;
use App\Models\RegisterForms\Training\TrainingRegisterEducation;
use App\Models\RegisterForms\Training\TrainingRegisterProgram;
use App\Models\RegisterForms\Training\TrainingRegisterWork;
use Carbon\Carbon;

class TrainingRegisterService
{

    public function storeAddresses($trainingRegisterId, $data): void
    {
        $address = new TrainingRegisterAddress;
        $address->city = $data['city'];
        $address->street = $data['street'];
        $address->city_of_birth = $data['city_of_birth'];
        $address->training_register_id = $trainingRegisterId;
        $address->save();
    }

    public function storePrograms($trainingRegisterId, $data): void
    {
        $program = new TrainingRegisterProgram;
        $program->training_register_id = $trainingRegisterId;
        $program->program_id = $data['program_id'];
        $program->level_id = $data['level_id'];
        $program->save();
    }

    public function storeEducations($trainingRegisterId, $data): void
    {
        $education = new TrainingRegisterEducation;
        $education->training_register_id = $trainingRegisterId;
        $education->university = $data['university'];
        $education->faculty = $data['faculty'];
        $education->start_date = $data['start_date'];
        $education->end_date = $data['end_date'];
        $education->academic_degree_id = $data['academic_degree_id'];
        if($data['university'] or $data['faculty'] or $data['start_date'] or $data['end_date'] or $data['academic_degree_id'])
        {
            $education->save();
        }
    }

    public function storeWorks($trainingRegisterId, $data): void
    {
        $work = new TrainingRegisterWork;
        $work->training_register_id = $trainingRegisterId;
        $work->organization = $data['organization'];
        $work->position = $data['position'];
        $work->employment_field = $data['employment_field'];
        $work->start_date = $data['start_date'];
        $work->end_date = $data['end_date'];
//        $work->start_date = $this->dateFormatter(
//            format: 'd-m-Y',
//            date: $data['start_date']
//        );
//        $work->end_date = $this->dateFormatter(
//            format: 'd-m-Y',
//            date: $data['end_date']
//        );
        $work->save();
    }

    public function dateFormatter($format, $date): \DateTime
    {
        return Carbon::createFromFormat(
            $format,
            $date
        );
    }

}
