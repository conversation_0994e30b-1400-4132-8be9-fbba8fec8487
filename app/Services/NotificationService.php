<?php

namespace App\Services;

use App\Models\Message\Message;
use App\Models\Notification;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Severity;
use App\Models\Syllabus\Syllabus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;
use Request;

class NotificationService
{

    public function sendExamNotification($assignment, $date): void
    {
        $messageService = new MessageService();
        $studentIds = StudentSyllabusHistory::whereSyllabusId($assignment->syllabus_id)
            ->pluck('student_id')->toArray();
        $assignmentName = $assignment->assessmentComponent->name_ka;
        $subjectName = Syllabus::find($assignment->syllabus_id)->name;
        $message = Message::create([
            'title' => "გამოცდა $assignmentName",
            'body' => "საგანში $subjectName გამოცდა $assignmentName ჩაგიტარდებათ $date",
            'author_id' => Auth::id()
        ]);
        $userIds = $messageService->getUserIds(students: $studentIds);
        $addressesData = $messageService->mapMessageToUsers($userIds, $message->id);
        $message->addresses()->createMany($addressesData);
        $bodyText = "გამოცდა $assignmentName საგანში $subjectName ჩაინიშნა $date";
        $lectureMessage = Message::create([
            'title' => "გამოცდა $assignmentName",
            'body' => $bodyText,
            'author_id' => Auth::id()
        ]);
        $lecturerIds = Syllabus::find($assignment->syllabus_id)
            ->lecturers()->pluck('lecturer_id')->toArray();
        $lecturerUserIds = $messageService->getUserIds(lecturers: $lecturerIds);
        $addresses = $messageService->mapMessageToUsers($lecturerUserIds, $lectureMessage->id);
        $lectureMessage->addresses()->createMany($addresses);
        foreach ($studentIds as $studentId) {
            $userId = Student::find($studentId)->user_id;
            $notification = self::sendNotification($userId, Severity::HIGH, $bodyText);
            self::publish($notification);
        }
    }

    public function sendNotification(int $userId, int $severityId, string $text): Notification
    {
        $notification = Notification::create([
            'user_id' => $userId,
            'severity_id' => $severityId,
            'text' => $text
        ]);
        return $notification;
    }

    public function markNotificationsAsSeen(Request $request): array
    {
        $request->validate([
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer|exists:notifications,id',
        ]);

        $notificationIds = $request->input('notification_ids');
        $time = now()->format('Y-m-d H:i:s');

        $chunkSize = 500;

        foreach (array_chunk($notificationIds, $chunkSize) as $chunk) {
            Notification::whereIn('id', $chunk)->update([
                'seen_at' => $time
            ]);
        }

        return [
            'notifications' => $notificationIds,
            'time' => $time
        ];
    }

    public function getNotifications()
    {
        return Notification::whereUserId(Auth::id())
            ->orderByDesc('created_at')
            ->paginate(30)
            ->map(function ($notification) {
                $notification->title = 'System Notification';
                return $notification;
            });
    }

    public function getNotificationsHeader()
    {
        $notifications = Notification::whereUserId(Auth::id())
            ->orderByDesc('created_at')
            ->limit(3)
            ->get();

        return $notifications->map(function ($notification) {
            $notification->title = 'System Notification';
            return $notification;
        });
    }



    public function publish(Notification $notification): void
    {
        $userId = $notification->user_id;
//        Redis::publish("exam-notification-{$userId}", [
//            'message' => $notification->text,
//            'notificationId' => $notification->id,
//            'notificationSeverity' => $notification->severity_id
//        ]);
        //TODO:: გასაკეთებელია
    }

}
