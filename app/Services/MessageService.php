<?php

namespace App\Services;

use App\Models\Curriculum\CurriculumStudentGroup;
use App\Models\Message\Message;
use App\Models\Message\MessageAttachment;
use App\Models\Reestry\Administration\Administration;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\SyllabusStudentGuest;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MessageService
{

    public function mapMessageToUsers(array $userIds, int $messageId): Collection
    {
        return collect($userIds)->map(function ($item) use ($messageId) {
            $user = [];
            $user['message_id'] = $messageId;
            $user['user_id'] = $item;
            return $user;
        });
    }

    public function searchPeople(string $keyword): Collection
    {
        $administrations = Administration::select(['id', 'first_name', 'last_name', 'photo', 'user_id'])
            ->where('user_id', '!=', Auth::id())
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
            })->take(4)->get()->map(function ($item) {
                $item['type'] = 'Administration';
                return $item;
            });
        $lecturers = Lecturer::select(['id', 'first_name', 'last_name', 'photo', 'user_id'])
            ->where('user_id', '!=', Auth::id())
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", ["%$keyword%"]);
            })->take(4)->get()->map(function ($item) {
                $item['type'] = 'Lecturer';
                return $item;
            });
        $students = Student::select(['id', 'name as first_name', 'surname as last_name', 'user_id', 'photo'])
            ->where('user_id', '!=', Auth::id())
            ->where(function ($query) use ($keyword) {
                $query
                    ->whereRaw("CONCAT(name, ' ', surname) LIKE ?", ["%$keyword%"])
                    ->orWhereRaw("CONCAT(surname, ' ', name) LIKE ?", ["%$keyword%"]);
            })->take(4)->get()->map(function ($item) {
                $item['type'] = 'Student';
                return $item;
            });

        return $administrations->concat($lecturers)->concat($students)
            ->take(12);
    }

    public function createAttachments($attachments, int $messageId): void
    {
        $imageService = new ImageService();
        $currentDate = now()->format('d-m-Y');
        foreach ($attachments as $attachment) {
            $fileName = $imageService->upload(
                $attachment,
                "/messages/$currentDate"
            );
            MessageAttachment::create([
                'message_id' => $messageId,
                'filename' => $fileName,
                'original_name' => substr($attachment->getClientOriginalName(), 0, 255)
            ]);
        }
    }

    public function getUserIds(
        array|null $schools = null,
        array|null $studentGroups = null,
        array|null $programs = null,
        array|null $flows = null,
        array|null $users = null,
        array|null $students = null,
        array|null $lecturers = null,
        array|null $groupIds = null,
        array|null $syllabusIds = null,
        bool|null $isBoth = null,

    ): array
    {
        if ($groupIds or $syllabusIds)
        {
            $userIds = $this->curriculumStudentIds($groupIds, $syllabusIds, $isBoth);
            return $userIds;
        }

        if ($users) {
            $userIds = $users;
        }

        if ($programs) {
            $userIds = Student::whereIn('program_id', $programs)
                ->pluck('user_id')->toArray();
        }

        if ($flows) {
            $userIds = Student::whereIn('learn_year_id', $flows)
                ->pluck('user_id')->toArray();
        }

        if ($schools) {
            $userIds = Student::whereIn('school_id', $schools)
                ->pluck('user_id')->toArray();
        }

        if ($studentGroups) {
            $userIds = Student::whereIn('group_id', $studentGroups)
                ->pluck('user_id')->toArray();
        }

        if ($students) {
            $userIds = Student::whereIn('id', $students)->pluck('user_id')->toArray();
        }

        if ($lecturers) {
            $userIds = Lecturer::whereIn('id', $lecturers)->pluck('user_id')->toArray();
        }

        return $userIds;
    }

    public function filterByPrograms(string $programs): array
    {
        $programs = json_decode($programs);
        $groups = StudentGroup::whereIn('program_id', $programs)->get();
        $flows = LearnYear::whereIn('program_id', $programs)->get();

        return [
            'groups' => $groups,
            'flows' => $flows
        ];
    }


    public function filterBySchools(string $schools): Collection
    {
        $schools = json_decode($schools);
        return Program::whereIn('school_id', $schools)->get();

    }

    public function allTypeMessageCount(): array
    {
        $sentMessageCount = Message::active()->with([
            'author',
            'addresses',
            'attachments'
        ])->whereNull('main_message_id')
            ->where('author_id', Auth::id())
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at')->whereNull('viewed_at');
            })
            ->count();

        $deletedMessageCount = Message::with([
            'author',
            'addresses',
            'attachments'
        ])
            ->whereNull('main_message_id')
            ->where(function ($query) {
                return $query->where(function ($query) {
                    return $query->trash()
                        ->where('author_id', Auth::id());
                })->orWhereHas('addresses', function ($query) {
                    return $query->trash()
                        ->where('user_id', Auth::id());
                });
            })
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at')->whereNull('viewed_at');
            })
            ->count();


        $favouriteMessageCount = Message::with([
            'author',
            'addresses',
            'attachments'
        ])
            ->whereNull('main_message_id')
            ->where(function ($query) {
                return $query->where(function ($query) {
                    return $query->favorite()
                        ->where('author_id', Auth::id());
                })->orWhereHas('addresses', function ($query) {
                    return $query->favorite()
                        ->where('user_id', Auth::id());
                });
            })
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at')->whereNull('viewed_at');
            })
            ->count();

        $incomingMessageCount = Message::with([
            'author',
            'addresses',
            'attachments'
        ])
            ->whereHas(
                'addresses',
                fn($query) => $query->active()
                    ->where('user_id', Auth::id())
            )
            ->whereNull('main_message_id')
            ->whereHas('addresses', function ($query) {
                $query->whereNull('deleted_at')->whereNull('viewed_at');
            })
            ->count();

        return [
            [
                'name' => 'compose',
                'query' => 'compose',
                'icon' => "compose",
                'badgeColor' => "success",
                'isButton' => true,
            ],[
                'name' => 'inbox',
                'query' => 'inbox',
                'icon' => "inbox",
                'messageCount' => $incomingMessageCount,
                'badgeColor' => "success"
            ],[
                'name' => 'favorites',
                'query' => 'favorites',
                'icon' => "favorites",
                'messageCount' => $favouriteMessageCount,
                'badgeColor' => "primary"
            ],[
                'name' => 'sent',
                'query' => 'sent',
                'icon' => "sent",
                'messageCount' => $sentMessageCount,
                'badgeColor' => "success"
            ],[
                'name' => 'trash',
                'query' => 'trash',
                'icon' => "delete",
                'messageCount' => $deletedMessageCount,
                'badgeColor' => "danger"
            ],
        ];
    }


    public function curriculumStudentIds($groupIds, $syllabusIds, $isBoth): array
    {
        $studentIds = [];
        if (is_null($groupIds) or $isBoth)
        {
            $studentIds = StudentSyllabusHistory::query()
                ->whereIn('syllabus_id', $syllabusIds)
                ->pluck('student_id')
                ->toArray()
            ;
        }

        $curriculumStudentGroups = CurriculumStudentGroup::query()
            ->with('curriculumLectureTime.curriculumLecture.curriculum.syllabus.students')
            ->whereHas('curriculumLectureTime.curriculumLecture.curriculum.syllabus', function ($query) use ($syllabusIds) {
                $query->whereIn('id', $syllabusIds);
            })
            ->whereIn('student_group_id', $groupIds ?? [])
            ->get()
        ;

        foreach ($curriculumStudentGroups as $group)
        {
            $students = $group->curriculumLectureTime->curriculumLecture->curriculum->syllabus->students ?? null;
            $studentIds = array_merge($students->pluck('student_id')->toArray(), $studentIds);
        }

        $guestStudentIds = SyllabusStudentGuest::query()
            ->whereIn('student_group_id', $groupIds ?? [])
            ->whereIn('syllabus_id', $syllabusIds)
            ->pluck('student_id')
            ->toArray()
        ;

        $studentIds = array_merge($guestStudentIds, $studentIds);

        return Student::query()->whereIn('id', array_unique($studentIds))->pluck('user_id')->toArray();
    }
}
