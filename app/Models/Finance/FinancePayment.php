<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Finance\FinancePayment
 *
 * @property int $id
 * @property int $finance_calendar_id
 * @property float $amount
 * @property string $date
 * @property-read \App\Models\Finance\FinanceCalendar|null $financeCalendar
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment whereFinanceCalendarId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinancePayment whereId($value)
 * @mixin \Eloquent
 */
class FinancePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'finance_calendar_id',
        'amount',
        'date'
    ];

    public function financeCalendar(): BelongsTo
    {
        return $this->belongsTo(
            FinanceCalendar::class
        );
    }
}
