<?php

namespace App\Models\Finance;

use App\Models\Reestry\Student\Student;
use App\Models\User\User;
use App\Models\UserProgram;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Finance\FinanceScheduler
 *
 * @property int $id
 * @property int $is_active
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Finance\FinanceCalendar[] $calendars
 * @property-read int|null $calendars_count
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler active()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceScheduler whereUserId($value)
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $comment
 * @property int|null $status_id
 * @property-read Student|null $student
 * @property-read User|null $user
 * @method static Builder|FinanceScheduler adminProgramScope()
 * @method static Builder|FinanceScheduler onlyTrashed()
 * @method static Builder|FinanceScheduler whereComment($value)
 * @method static Builder|FinanceScheduler whereDeletedAt($value)
 * @method static Builder|FinanceScheduler whereStatusId($value)
 * @method static Builder|FinanceScheduler withTrashed()
 * @method static Builder|FinanceScheduler withoutTrashed()
 * @method static Builder|FinanceScheduler filter(\App\Filters\QueryFilters $filters)
 * @mixin \Eloquent
 */
class FinanceScheduler extends Model
{
    use SoftDeletes, HasFilters;

    protected $fillable = [
        'is_active',
        'user_id',
        'status_id',
        'comment'
    ];

    protected $dates = ['deleted_at'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'user_id', 'user_id');
    }

    public function calendars(): HasMany
    {
        return $this->hasMany(
            FinanceCalendar::class,
            'finance_scheduler_id',
            'id'
        );
    }

    public function scopeActive($query)
    {
        return $query->whereIsActive(1);
    }

    public function scopeAdminProgramScope(Builder $query): void
    {
        $userId = auth()->id();
        $userProgramIds = UserProgram::whereUserId($userId)->pluck('program_id')->toArray();
        $userIds = Student::whereIn('program_id', $userProgramIds)->pluck('user_id')->toArray();

        $query->whereIn('user_id', $userIds);
    }

}
