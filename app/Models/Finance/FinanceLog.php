<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Finance\FinanceLog
 *
 * @property int $id
 * @property string $piradi_nom
 * @property string $tanxa
 * @property string $tarigi
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog wherePiradiNom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog whereTanxa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog whereTarigi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceLog whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FinanceLog extends Model
{
    use HasFactory;

    protected $table = 'finance_log';

    protected $fillable = ['piradi_nom', 'tanxa', 'tarigi'];
}
