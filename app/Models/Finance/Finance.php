<?php

namespace App\Models\Finance;

use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\Student;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Finance\Finance
 *
 * @property int $id
 * @property int|null $oris_id
 * @property string $Account1410
 * @property string $piradi_nom
 * @property int $ProgramID
 * @property int $saswavlo_weli
 * @property int $Qrter
 * @property string $PeriodFrom
 * @property float|null $kontraqtis_tanxa
 * @property float $dam_sagnebi
 * @property float $sareitingo_fasdakleba
 * @property float $grantianis_fasdakleba
 * @property float $extra
 * @property float $sax_daxmareba
 * @property float $sax_granti
 * @property float $meriis_daxmareba
 * @property float $charicxuli_studenti
 * @property float $charicxuli_granti
 * @property float $charicxuli_sax_daxmareba
 * @property float $charicxuli_meriis_daxmareba
 * @property float $akademiuris_tanxa1
 * @property int $IsTrainings
 * @property float $kvartlis_nashti
 * @property float $kvartlis_jami
 * @property float $nashti
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Program|null $program
 * @method static \Illuminate\Database\Eloquent\Builder|Finance filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Finance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Finance query()
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereAccount1410($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereAkademiurisTanxa1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereCharicxuliGranti($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereCharicxuliMeriisDaxmareba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereCharicxuliSaxDaxmareba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereCharicxuliStudenti($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereDamSagnebi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereExtra($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereGrantianisFasdakleba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereIsTrainings($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereKontraqtisTanxa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereKvartlisJami($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereKvartlisNashti($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereMeriisDaxmareba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereNashti($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereOrisId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance wherePeriodFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance wherePiradiNom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereProgramID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereQrter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereSareitingoFasdakleba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereSaswavloWeli($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereSaxDaxmareba($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereSaxGranti($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Finance whereUpdatedAt($value)
 * @property-read Student|null $student
 * @mixin \Eloquent
 */
class Finance extends Model
{
    use HasFactory, HasFilters;

    protected $table = 'finance_new';

    const QUARTERS = [
        'first' => [
            '09',
            '11'
        ],
        'second' => [
            '12',
            '03',
        ],
        'third' => [
            '03',
            '06'
        ],
        'fourth' => [
            '07',
            '08'
        ]
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(
            Program::class,
            'ProgramID',
            'id'
        );
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'piradi_nom', 'personal_id')
            ->with('financeScheduler.calendars')
            ;
    }
}
