<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Finance\FinanceCalendar
 *
 * @property int $id
 * @property int $finance_scheduler_id
 * @property string $start_date
 * @property string|null $end_date
 * @property float $amount
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Finance\FinanceScheduler|null $financeScheduler
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereFinanceSchedulerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceCalendar whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FinanceCalendar extends Model
{
    use HasFactory;

    protected $fillable = [
        'finance_scheduler_id',
        'start_date',
        'end_date',
        'amount'
    ];


    public function financeScheduler(): BelongsTo
    {
        return $this->belongsTo(
            FinanceScheduler::class,
            'finance_scheduler_id',
            'id',
        );
    }

    public function startDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Carbon::make($value)->format('d/m/Y'),
            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
        );
    }

    public function endDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Carbon::make($value)->format('d/m/Y'),
            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value),
        );
    }
}
