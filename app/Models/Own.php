<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Own
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder|Own newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Own newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Own query()
 * @method static \Illuminate\Database\Eloquent\Builder|Own whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Own whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Own whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Own whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Own extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    const MODELS = [
        'Curriculum' => 1,
        'Administration' => 2,
        'Lecturer' => 3,
        'Program' => 4,
        'Student' => 5,
        'Flow' => 6,
        'LearnYear' => 7,
        'School' => 8,
        'Assignment' => 9,
        'Syllabus' => 10,
        'Campus' => 11,
        'Auditorium' => 12,
        'StudentGroup' => 13,
        'ApplicantsRegister' => 14
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
}
