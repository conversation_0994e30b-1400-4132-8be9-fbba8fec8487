<?php

namespace App\Models\Curriculum;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Curriculum\LectureStudent
 *
 * @property int $id
 * @property int $lecture_id
 * @property int $student_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent query()
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent whereLectureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LectureStudent whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class LectureStudent extends Model
{
    protected $table = 'lecture_student';
    protected $fillable = [
        'lecture_id',
        'student_id'
    ];
}
