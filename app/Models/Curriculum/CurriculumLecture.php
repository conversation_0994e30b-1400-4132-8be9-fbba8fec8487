<?php

namespace App\Models\Curriculum;

use App\Models\Lectures\StudentAttendance;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Curriculum\CurriculumLecture
 *
 * @property int $id
 * @property int $curriculum_id
 * @property int $free_places
 * @property int $registered_free_students
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Curriculum\Curriculum $curriculum
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Curriculum\CurriculumLectureTime[] $times
 * @property-read int|null $times_count
 * @method static \Database\Factories\Curriculum\CurriculumLectureFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereCurriculumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereFreePlaces($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereRegisteredFreeStudents($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereUpdatedAt($value)
 * @property int $lectures_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, StudentAttendance> $studentAttendances
 * @property-read int|null $student_attendances_count
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLecture whereLecturesCount($value)
 * @mixin \Eloquent
 */
class CurriculumLecture extends Model
{
    use HasFactory;

    protected $fillable = [
        'curriculum_id',
        'free_places',
        'lectures_count',
        'registered_free_students',
    ];

    public function curriculum(): BelongsTo
    {
        return $this->belongsTo(Curriculum::class);
    }

    public function times(): HasMany
    {
        return $this->hasMany(CurriculumLectureTime::class);
    }

    public function studentAttendances(): HasMany
    {
        return $this->hasMany(StudentAttendance::class, 'lecture_id');
    }
}
