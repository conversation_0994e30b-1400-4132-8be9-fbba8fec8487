<?php

namespace App\Models\Curriculum;

use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Curriculum\CurriculumStudentGroup
 *
 * @property int $id
 * @property int $curriculum_lecture_time_id
 * @property int $student_group_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup whereCurriculumLectureTimeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup whereStudentGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumStudentGroup whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CurriculumStudentGroup extends Model
{
    use HasFactory;

    protected $table = 'curriculum_student_group';

    protected $fillable = [
        'curriculum_lecture_time_id',
        'student_group_id'
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function curriculumLectureTime()
    {
        return $this->belongsTo(CurriculumLectureTime::class);
    }
}
