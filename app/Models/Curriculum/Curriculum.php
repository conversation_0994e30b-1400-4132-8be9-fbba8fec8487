<?php

namespace App\Models\Curriculum;

use App\Models\Own;
use App\Models\Reestry\Flow;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Scopes\AccessibleScope;
use App\Models\Syllabus\Syllabus;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\Curriculum\Curriculum
 *
 * @property int $id
 * @property int $syllabus_id
 * @property string $start_date
 * @property string $end_date
 * @property int $allowed_amount_of_students
 * @property int $minimum_amount_of_students
 * @property string|null $registration_start_date
 * @property string|null $registration_end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Curriculum\CurriculumLecture[] $lectures
 * @property-read int|null $lectures_count
 * @property-read Syllabus $syllabus
 * @method static \Database\Factories\Curriculum\CurriculumFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum query()
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereAllowedAmountOfStudents($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereMinimumAmountOfStudents($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereRegistrationEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereRegistrationStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereUpdatedAt($value)
 * @property int $flow_id
 * @property string $language
 * @property-read Flow $flow
 * @property-read \App\Models\Curriculum\CurriculumLecture|null $lecture
 * @property-read \Illuminate\Database\Eloquent\Collection<int, StudentGroup> $studentGroups
 * @property-read int|null $student_groups_count
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereFlowId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Curriculum whereLanguage($value)
 * @mixin \Eloquent
 */
class Curriculum extends Model
{
    use HasFactory;

    protected $fillable = [
        'syllabus_id',
        'start_date',
        'end_date',
        'allowed_amount_of_students',
        'minimum_amount_of_students',
        'registration_start_date',
        'registration_end_date',
        'flow_id',
        'student_flow_ids'
    ];

    protected $casts = [
        'student_flow_ids' => 'array',
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
//            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();

//            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
//            $model->student_flow_ids = $oldValue;

        });

        // Deleting even
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        if(is_array($model)) {
            $model['student_flow_ids'] = json_encode($model['student_flow_ids']);

        }else{
            $model->student_flow_ids = json_encode($model->student_flow_ids);
        }

        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function syllabus(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }

    public function toArray()
    {
        $data = parent::toArray();
        if ($this->relationLoaded('lecture')) {
            if ($this->lecture) {
                $data['lecture'] = [$this->lecture?->toArray()];
            }
        }
        return $data;
    }

    public function lecture(): HasOne
    {
        return $this->hasOne(CurriculumLecture::class);
    }

    public function studentGroups(): BelongsToMany
    {
        return $this->belongsToMany(StudentGroup::class);
    }

    public function flow(): BelongsTo
    {
        return $this->belongsTo(Flow::class); //მიმდინარე სემესტრის დასადგენად რომელ სასწავლო წლის სემესტრში იყო ეს საგანი შექმნილი
    }

}
