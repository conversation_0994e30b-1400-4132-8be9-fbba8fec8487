<?php

namespace App\Models\Curriculum;

use App\Models\Reestry\Auditorium;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Curriculum\CurriculumLectureTime
 *
 * @property int $id
 * @property int $curriculum_lecture_id
 * @property int $week_day
 * @property string $start_time
 * @property string $end_time
 * @property int $auditorium_id
 * @property int $lecturer_id
 * @property string $lecturer_accounting_code
 * @property int $payment_per_hour
 * @property int $is_lecture
 * @property string $lecturer_start_date
 * @property string $lecturer_end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Auditorium $auditorium
 * @property-read \App\Models\Curriculum\CurriculumLecture $curriculumLecture
 * @property-read Lecturer $lecturer
 * @property-read \Illuminate\Database\Eloquent\Collection|StudentGroup[] $studentGroups
 * @property-read int|null $student_groups_count
 * @method static \Database\Factories\Curriculum\CurriculumLectureTimeFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereAuditoriumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereCurriculumLectureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereIsLecture($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereLecturerAccountingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereLecturerEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereLecturerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereLecturerStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime wherePaymentPerHour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereWeekDay($value)
 * @property int $missed_lectures
 * @method static \Illuminate\Database\Eloquent\Builder|CurriculumLectureTime whereMissedLectures($value)
 * @mixin \Eloquent
 */
class CurriculumLectureTime extends Model
{
    use HasFactory;

    protected $fillable = [
        'curriculum_lecture_id',
        'week_day',
        'start_time',
        'end_time',
        'auditorium_id',
        'lecturer_id',
        'payment_per_hour',
        'is_lecture',
        'lecturer_start_date',
        'lecturer_end_date',
        'lecturer_accounting_code',
        'missed_lectures'
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function curriculumLecture(): BelongsTo
    {
        return $this->belongsTo(CurriculumLecture::class);
    }

    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(Lecturer::class);
    }

    public function auditorium(): BelongsTo
    {
        return $this->belongsTo(Auditorium::class);
    }

    public function studentGroups(): BelongsToMany
    {
        return $this->belongsToMany(StudentGroup::class, 'curriculum_student_group')->withTimestamps();
    }

    public function groups(): HasMany
    {
        return $this->hasMany(CurriculumStudentGroup::class, 'curriculum_lecture_time_id', 'id');
    }
}
