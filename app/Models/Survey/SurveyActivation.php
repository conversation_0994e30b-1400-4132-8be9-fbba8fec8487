<?php

namespace App\Models\Survey;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Syllabus\Syllabus;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Survey\SurveyActivation
 *
 * @property int $id
 * @property int $user_id
 * @property int $survey_id
 * @property int $syllabus_id
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Survey\Survey|null $survey
 * @property-read Syllabus $syllabus
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereSurveyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyActivation whereUserId($value)
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyAnswer[] $surveyAnswers
 * @property-read int|null $survey_answers_count
 * @mixin \Eloquent
 */
class SurveyActivation extends Model
{
    use HasFactory;

    protected $table = 'survey_activations';

    protected $fillable = [
        'user_id',
        'survey_id',
        'syllabus_id',
        'lecturer_id',
        'status',
        'answered',
        'learn_semester',
        'created_at',
        'updated_at'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function survey()
    {
        return $this->belongsTo(Survey::class);
    }

    public function syllabus()
    {
        return $this->belongsTo(Syllabus::class);
    }

    public function surveyAnswers()
    {
        return $this->hasMany(SurveyAnswer::class);
    }

    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(Lecturer::class);
    }
}
