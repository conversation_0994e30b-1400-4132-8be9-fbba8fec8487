<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\SurveySettingType
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveySetting[] $survey
 * @property-read int|null $survey_count
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySettingType whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SurveySettingType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name'
    ];

    public function survey()
    {
        return $this->hasMany(SurveySetting::class);
    }
}
