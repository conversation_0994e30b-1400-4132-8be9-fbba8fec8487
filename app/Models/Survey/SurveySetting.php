<?php

namespace App\Models\Survey;

use App\Models\Reestry\School;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\SurveySetting
 *
 * @property int $id
 * @property int $survey_id
 * @property int $survey_setting_type_id
 * @property int|null $school_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read School|null $school
 * @property-read \App\Models\Survey\Survey|null $survey
 * @property-read \App\Models\Survey\SurveySettingType|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereSurveyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereSurveySettingTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveySetting whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SurveySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'survey_id',
        'survey_setting_type_id',
        'school_id'
    ];

    public function survey()
    {
        return $this->belongsTo(Survey::class);
    }

    public function type()
    {
        return $this->belongsTo(SurveySettingType::class);
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }
}
