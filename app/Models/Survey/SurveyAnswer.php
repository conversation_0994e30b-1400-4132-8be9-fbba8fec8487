<?php

namespace App\Models\Survey;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\SurveyAnswer
 *
 * @property int $id
 * @property int $survey_activation_id
 * @property int $survey_question_id
 * @property int $answer_int
 * @property string|null $answer_string
 * @property string|null $comment
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Survey\SurveyActivation|null $activation
 * @property-read \App\Models\Survey\SurveyQuestion|null $question
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereAnswerInt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereAnswerString($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereSurveyActivationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereSurveyQuestionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyAnswer whereUpdatedAt($value)
 * @property-read \App\Models\Survey\SurveyActivation|null $surveyActivation
 * @property-read \App\Models\Survey\SurveyQuestion|null $surveyQuestion
 * @mixin \Eloquent
 */
class SurveyAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'survey_activation_id',
        'survey_question_id',
        'answer_int',
        'answer_string',
        'comment'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function surveyActivation()
    {
        return $this->belongsTo(SurveyActivation::class);
    }

    public function surveyQuestion()
    {
        return $this->belongsTo(SurveyQuestion::class);
    }
}
