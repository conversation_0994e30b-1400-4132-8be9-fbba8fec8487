<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\SurveyQuestion
 *
 * @property int $id
 * @property int $survey_id
 * @property int $survey_question_type_id
 * @property string $name
 * @property int $question_required
 * @property int $comment_required
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyAnswer[] $answer
 * @property-read int|null $answer_count
 * @property-read \App\Models\Survey\Survey|null $survey
 * @property-read \App\Models\Survey\SurveyQuestionType|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereCommentRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereQuestionRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereSurveyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereSurveyQuestionTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestion whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SurveyQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'survey_id',
        'survey_question_type_id',
        'name',
        'question_required',
        'comment_required'
    ];

    public function survey()
    {
        return $this->belongsTo(Survey::class);
    }

    public function type()
    {
        return $this->belongsTo(SurveyQuestionType::class);
    }

    public function answer()
    {
        return $this->hasMany(SurveyAnswer::class);
    }
}
