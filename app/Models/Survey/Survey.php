<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\Survey
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyActivation[] $activation
 * @property-read int|null $activation_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyQuestion[] $questions
 * @property-read int|null $questions_count
 * @property-read \App\Models\Survey\SurveySetting|null $setting
 * @method static \Illuminate\Database\Eloquent\Builder|Survey newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Survey newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Survey query()
 * @method static \Illuminate\Database\Eloquent\Builder|Survey whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survey whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survey whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survey whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Survey whereUpdatedAt($value)
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyActivation[] $surveyActivations
 * @property-read int|null $survey_activations_count
 * @mixin \Eloquent
 */
class Survey extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type' //1=საგნობრივი, 2=ზოგადი
    ];

    public function setting()
    {
        return $this->hasOne(SurveySetting::class);
    }

    public function questions()
    {
        return $this->hasMany(SurveyQuestion::class);
    }

    public function activations()
    {
        return $this->hasMany(SurveyActivation::class);
    }
}
