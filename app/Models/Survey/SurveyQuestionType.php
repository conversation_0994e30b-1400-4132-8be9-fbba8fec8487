<?php

namespace App\Models\Survey;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Survey\SurveyQuestionType
 *
 * @property int $id
 * @property string $name
 * @property int $type 0=integer value;1=string value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Survey\SurveyQuestion[] $survey
 * @property-read int|null $survey_count
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType query()
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SurveyQuestionType whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SurveyQuestionType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type'
    ];

    public function survey()
    {
        return $this->hasMany(SurveyQuestion::class);
    }
}
