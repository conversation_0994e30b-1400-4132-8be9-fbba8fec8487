<?php

namespace App\Models\HR\Lecturer\Invited;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $father_name
 * @property int|null $gender
 * @property int|null $family_state
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Lecturer\Invited\HrInvitedLectureAttachment[] $hrInvitedLectureAttachments
 * @property-read int|null $hr_invited_lecture_attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Lecturer\Invited\HrInvitedLectureEducation[] $hrInvitedLectureEducations
 * @property-read int|null $hr_invited_lecture_educations_count
 * @property-read \App\Models\HR\Lecturer\Invited\HrInvitedLecturePosition|null $hrInvitedLecturePosition
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereFamilyState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereFatherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereUserId($value)
 * @property int|null $age
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureInfo whereAge($value)
 * @mixin \Eloquent
 */
class HrInvitedLectureInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'father_name',
        'gender',
        'age',
        'family_state',
    ];

    public function hrInvitedLectureAttachments(): HasMany
    {
        return $this->hasMany(HrInvitedLectureAttachment::class);
    }

    public function hrInvitedLectureEducations(): HasMany
    {
        return $this->hasMany(HrInvitedLectureEducation::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function hrInvitedLecturePosition(): HasOne
    {
        return $this->hasOne(HrInvitedLecturePosition::class);
    }
}
