<?php

namespace App\Models\HR\Lecturer\Invited;

use App\Models\Reestry\Program\Program;
use App\Models\Reestry\School;
use App\Models\WorkType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Invited\HrInvitedLecturePosition
 *
 * @property int $id
 * @property int $hr_invited_lecture_info_id
 * @property int|null $salary
 * @property string|null $direction
 * @property int|null $school_id
 * @property int|null $program_id
 * @property string|null $course
 * @property int|null $work_type_id
 * @property string|null $workplace_name
 * @property string|null $position
 * @property int|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo|null $hrInvitedLectureInfo
 * @property-read Program|null $program
 * @property-read School|null $school
 * @property-read WorkType|null $workType
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereCourse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereHrInvitedLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereWorkTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLecturePosition whereWorkplaceName($value)
 * @mixin \Eloquent
 */
class HrInvitedLecturePosition extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_invited_lecture_info_id',
        'salary',
        'direction',
        'school_id',
        'position',
        'program_ids',
        'course',
        'work_type_id',
        'workplace_name',
        'status',
        'position',
        'cv_georgian',
        'cv_english',
        'id_card',
        'diploma',
        'scientific_works',
        'certificate',
    ];

    protected $casts = [
        'program_ids' => 'array',
    ];

    public function hrInvitedLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrInvitedLectureInfo::class);
    }

    public function workType(): BelongsTo
    {
        return $this->belongsTo(WorkType::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }
}
