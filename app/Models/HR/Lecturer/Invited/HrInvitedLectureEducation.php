<?php

namespace App\Models\HR\Lecturer\Invited;

use App\Models\Reestry\AcademicDegree;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Invited\HrInvitedLectureEducation
 *
 * @property int $id
 * @property int $hr_invited_lecture_info_id
 * @property int|null $academic_degree_id
 * @property string|null $qualification
 * @property string|null $country
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree|null $academicDegree
 * @property-read \App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo|null $hrInvitedLectureInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereHrInvitedLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereQualification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrInvitedLectureEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_invited_lecture_info_id',
        'academic_degree_id',
        'qualification',
        'country'
    ];

    public function hrInvitedLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrInvitedLectureInfo::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }
}
