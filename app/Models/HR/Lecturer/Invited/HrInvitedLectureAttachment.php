<?php

namespace App\Models\HR\Lecturer\Invited;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Invited\HrInvitedLectureAttachment
 *
 * @property int $id
 * @property int $hr_invited_lecture_info_id
 * @property string $filename
 * @property string|null $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo|null $hrInvitedLectureInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereHrInvitedLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrInvitedLectureAttachment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrInvitedLectureAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_invited_lecture_info_id',
        'filename',
        'name'
    ];

    public function hrInvitedLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrInvitedLectureInfo::class);
    }
}
