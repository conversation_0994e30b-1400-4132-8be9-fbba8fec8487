<?php

namespace App\Models\HR\Lecturer\Academic;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\HR\Lecturer\Academic\LecturerCategory
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerCategory whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class LecturerCategory extends Model
{
    use HasFactory;

    const TYPES = [
        'ა',
        'ბ',
        'გ'
    ];

    protected $fillable = [
        'title'
    ];
}
