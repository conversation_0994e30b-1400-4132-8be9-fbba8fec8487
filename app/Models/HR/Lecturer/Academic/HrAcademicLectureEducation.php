<?php

namespace App\Models\HR\Lecturer\Academic;

use App\Models\Reestry\AcademicDegree;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Academic\HrAcademicLectureEducation
 *
 * @property int $id
 * @property int $hr_academic_lecture_info_id
 * @property string|null $qualification
 * @property string|null $country
 * @property int|null $academic_degree_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo|null $hrAcademicLectureInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereHrAcademicLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereQualification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrAcademicLectureEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'academic_degree_id',
        'qualification',
        'country',
        'hr_academic_lecture_info_id'
    ];

    public function hrAcademicLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrAcademicLectureInfo::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class,'academic_degree_id', 'id');
    }
}
