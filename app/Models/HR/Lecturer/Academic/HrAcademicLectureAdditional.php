<?php

namespace App\Models\HR\Lecturer\Academic;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Academic\HrAcademicLectureAdditional
 *
 * @property int $id
 * @property int $hr_academic_lecture_info_id
 * @property int|null $scopus_g
 * @property int|null $scopus_h
 * @property int|null $web_of_science_g
 * @property int|null $web_of_science_h
 * @property int|null $google_scholar_g
 * @property int|null $google_scholar_h
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo|null $hrAcademicLectureInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereGoogleScholarG($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereGoogleScholarH($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereHrAcademicLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereScopusG($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereScopusH($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereWebOfScienceG($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAdditional whereWebOfScienceH($value)
 * @mixin \Eloquent
 */
class HrAcademicLectureAdditional extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_academic_lecture_info_id',
        'scopus_g',
        'scopus_h',
        'web_of_science_g',
        'web_of_science_h',
        'google_scholar_g',
        'google_scholar_h',
    ];

    public function hrAcademicLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrAcademicLectureInfo::class);
    }
}
