<?php

namespace App\Models\HR\Lecturer\Academic;

use App\Models\Reestry\School;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\HR\Lecturer\Academic\HrAcademicLecturePosition
 *
 * @property int $id
 * @property int $hr_academic_lecture_info_id
 * @property int|null $lecturer_position_id
 * @property int|null $grant
 * @property int|null $affiliated
 * @property int|null $lecturer_category_id
 * @property int|null $salary
 * @property int|null $paid_hours
 * @property int|null $unpaid_hours
 * @property string|null $direction
 * @property int|null $school_id
 * @property string|null $appointment
 * @property string|null $vacancy_command_number
 * @property string|null $vacancy_command_number_file
 * @property \Illuminate\Support\Carbon|null $vacancy_command_number_date
 * @property string|null $appointment_command_number
 * @property string|null $appointment_command_number_file
 * @property \Illuminate\Support\Carbon|null $appointment_command_number_date
 * @property \Illuminate\Support\Carbon|null $contract_start
 * @property \Illuminate\Support\Carbon|null $contract_end
 * @property string|null $contract_period
 * @property int|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo|null $hrAcademicLectureInfo
 * @property-read \App\Models\HR\Lecturer\Academic\LecturerCategory|null $lecturerCategory
 * @property-read \App\Models\HR\Lecturer\Academic\LecturerPosition|null $lecturerPosition
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereAffiliated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereAppointment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereAppointmentCommandNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereAppointmentCommandNumberDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereAppointmentCommandNumberFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereContractEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereContractPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereContractStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereGrant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereHrAcademicLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereLecturerCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereLecturerPositionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition wherePaidHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereUnpaidHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereVacancyCommandNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereVacancyCommandNumberDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLecturePosition whereVacancyCommandNumberFile($value)
 * @mixin \Eloquent
 */
class HrAcademicLecturePosition extends Model
{
    use HasFactory;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::saving(function ($position) {
            // If contract_start and contract_end are set, calculate contract_period
            if ($position->contract_start && $position->contract_end) {
                $position->contract_period = $position->calculateContractDuration();
            }
        });
    }

    protected $fillable = [
        'hr_academic_lecture_info_id',
        'lecturer_position_id',
        'grant',
        'affiliated',
        'lecturer_category_id',
        'salary',
        'paid_hours',
        'unpaid_hours',
        'direction',
        'school_id',
        'appointment',
        'vacancy_command_number',
        'vacancy_command_number_file',
        'vacancy_command_number_date',
        'appointment_command_number',
        'appointment_command_number_file',
        'appointment_command_number_date',
        'commission_file',
        'vacancy_document_file',
        'contract_start',
        'contract_end',
        'contract_period',
        'status',

        'cv_georgian',
        'cv_english',
        'id_card',
        'diploma',
        'scientific_works',
        'certificate',
    ];

    protected $casts = [
        'contract_start' => 'date:d-m-Y',
        'contract_end' => 'date:d-m-Y',
        'appointment_command_number_date' => 'date:d-m-Y',
        'vacancy_command_number_date' => 'date:d-m-Y',
    ];

    public function hrAcademicLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrAcademicLectureInfo::class);
    }

    public function lecturerCategory(): BelongsTo
    {
        return $this->belongsTo(LecturerCategory::class);
    }

    public function lecturerPosition(): BelongsTo
    {
        return $this->belongsTo(LecturerPosition::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Calculate contract duration in years
     *
     * @return string Duration in years (with decimal for months)
     */
    public function calculateContractDuration()
    {
        if (!$this->contract_start || !$this->contract_end) {
            return null;
        }

        $start = new \DateTime($this->contract_start);
        $end = new \DateTime($this->contract_end);
        $interval = $start->diff($end);

        $years = $interval->y;
        $months = $interval->m;

        if ($months > 0) {
            $decimalYears = $years + ($months / 12);
            return number_format($decimalYears, 1);
        }

        return (string)$years;
    }
}
