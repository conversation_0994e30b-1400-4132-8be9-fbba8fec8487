<?php

namespace App\Models\HR\Lecturer\Academic;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\HR\Lecturer\Academic\LecturerPosition
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition query()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerPosition whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class LecturerPosition extends Model
{
    use HasFactory;

    const TYPES = [
        'პროფესორი',
        'ასოცირებული პროფესორი',
        'ასისტენტ პროფესორი',
        'ასისტენტი'
    ];

    protected $fillable = [
        'title'
    ];
}
