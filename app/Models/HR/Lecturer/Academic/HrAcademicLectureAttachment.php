<?php

namespace App\Models\HR\Lecturer\Academic;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Lecturer\Academic\HrAcademicLectureAttachment
 *
 * @property int $id
 * @property int $hr_academic_lecture_info_id
 * @property string $filename
 * @property string|null $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo|null $hrAcademicLectureInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereHrAcademicLectureInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureAttachment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrAcademicLectureAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'hr_academic_lecture_info_id',
        'name'
    ];

    public function hrAcademicLectureInfo(): BelongsTo
    {
        return $this->belongsTo(HrAcademicLectureInfo::class);
    }
}
