<?php

namespace App\Models\HR\Lecturer\Academic;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $father_name
 * @property int|null $gender 0 - ქალი , 1 - კაცი
 * @property int|null $age
 * @property int|null $family_state 0 - დასაოჯახებელი , 1 - დაოჯახებული
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLectureAdditional|null $hrAcademicLectureAdditional
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Lecturer\Academic\HrAcademicLectureAttachment[] $hrAcademicLectureAttachments
 * @property-read int|null $hr_academic_lecture_attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Lecturer\Academic\HrAcademicLectureEducation[] $hrAcademicLectureEducations
 * @property-read int|null $hr_academic_lecture_educations_count
 * @property-read \App\Models\HR\Lecturer\Academic\HrAcademicLecturePosition|null $hrAcademicLecturePosition
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereAge($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereFamilyState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereFatherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAcademicLectureInfo whereUserId($value)
 * @mixin \Eloquent
 */
class HrAcademicLectureInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'father_name',
        'gender',
        'age',
        'family_state',
        'user_id'
    ];

    public function hrAcademicLectureEducations(): HasMany
    {
        return $this->hasMany(HrAcademicLectureEducation::class);
    }

    public function hrAcademicLectureAttachments(): HasMany
    {
        return $this->hasMany(HrAcademicLectureAttachment::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function hrAcademicLecturePosition(): HasOne
    {
        return $this->hasOne(HrAcademicLecturePosition::class);
    }

    public function hrAcademicLectureAdditional(): HasOne
    {
        return $this->hasOne(HrAcademicLectureAdditional::class);
    }

}
