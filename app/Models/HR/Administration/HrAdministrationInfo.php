<?php

namespace App\Models\HR\Administration;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\HR\Administration\HrAdministrationInfo
 *
 * @property int $id
 * @property string|null $father_name
 * @property int|null $gender 0 - ქალი , 1 - კაცი
 * @property string|null $date_of_birth
 * @property int|null $age
 * @property string|null $address
 * @property string|null $phone
 * @property int|null $family_state დაუოჯახებელი - 0 , დასაოჯახებელი - 1
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Administration\HrAdministrationAppointment|null $hrAdministrationAppointment
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Administration\HrAdministrationEducation[] $hrAdministrationEducations
 * @property-read int|null $hr_administration_educations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\HR\Administration\HrAdministrationFile[] $hrAdministrationFiles
 * @property-read int|null $hr_administration_files_count
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereAge($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereFamilyState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereFatherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationInfo whereUserId($value)
 * @mixin \Eloquent
 */
class HrAdministrationInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'father_name',
        'gender',
        'address',
        'age',
        'phone',
        'family_state',
        'user_id',
        'address',
        'date_of_birth'
    ];


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function hrAdministrationFiles(): HasMany
    {
        return $this->hasMany(HrAdministrationFile::class);
    }

    public function hrAdministrationEducations(): HasMany
    {
        return $this->hasMany(HrAdministrationEducation::class);
    }

    public function hrAdministrationAppointment(): BelongsTo
    {
        return $this->belongsTo(
            HrAdministrationAppointment::class,
            'id',
            'hr_administration_info_id',
        );
    }

}
