<?php

namespace App\Models\HR\Administration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Administration\HrAdministrationAppointment
 *
 * @property int $id
 * @property int|null $appointment კონკურსი - 0  , რეკომენდაცია - 1
 * @property string|null $vacancy_command_number
 * @property string|null $vacancy_command_number_file
 * @property string|null $appointment_command_number
 * @property string|null $appointment_command_number_file
 * @property string|null $appointment_command_number_date
 * @property string|null $vacancy_command_number_date
 * @property string|null $position
 * @property int|null $type_of_position
 * @property string|null $contract_start
 * @property string|null $contract_end
 * @property string|null $vacation
 * @property string|null $day_off
 * @property int|null $status 0 - არააქტიური , 1 - აქტიური
 * @property int|null $educational_staff
 * @property int $hr_administration_info_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Administration\HrAdministrationInfo|null $hrAdministrationInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereAppointment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereAppointmentCommandNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereAppointmentCommandNumberDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereAppointmentCommandNumberFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereContractEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereContractStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereDayOff($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereEducationalStaff($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereHrAdministrationInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereTypeOfPosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereVacancyCommandNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereVacancyCommandNumberDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereVacancyCommandNumberFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereVacation($value)
 * @property string|null $contract_period
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationAppointment whereContractPeriod($value)
 * @mixin \Eloquent
 */
class HrAdministrationAppointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment',
        'vacancy_command_number',
        'vacancy_command_number_file',
        'vacancy_command_number_date',
        'appointment_command_number',
        'appointment_command_number_file',
        'appointment_command_number_date',
        'contract_start',
        'contract_end',
        'position',
        'type_of_position',
        'vacation',
        'day_off',
        'status',
        'educational_staff',
        'hr_administration_info_id',
        'cv_georgian',
        'cv_english',
        'id_card',
        'diploma',
        'scientific_works',
        'certificate',
        'disciplinary_remark_file',
        'disciplinary_complaint_file',
        'disciplinary_compensation_file',
        'disciplinary_contract_termination_file',
    ];

    public function hrAdministrationInfo(): BelongsTo
    {
        return $this->belongsTo(HrAdministrationInfo::class);
    }
}
