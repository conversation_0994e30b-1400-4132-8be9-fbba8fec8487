<?php

namespace App\Models\HR\Administration;

use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Administration\AdministrationItem;
use App\Models\Reestry\School;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Administration\HrAdministrationEducation
 *
 * @property int $id
 * @property int|null $academic_degree_id
 * @property int $hr_administration_info_id
 * @property string|null $country
 * @property string|null $qualification
 * @property string|null $position
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree|null $academicDegree
 * @property-read \App\Models\HR\Administration\HrAdministrationInfo|null $hrAdministrationInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereHrAdministrationInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereQualification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrAdministrationEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_administration_info_id',
        'academic_degree_id',
        'qualification',
        'country',
        'position',
    ];

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }

    public function hrAdministrationInfo(): BelongsTo
    {
        return $this->belongsTo(HrAdministrationInfo::class);
    }
}
