<?php

namespace App\Models\HR\Administration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\HR\Administration\HrAdministrationFile
 *
 * @property int $id
 * @property int $hr_administration_info_id
 * @property string $filename
 * @property string|null $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\HR\Administration\HrAdministrationInfo|null $hrAdministrationInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile query()
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereHrAdministrationInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HrAdministrationFile whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class HrAdministrationFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'hr_administration_info_id',
        'filename',
        'name'
    ];

    public function hrAdministrationInfo(): BelongsTo
    {
        return $this->belongsTo(HrAdministrationInfo::class);
    }
}
