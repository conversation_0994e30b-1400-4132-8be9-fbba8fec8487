<?php

namespace App\Models;

use App\Models\Reestry\Student\Student;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasFilters;

/**
 * App\Models\FinanceStudentStatusLog
 *
 * @property int $id
 * @property float|null $amount
 * @property int $student_id
 * @property int $status_id 1: Activate status 2: Suspend status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Student $student
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceStudentStatusLog whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FinanceStudentStatusLog extends Model
{
    use HasFactory, HasFilters;

    protected $table = 'finance_student_status_logs';

    protected $fillable = ['amount', 'student_id', 'status_id'];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }
}
