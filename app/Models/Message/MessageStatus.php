<?php

namespace App\Models\Message;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Message\MessageStatus
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Message\MessageAddress[] $messageAddresses
 * @property-read int|null $message_addresses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Message\Message[] $messages
 * @property-read int|null $messages_count
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageStatus whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MessageStatus extends Model
{
    use HasFactory;

    const ACTIVE = 1;
    const FAVORITE = 2;
    const DELETED = 3;

    protected $fillable = [
        'title'
    ];


    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    public function messageAddresses(): HasMany
    {
        return $this->hasMany(MessageAddress::class);
    }
}
