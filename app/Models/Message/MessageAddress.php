<?php

namespace App\Models\Message;

use App\Models\User\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Message\MessageAddress
 *
 * @property int $id
 * @property int $message_id
 * @property int $user_id
 * @property string|null $viewed_at
 * @property int $message_status_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Message\Message|null $message
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress active()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress favorite()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress newQuery()
 * @method static \Illuminate\Database\Query\Builder|MessageAddress onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress trash()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereMessageStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAddress whereViewedAt($value)
 * @method static \Illuminate\Database\Query\Builder|MessageAddress withTrashed()
 * @method static \Illuminate\Database\Query\Builder|MessageAddress withoutTrashed()
 * @mixin \Eloquent
 */
class MessageAddress extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'message_id',
        'user_id',
        'message_status_id',
        'viewed_at',
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    public function scopeActive($query)
    {
        return $query->where('message_status_id', MessageStatus::ACTIVE);
    }

    public function scopeFavorite($query)
    {
        return $query->where('message_status_id', MessageStatus::FAVORITE);
    }

    public function scopeTrash($query)
    {
        return $query->where('message_status_id', MessageStatus::DELETED);
    }
}
