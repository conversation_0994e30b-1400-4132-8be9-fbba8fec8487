<?php

namespace App\Models\Message;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Message\MessageAttachment
 *
 * @property int $id
 * @property int $message_id
 * @property string $filename
 * @property string $original_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Message\Message|null $message
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment query()
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereOriginalName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MessageAttachment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MessageAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'filename',
        'original_name'
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }
}
