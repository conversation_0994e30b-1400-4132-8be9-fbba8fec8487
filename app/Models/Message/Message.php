<?php

namespace App\Models\Message;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * App\Models\Message\Message
 *
 * @property int $id
 * @property string|null $title if null it is reply
 * @property string|null $body
 * @property int $author_id
 * @property int $message_status_id
 * @property int|null $main_message_id if null, it is main message, otherwise - reply
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Message\MessageAddress[] $addresses
 * @property-read int|null $addresses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Message\MessageAttachment[] $attachments
 * @property-read int|null $attachments_count
 * @property-read User|null $author
 * @property-read \Illuminate\Database\Eloquent\Collection|Message[] $replies
 * @property-read int|null $replies_count
 * @method static \Illuminate\Database\Eloquent\Builder|Message active()
 * @method static \Illuminate\Database\Eloquent\Builder|Message favorite()
 * @method static \Illuminate\Database\Eloquent\Builder|Message newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Message newQuery()
 * @method static \Illuminate\Database\Query\Builder|Message onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Message query()
 * @method static \Illuminate\Database\Eloquent\Builder|Message trash()
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereAuthorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereMainMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereMessageStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Message whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Message withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Message withoutTrashed()
 * @mixin \Eloquent
 */
class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'body',
        'author_id',
        'message_status_id',
        'main_message_id',
        'updated_at'
    ];

    /**
     * Get the created_at attribute and convert it to the Tbilisi timezone.
     *
     * @param string $value
     * @return string
     */
    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Tbilisi')->toDateTimeString();
    }

    public function scopeActive($query)
    {
        return $query->where('message_status_id', MessageStatus::ACTIVE);
    }

    public function scopeFavorite($query)
    {
        return $query->where('message_status_id', MessageStatus::FAVORITE);
    }

    public function scopeTrash($query)
    {
        return $query->where('message_status_id', MessageStatus::DELETED);
    }

    public function replies(): hasMany
    {
        return $this->hasMany(Message::class, 'main_message_id', 'id')
            ->with(['attachments','author']);
    }

    public function author(): belongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function addresses(): hasMany
    {
//      todo: most likely it wont work
        return $this->hasMany(
            MessageAddress::class,
            'message_id',
            'id'
        );
    }

    public function attachments(): HasMany
    {
        return $this->hasMany(
            MessageAttachment::class,
            'message_id',
            'id'
        );
    }

}
