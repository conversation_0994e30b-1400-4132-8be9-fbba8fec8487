<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\WorkType
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkType whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class WorkType extends Model
{
    use HasFactory;

    const TYPES = [
        'საჯარო',
        'კერძო',
        'საერთაშორისო',
        'მედია',
        'NGO'
    ];

    protected $fillable = [
        'title'
    ];

}
