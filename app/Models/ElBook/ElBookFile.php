<?php

namespace App\Models\ElBook;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ElBook\ElBookFile
 *
 * @property int $id
 * @property int $el_book_id
 * @property string $filename
 * @property string $path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ElBook\ElBook|null $elBook
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile query()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile whereElBookId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookFile whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ElBookFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'el_book_id',
        'path'
    ];

    public function elBook(): BelongsTo
    {
        return $this->belongsTo(ElBook::class);
    }
}
