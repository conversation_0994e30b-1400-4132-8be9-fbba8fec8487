<?php

namespace App\Models\ElBook;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Topic;
use App\Traits\HasFilters;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\ElBook\ElBook
 *
 * @property int $id
 * @property string $title
 * @property string|null $author
 * @property string|null $subject
 * @property int|null $lecturer_id
 * @property \Illuminate\Support\Carbon|null $published_date
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ElBook\ElBookFile[] $files
 * @property-read int|null $files_count
 * @property-read Lecturer|null $lecturer
 * @property-read \Illuminate\Database\Eloquent\Collection|Topic[] $topics
 * @property-read int|null $topics_count
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook query()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereAuthor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereLecturerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook wherePublishedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBook whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ElBook extends Model
{
    use HasFactory, HasFilters;

    protected $fillable = [
        'title',
        'author',
        'subject',
        'lecturer_id',
        'topic',
        'published_date',
        'source',
        'file_name'
    ];
//
    protected $casts = [
        'published_date' => 'date:d-m-Y'
    ];

    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(Lecturer::class);
    }

    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(ElBookFile::class);
    }
}
