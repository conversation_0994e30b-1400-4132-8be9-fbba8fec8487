<?php

namespace App\Models\Scopes;

use App\Models\Own;
use App\Models\User\UserFullAccess;
use App\Models\User\UserType;
use App\Models\UserProgram;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class AccessibleScope implements Scope
{
    public $model;

    public function __construct($model)
    {
        $this->model = $model;
    }

    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @param \Illuminate\Database\Eloquent\Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $builder;
    }
}
