<?php

namespace App\Models;

use App\Models\Reestry\Program\Program;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\UserProgram
 *
 * @property int $program_id
 * @property int $user_id
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram whereUserId($value)
 * @property int $id
 * @property-read Program|null $program
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserProgram whereId($value)
 * @mixin \Eloquent
 */
class UserProgram extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'program_id'
    ];

    protected $table = 'user_program';

    public $timestamps = false;

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
