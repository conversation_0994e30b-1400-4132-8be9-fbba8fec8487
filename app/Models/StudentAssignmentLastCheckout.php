<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\StudentAssignmentLastCheckout
 *
 * @property int $id
 * @property int $student_assignment_id
 * @property int $status_id
 * @property int $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereStudentAssignmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignmentLastCheckout whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StudentAssignmentLastCheckout extends Model
{
    use HasFactory;

    protected $table = 'student_assignment_last_checkouts';

    protected $fillable = [
        'student_assignment_id',
        'status_id',
        'is_active'
    ];
}
