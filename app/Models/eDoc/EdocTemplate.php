<?php

namespace App\Models\eDoc;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\eDoc\EdocTemplate
 *
 * @property int $id
 * @property string $name
 * @property string|null $index
 * @property string|null $text
 * @property int $automatic 0=ადმინისტრატორის მიერ ცნობის გენერირება; 1=ავტომატურად გენერირება
 * @property int $user_type_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\eDoc\eDoc[] $edoc
 * @property-read int|null $edoc_count
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereAutomatic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereUserTypeId($value)
 * @property int|null $signature
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereSignature($value)
 * @property int $is_active
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereIsActive($value)
 * @property int $lang 0=ქართული; 1=ინგლისური
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereLang($value)
 * @property string|null $file_name
 * @property string|null $description
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EdocTemplate whereFileName($value)
 * @mixin \Eloquent
 */
class EdocTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'index',
        'text',
        'automatic',
        'lang', // 0=ქართული; 1=ინგლისური
        'is_active',
        'user_type_id',
        'signature',
    ];

    public function edoc(): HasMany
    {
        return $this->hasMany(eDoc::class);
    }
}
