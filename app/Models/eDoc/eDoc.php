<?php

namespace App\Models\eDoc;

use App\Models\Reestry\Student\Student;
use App\Models\User\User;
use App\Models\UserProgram;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\eDoc\eDoc
 *
 * @property int $id
 * @property string|null $document_number connected with template index
 * @property int $user_id
 * @property int $edoc_template_id
 * @property int|null $created_by administrator user_id
 * @property int $created
 * @property string|null $text
 * @property \Illuminate\Support\Carbon|null $opened_at
 * @property string|null $comment
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\eDoc\EdocTemplate|null $template
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc query()
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereCreated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereEdocTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereOpenedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereUserId($value)
 * @property int $status 1=pending;2=approved;3=declined
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc filter(\App\Filters\QueryFilters $filters)
 * @property int $stamp
 * @method static \Illuminate\Database\Eloquent\Builder|eDoc whereStamp($value)
 * @mixin \Eloquent
 */
class eDoc extends Model
{
    use HasFilters, HasFilters;

    protected $fillable = [
        'user_id',
        'edoc_template_id',
        'created',
        'created_by',
        'text',
        'opened_at',
        'comment',
        'status' //1=pending;2=approved;3=declined
    ];

    protected $dates = ['updated_at'];

    protected $casts = [
        'opened_at' => 'datetime:d/m/Y H:i:s',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(EdocTemplate::class, 'edoc_template_id', 'id');
    }

    public function scopeAdminProgramScope(Builder $query): void
    {
        $user = auth()->user();
        if ($user->is_super_admin == 0)
        {
            $userId = $user->id;
            $userProgramIds = UserProgram::whereUserId($userId)->pluck('program_id')->toArray();
            $userIds = Student::whereIn('program_id', $userProgramIds)->pluck('user_id')->toArray();

            $query->whereIn('user_id', $userIds);
        }
    }


}
