<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ElBookTopic
 *
 * @property int $id
 * @property int $el_book_id
 * @property int $topic_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic query()
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic whereElBookId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ElBookTopic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ElBookTopic extends Model
{
    use HasFactory;

    protected $table = 'el_book_topic';

    protected $fillable = [
        'el_book_id',
        'topic_id'
    ];
}
