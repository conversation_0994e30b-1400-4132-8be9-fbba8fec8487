<?php

namespace App\Models\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Master\MasterEducation
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $university
 * @property string $faculty
 * @property int $start_date
 * @property int $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereFaculty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterEducation extends Model
{
    use HasFactory;

    protected $table = 'master_register_education';

    protected $fillable = [
        'master_register_id',
        'university',
        'faculty',
        'start_date',
        'end_date'
    ];

    public function masterRegister(): BelongsTo
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
