<?php

namespace App\Models\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Master\MasterLanguage
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $language
 * @property string $level
 * @property int $certificate
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereCertificate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterLanguage whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterLanguage extends Model
{
    use HasFactory;

    protected $fillable = [
        'language',
        'level',
        'certificate',
        'master_register_id'
    ];

    public function masterRegister(): BelongsTo
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
