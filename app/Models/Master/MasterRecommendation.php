<?php

namespace App\Models\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Master\MasterRecommendation
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $person
 * @property string $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation wherePerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRecommendation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterRecommendation extends Model
{
    use HasFactory;

    protected $table = 'master_recommendations';

    protected $fillable = [
        'master_register_id',
        'person',
        'phone'
    ];

    public function masterRegister(): BelongsTo
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
