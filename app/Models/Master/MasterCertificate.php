<?php

namespace App\Models\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MasterCertificate
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterCertificate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterCertificate extends Model
{
    use HasFactory;

    protected $table = 'master_register_certificates';

    protected $fillable = [
        'master_register_id',
        'title'
    ];

    public function masterRegister()
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
