<?php

namespace App\Models;

use App\Models\Reestry\Student\Student;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TempUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'personal_id',
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'personal_id', 'personal_id');
    }
}
