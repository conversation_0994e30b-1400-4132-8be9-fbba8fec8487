<?php

namespace App\Models;

use App\Models\Scopes\AccessibleScope;
use App\Models\Syllabus\Syllabus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Assignment
 *
 * @property int $id
 * @property int $assessment_component_id
 * @property int $parent_id
 * @property int $syllabus_id
 * @property int $score
 * @property int $min_score
 * @property string $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $calculation_type
 * @property-read \App\Models\AssessmentComponent $assessmentComponent
 * @property-read Syllabus $syllabus
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereAssessmentComponentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereCalculationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereMinScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereUpdatedAt($value)
 * @property string|null $exam_date
 * @property string|null $expiration_date
 * @property string|null $description_en
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Assignment> $assignments
 * @property-read int|null $assignments_count
 * @property-read \App\Models\AssessmentComponent|null $parentComponent
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereDescriptionEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereExamDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Assignment whereExpirationDate($value)
 * @mixin \Eloquent
 */
class Assignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'assessment_component_id',
        'parent_id',
        'calculation_type',
        'syllabus_id',
        'exam_date',
        'expiration_date',
        'score',
        'min_score',
        'description',
        'description_en',
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function syllabus(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }

    public function assessmentComponent(): BelongsTo
    {
        return $this->belongsTo(AssessmentComponent::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class, 'parent_id', 'id');
    }

    public function parentComponent()
    {
        return $this->belongsTo(AssessmentComponent::class, 'parent_id', 'id');
    }
}
