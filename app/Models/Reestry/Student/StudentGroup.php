<?php

namespace App\Models\Reestry\Student;

use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use App\Models\StudentGroupStudent;
use App\Traits\HasFilters;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\StudentGroup
 *
 * @property int $id
 * @property string $name
 * @property int $program_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Reestry\Program\Program $program
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup newQuery()
 * @method static \Illuminate\Database\Query\Builder|StudentGroup onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|StudentGroup withTrashed()
 * @method static \Illuminate\Database\Query\Builder|StudentGroup withoutTrashed()
 * @property string $name_ka
 * @property string $name_en
 * @property-read \Illuminate\Database\Eloquent\Collection|LearnYear[] $learnYears
 * @property-read int|null $learn_years_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\Student[] $students
 * @property-read int|null $students_count
 * @method static \Database\Factories\Reestry\Student\StudentGroupFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroup whereNameKa($value)
 * @mixin \Eloquent
 */
class StudentGroup extends Model
{
    use HasFactory;
    use softDeletes;
    use HasFilters;

    protected $fillable = ['name_en', 'name_ka', 'program_id', 'deleted_at'];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function learnYears(): HasMany
    {
        return $this->hasMany(LearnYear::class, 'program_id', 'program_id');
    }

    public function getCreatedAtAttribute($value)
    {
        $date = Carbon::parse($value);
        return $date->format('Y-m-d H:i');
    }

    public function getUpdatedAtAttribute($value)
    {
        $date = Carbon::parse($value);
        return $date->format('Y-m-d H:i');
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(
            Student::class,
            'student_group_students'
        );
    }

}
