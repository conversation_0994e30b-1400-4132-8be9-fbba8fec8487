<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentLearnYearHistory
 *
 * @property int $id
 * @property int $student_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentLearnYearHistory whereUpdatedAt($value)
 * @property int $learn_year_id
 * @property-read \App\Models\Reestry\Student\Student $student
 * @method static Builder|StudentLearnYearHistory whereLearnYearId($value)
 * @mixin \Eloquent
 */
class StudentLearnYearHistory extends Model
{
    use HasFactory;

    protected $guarded = [];

//    protected static function booted()
//    {
//        static::addGlobalScope('accessible', function (Builder $builder) {
//            if (Auth::user()->is_super_admin || Auth::user()->hasModel(Own::MODELS['Student'])) {
//                return $builder;
//            } else if (Auth::user()->hasPrograms()) {
//                return $builder->whereHas('student', function ($query) {
//                    return $query->whereIn('program_id', Auth::user()->programs()->pluck('id'));
//                });
//            } else {
//                return $builder->where('created_at', '<=', now()->subYears(20));
//            }
//        });
//    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
}
