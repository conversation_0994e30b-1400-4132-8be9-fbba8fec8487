<?php

namespace App\Models\Reestry\Student;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Reestry\Student\StudentOldMark
 *
 * @method static \Illuminate\Database\Eloquent\Builder|StudentOldMark newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentOldMark newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentOldMark query()
 * @mixin \Eloquent
 */
class StudentOldMark extends Model
{
    use HasFactory;

    protected $table = 'shefaseba_old';
}
