<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentMobilityStatusHistory
 *
 * @property int $id
 * @property int $status
 * @property int $student_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentMobilityStatusHistory whereUpdatedAt($value)
 * @property-read \App\Models\Reestry\Student\Student $student
 * @mixin \Eloquent
 */
class StudentMobilityStatusHistory extends Model
{

    use HasFactory;

    protected $fillable = ['student_id', 'status'];

//    protected static function booted()
//    {
//        static::addGlobalScope('accessible', function (Builder $builder) {
//            if (Auth::user()->is_super_admin || Auth::user()->hasModel(Own::MODELS['Student'])) {
//                return $builder;
//            }
//            else if (Auth::user()->hasPrograms()) {
//                return $builder->whereHas('student', function ($query) {
//                    return $query->whereIn('program_id', Auth::user()->programs()->pluck('id'));
//                });
//            }
//            else {
//                return $builder->where('created_at', '<=', now()->subYears(20));
//            }
//        });
//    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

}
