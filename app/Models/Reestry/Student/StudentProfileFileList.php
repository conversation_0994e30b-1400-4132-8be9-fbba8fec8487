<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentProfileFileList
 *
 * @property int $id
 * @property int $student_id
 * @property string $file_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList newQuery()
 * @method static \Illuminate\Database\Query\Builder|StudentProfileFileList onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProfileFileList whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|StudentProfileFileList withTrashed()
 * @method static \Illuminate\Database\Query\Builder|StudentProfileFileList withoutTrashed()
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Reestry\Student\Student $student
 * @method static Builder|StudentProfileFileList whereDeletedAt($value)
 * @mixin \Eloquent
 */
class StudentProfileFileList extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['student_id', 'file_name'];

//    protected static function booted()
//    {
//        static::addGlobalScope('accessible', function (Builder $builder) {
//            if (Auth::user()->is_super_admin || Auth::user()->hasModel(Own::MODELS['Student'])) {
//                return $builder;
//            }
//            else if (Auth::user()->hasPrograms()) {
//                return $builder->whereHas('student', function ($query) {
//                    return $query->whereIn('program_id', Auth::user()->programs()->pluck('id'));
//                });
//            }
//            else {
//                return $builder->where('created_at', '<=', now()->subYears(20));
//            }
//        });
//    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
}
