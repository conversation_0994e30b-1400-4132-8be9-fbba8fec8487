<?php

namespace App\Models\Reestry\Student;

use App\Models\Curriculum\LectureStudent;
use App\Models\Syllabus\Syllabus;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Predis\Client;

/**
 * App\Models\Reestry\Student\StudentSyllabusHistory
 *
 * @property int $id
 * @property int $student_id
 * @property int $syllabus_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Student\Student $student
 * @property-read Syllabus $syllabus
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereUpdatedAt($value)
 * @property int $is_passed
 * @property float $point
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory whereIsPassed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentSyllabusHistory wherePoint($value)
 * @mixin \Eloquent
 */
class StudentSyllabusHistory extends Model
{
    use SoftDeletes;

    protected $table = 'student_syllabus_history';

    protected $fillable = [
        'student_id',
        'syllabus_id',
        'is_passed',
        'point',
        'is_closed',
        'credits',
        'created_at',
        'updated_at',
        'lmb',
        'semester_id',
        'manual_addition'
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::calculateGpa($model->student_id, $model->syllabus_id);
            self::systemLog($model, SystemLog::DELETE_ACTION);
            $lectureId = 3;
            LectureStudent::query()->where('student_id', $model->student_id)->where('lecture_id', $lectureId)->delete();
        });

        static::created(function ($model){
            self::calculateGpa($model->student_id);
        });
        static::updated(function ($model){
            self::calculateGpa($model->student_id);
        });
    }

    static public function calculateGpa($studentId, $syllabusId=null)
    {
        $histories = StudentSyllabusHistory::query()
            ->with('syllabus')
            ->where('student_id', $studentId)
            ->where('is_passed', 1)
            ->where('is_closed', 1)
            ->when($syllabusId, function ($history)use ($syllabusId){
                $history->where('syllabus_id', '!=', $syllabusId);
            })
            ->get()
            ->map(function ($history){
                $point = $history->point;
                $weight = 0;
                if ($point >= 90.5){
                    $weight = 4;
                } elseif ($point >= 80.5) {
                    $weight = 3.2;
                } elseif ($point >= 70.5) {
                    $weight = 2.4;
                } elseif ($point >= 60.5) {
                    $weight = 1.6;
                } elseif ($point >= 50.5) {
                    $weight = 0.8;
                }

                $credit = $history->credits ?? $history->syllabus?->credits;
                return [
                    'weighted_credit' => $weight * $credit,
                    'credit' => $credit
                ];
            });
        $totalCredits = $histories->sum('credit');
        $gpa = $totalCredits > 0 ? round($histories->sum('weighted_credit') / $totalCredits, 3) : 0;
        $gpaFormatted = number_format($gpa, 3, '.', '');
        Student::query()->find($studentId)->update(['gpa' => $gpaFormatted]);
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function syllabus()
    {
        return $this->belongsTo(Syllabus::class);
    }

    static public function studentHistories($studentId)
    {
        return StudentSyllabusHistory::query()
            ->select([
                'student_syllabus_history.id',
                'student_syllabus_history.student_id',
                'student_syllabus_history.syllabus_id',
                'student_syllabus_history.is_passed',
                'student_syllabus_history.point',
                'student_syllabus_history.is_closed',
                'student_syllabus_history.credits',
                'student_syllabus_history.lmb',
                'student_syllabus_history.created_at',
                'student_syllabus_history.updated_at',
                'student_syllabus_history.semester_id',
            ])
            ->with(['syllabus' => function($syllabus){
                $syllabus->select(['id', 'semester_id', 'name', 'name_en', 'credits', 'code', 'is_external']);
            }])
            ->join('syllabi', 'student_syllabus_history.syllabus_id', '=', 'syllabi.id')
            ->whereHas('syllabus')
            ->where('is_closed', 1)
            ->where('student_id', $studentId)
            ->orderBy('syllabi.semester_id', 'desc')
            ->get()
        ;
    }
}
