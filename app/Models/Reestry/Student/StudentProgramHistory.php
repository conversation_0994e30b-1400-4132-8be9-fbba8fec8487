<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use App\Models\Reestry\Program\Program;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentProgramHistory
 *
 * @property int $id
 * @property int $student_id
 * @property int $program_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Program\Program $program
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentProgramHistory whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StudentProgramHistory extends Model
{
    use HasFactory;

    protected $fillable = ['student_id', 'program_id'];

    public function program() //მოგვაქვს პროგრამის დასახელება
    {
        return $this->belongsTo(Program::class);
    }
}
