<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentGroupHistory
 *
 * @property int $id
 * @property int $student_id
 * @property int $group_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Student\StudentGroup $group
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupHistory whereUpdatedAt($value)
 * @property-read \App\Models\Reestry\Student\Student $student
 * @mixin \Eloquent
 */
class StudentGroupHistory extends Model
{
    use HasFactory;

    protected $fillable = ['student_id', 'group_id'];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function group() //ვიღებთ ჯგუფის დასახელებას
    {
        return $this->belongsTo(StudentGroup::class, 'group_id', 'id');
    }
}
