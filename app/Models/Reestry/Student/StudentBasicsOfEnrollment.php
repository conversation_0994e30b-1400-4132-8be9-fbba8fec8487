<?php

namespace App\Models\Reestry\Student;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\StudentBasicsOfEnrollment
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment newQuery()
 * @method static \Illuminate\Database\Query\Builder|StudentBasicsOfEnrollment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|StudentBasicsOfEnrollment withTrashed()
 * @method static \Illuminate\Database\Query\Builder|StudentBasicsOfEnrollment withoutTrashed()
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Database\Factories\Reestry\Student\StudentBasicsOfEnrollmentFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentBasicsOfEnrollment whereDeletedAt($value)
 * @mixin \Eloquent
 */
class StudentBasicsOfEnrollment extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name'];
}
