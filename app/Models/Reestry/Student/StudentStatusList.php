<?php

namespace App\Models\Reestry\Student;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\StudentStatusList
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\Student[] $students
 * @property-read int|null $students_count
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList newQuery()
 * @method static \Illuminate\Database\Query\Builder|StudentStatusList onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|StudentStatusList withTrashed()
 * @method static \Illuminate\Database\Query\Builder|StudentStatusList withoutTrashed()
 * @property string $name_en
 * @property string $name_ka
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Database\Factories\Reestry\Student\StudentStatusListFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusList whereNameKa($value)
 * @mixin \Eloquent
 */
class StudentStatusList extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name_en', 'name_ka'];

    const STATUSES = [
        1 => ['Active', 'აქტიური'],
        2 => ['Stopped', 'შეჩერებული'],
        3 => ['Academic', 'აკადემიური'],
        9 => ['Finance', 'ფინანსი']
    ];

    const ACTIVE = 1;
    const STOPPED = 2;
    const ACADEMIC = 3;
    const FINANCE = 9;

    public function students() //მოგვას სტუდენტების სტატუსის მიხედვით
    {
        return $this->hasMany(Student::class);
    }
}
