<?php

namespace App\Models\Reestry\Student;

use App\Models\Assignment;
use App\Models\StudentAssignmentLastCheckout;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Reestry\Student\StudentAssignment
 *
 * @property int $id
 * @property int $student_id
 * @property int $assignment_id
 * @property int $syllabus_id
 * @property string $date
 * @property float $point
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Assignment|null $assignment
 * @property-read \Illuminate\Database\Eloquent\Collection<int, StudentAssignmentLastCheckout> $lastCheckout
 * @property-read int|null $last_checkout_count
 * @property-read \App\Models\Reestry\Student\Student $student
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereAssignmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAssignment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StudentAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'assignment_id',
        'syllabus_id',
        'point',
        'date',
    ];

    protected $casts = [
        'last_checkout' => 'array',
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function assignment(): BelongsTo
    {
        return $this->belongsTo(Assignment::class);
    }

    public function lastCheckout(): HasMany
    {
        return $this->hasMany(StudentAssignmentLastCheckout::class);
    }
}
