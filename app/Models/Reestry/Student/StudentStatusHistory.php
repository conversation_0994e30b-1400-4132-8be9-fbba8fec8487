<?php

namespace App\Models\Reestry\Student;

use App\Models\Own;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\StudentStatusHistory
 *
 * @property int $id
 * @property int $student_id
 * @property int $status_id
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Student\Student $student
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentStatusHistory whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StudentStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = ['student_id', 'status_id', 'notes'];

//    protected static function booted()
//    {
//        static::addGlobalScope('accessible', function (Builder $builder) {
//            if (Auth::user()->is_super_admin || Auth::user()->hasModel(Own::MODELS['Student'])) {
//                return $builder;
//            }
//            else if (Auth::user()->hasPrograms()) {
//                return $builder->whereHas('student', function ($query) {
//                    return $query->whereIn('program_id', Auth::user()->programs()->pluck('id'));
//                });
//            }
//            else {
//                return $builder->where('created_at', '<=', now()->subYears(20));
//            }
//        });
//    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
}
