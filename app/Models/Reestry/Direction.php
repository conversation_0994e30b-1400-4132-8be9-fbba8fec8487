<?php

namespace App\Models\Reestry;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Direction
 *
 * @property int $id
 * @property string $name_en
 * @property string $name_ka
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|Lecturer[] $lecturers
 * @property-read int|null $lecturers_count
 * @method static \Database\Factories\Reestry\DirectionFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Direction newQuery()
 * @method static \Illuminate\Database\Query\Builder|Direction onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Direction query()
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereNameKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Direction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Direction withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Direction withoutTrashed()
 * @mixin \Eloquent
 */
class Direction extends Model
{
    use HasFactory, SoftDeletes,HasFilters;

    protected $fillable = ['name_en', 'name_ka'];

    public function lecturers(): BelongsToMany
    {
        return $this->belongsToMany(Lecturer::class);
    }
}
