<?php

namespace App\Models\Reestry;

use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Program\Program;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\AcademicDegree
 *
 * @property int $id
 * @property string $name_en
 * @property string $name_ka
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Lecturer|null $lecturer
 * @property-read Program|null $program
 * @method static \Database\Factories\Reestry\AcademicDegreeFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree newQuery()
 * @method static \Illuminate\Database\Query\Builder|AcademicDegree onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree query()
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereNameKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|AcademicDegree withTrashed()
 * @method static \Illuminate\Database\Query\Builder|AcademicDegree withoutTrashed()
 * @property string|null $url
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicDegree whereUrl($value)
 * @mixin \Eloquent
 */
class AcademicDegree extends Model
{
    use HasFactory, SoftDeletes, HasFilters;

    const DEGREES = [
        1 => ['Bachelor', 'ბაკალავრი', 'bachelor'],
        2 => ['Master', 'მაგისტრი', 'master'],
        3 => ['PhD', 'დოქტორი', 'phd'],
        4 => ['Proffession', 'პროფესიული', 'hse'],
        5 => ['Training', 'ტრენინგი', 'tcc']
    ];

    protected $guarded = [];

    public function program(): HasOne
    {
        return $this->hasOne(Program::class, 'academic_degree_id', 'id');
    }

    public function lecturer(): HasOne
    {
        return $this->hasOne(Lecturer::class);
    }
}
