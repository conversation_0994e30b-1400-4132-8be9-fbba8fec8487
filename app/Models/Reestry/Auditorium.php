<?php

namespace App\Models\Reestry;

use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Own;
use App\Models\Scopes\AccessibleScope;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Auditorium
 *
 * @property int $id
 * @property string $name
 * @property int $campus_id
 * @property int $student_aid
 * @property int $quantity
 * @property int $projector
 * @property int $multimedia
 * @property int $exam_audience
 * @property int $cameras
 * @property int $computer_lab
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Campus|null $campus
 * @property-read \Illuminate\Database\Eloquent\Collection|CurriculumLectureTime[] $lectureTimes
 * @property-read int|null $lecture_times_count
 * @method static \Database\Factories\Reestry\AuditoriumFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium newQuery()
 * @method static \Illuminate\Database\Query\Builder|Auditorium onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium query()
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereCameras($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereCampusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereComputerLab($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereExamAudience($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereMultimedia($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereProjector($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereStudentAid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Auditorium whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Auditorium withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Auditorium withoutTrashed()
 * @mixin \Eloquent
 */
class Auditorium extends Model
{
    use HasFactory, SoftDeletes, HasFilters;

    protected $guarded = [];

    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function lectureTimes()
    {
        return $this->hasMany(CurriculumLectureTime::class);
    }
}
