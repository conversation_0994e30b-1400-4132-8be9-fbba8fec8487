<?php

namespace App\Models\Reestry;

use App\Models\Own;
use App\Models\Reestry\Program\Program;
use App\Models\Scopes\AccessibleScope;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\School
 *
 * @property int $id
 * @property string $name
 * @property int $campus_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Reestry\Campus $campus
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Program\Program[] $programs
 * @property-read int|null $programs_count
 * @method static \Illuminate\Database\Eloquent\Builder|School newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|School newQuery()
 * @method static \Illuminate\Database\Query\Builder|School onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|School query()
 * @method static \Illuminate\Database\Eloquent\Builder|School whereCampusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|School withTrashed()
 * @method static \Illuminate\Database\Query\Builder|School withoutTrashed()
 * @property string $name_ka
 * @property string $name_en
 * @method static \Database\Factories\Reestry\SchoolFactory factory(...$parameters)
 * @method static Builder|School filter(\App\Filters\QueryFilters $filters)
 * @method static Builder|School whereNameEn($value)
 * @method static Builder|School whereNameKa($value)
 * @property string|null $dean_ka
 * @property string|null $dean_en
 * @property string|null $signature
 * @method static Builder|School whereDeanEn($value)
 * @method static Builder|School whereDeanKa($value)
 * @method static Builder|School whereSignature($value)
 * @mixin \Eloquent
 */
class School extends Model
{
    use SoftDeletes, HasFactory, HasFilters;

    protected $fillable = ['name_en', 'name_ka', 'address_en', 'address_ka', 'campus_id', 'deleted_at', 'updated_at'];

    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function programs(): HasMany
    {
        return $this->hasMany(Program::class, 'school_id', 'id');
    }

    public function createdAt(): Attribute
    {
        return (new Program())->createdAt();
    }

    public function updatedAt(): Attribute
    {
        return (new Program())->createdAt();
    }

}
