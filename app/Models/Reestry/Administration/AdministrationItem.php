<?php

namespace App\Models\Reestry\Administration;

use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Administration\AdministrationItem
 *
 * @property int $id
 * @property string $name_en
 * @property string $name_ka
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem newQuery()
 * @method static \Illuminate\Database\Query\Builder|AdministrationItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereNameKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|AdministrationItem withTrashed()
 * @method static \Illuminate\Database\Query\Builder|AdministrationItem withoutTrashed()
 * @mixin \Eloquent
 */
class AdministrationItem extends Model
{
    use HasFactory,HasFilters,SoftDeletes;

    protected $guarded = [];
}
