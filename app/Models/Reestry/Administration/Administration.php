<?php

namespace App\Models\Reestry\Administration;

use App\Models\HR\Administration\HrAdministrationInfo;
use App\Models\Own;
use App\Models\Reestry\School;
use App\Models\RoleUser;
use App\Models\User\User;
use App\Models\Scopes\AccessibleScope;
use App\Models\UserProgram;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Administration\Administration
 *
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property int $identity_number
 * @property string|null $phone
 * @property string $email
 * @property int|null $administration_position_id
 * @property int|null $administration_item_id
 * @property int|null $school_id
 * @property string|null $photo
 * @property string|null $cv
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Administration\AdministrationItem|null $administrationItem
 * @property-read \App\Models\Reestry\Administration\AdministrationPosition|null $administrationPosition
 * @property-read HrAdministrationInfo|null $hrAdministrationInfo
 * @property-read School|null $school
 * @property-read User|null $user
 * @method static \Database\Factories\Reestry\Administration\AdministrationFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Administration newQuery()
 * @method static \Illuminate\Database\Query\Builder|Administration onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Administration query()
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereAdministrationItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereAdministrationPositionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereCv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereIdentityNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Administration whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|Administration withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Administration withoutTrashed()
 * @property-read mixed $full_name
 * @property-read RoleUser|null $role
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserProgram> $userPrograms
 * @property-read int|null $user_programs_count
 * @mixin \Eloquent
 */
class Administration extends Model
{
    use HasFactory, SoftDeletes, HasFilters;

    protected $fillable = [
        'first_name',
        'last_name',
        'identity_number',
        'phone',
        'email',
        'administration_position_id',
        'administration_item_id',
        'school_id',
        'photo',
        'cv',
        'user_id'
    ];

    protected $appends = ['full_name'];

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function hrAdministrationInfo(): BelongsTo
    {
        return $this->belongsTo(
            HrAdministrationInfo::class,
            'user_id',
            'user_id'
        );
    }

    public function administrationPosition(): BelongsTo
    {
        return $this->belongsTo(AdministrationPosition::class);
    }

    public function administrationItem(): BelongsTo
    {
        return $this->belongsTo(AdministrationItem::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function role()
    {
        return $this->belongsTo(RoleUser::class, 'user_id', 'user_id');
    }

    public function userPrograms()
    {
        return $this->hasMany(UserProgram::class, 'user_id', 'user_id')->with('program');
    }

}
