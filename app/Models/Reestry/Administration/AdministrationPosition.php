<?php

namespace App\Models\Reestry\Administration;

use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Administration\AdministrationPosition
 *
 * @property int $id
 * @property string $name_en
 * @property string $name_ka
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Reestry\Administration\Administration|null $administration
 * @method static \Database\Factories\Reestry\Administration\AdministrationPositionFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition newQuery()
 * @method static \Illuminate\Database\Query\Builder|AdministrationPosition onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereNameKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdministrationPosition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|AdministrationPosition withTrashed()
 * @method static \Illuminate\Database\Query\Builder|AdministrationPosition withoutTrashed()
 * @mixin \Eloquent
 */
class AdministrationPosition extends Model
{
    use HasFactory,SoftDeletes,HasFilters;

    protected $guarded = [];

    public function administration(): HasOne
    {
        return $this->hasOne(Administration::class);
    }
}
