<?php

namespace App\Models\Reestry\Lecturer;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reestry\Lecturer\LecturerDirection
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerDirection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerDirection newQuery()
 * @method static \Illuminate\Database\Query\Builder|LecturerDirection onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerDirection query()
 * @method static \Illuminate\Database\Query\Builder|LecturerDirection withTrashed()
 * @method static \Illuminate\Database\Query\Builder|LecturerDirection withoutTrashed()
 * @mixin \Eloquent
 */
class LecturerDirection extends Model
{
    use HasFactory,SoftDeletes;
}
