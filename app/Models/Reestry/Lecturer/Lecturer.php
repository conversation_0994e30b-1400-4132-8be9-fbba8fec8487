<?php

namespace App\Models\Reestry\Lecturer;

use App\Models\ElBook\ElBook;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo;
use App\Models\HR\Lecturer\Invited\HrInvitedLectureInfo;
use App\Models\Own;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\Direction;
use App\Models\Scopes\AccessibleScope;
use App\Models\SystemLog;
use App\Models\User\User;
use App\Models\syllabus\LecturerContactTime;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Lecturer
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer newQuery()
 * @method static \Illuminate\Database\Query\Builder|Lecturer onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer query()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Lecturer withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Lecturer withoutTrashed()
 * @property string $first_name
 * @property string $last_name
 * @property int $user_id
 * @property int $identity_number
 * @property string $card_number
 * @property string $address
 * @property string $phone
 * @property string $date_of_birth
 * @property string $email
 * @property string|null $photo
 * @property int $academic_degree_id
 * @property int|null $affiliated
 * @property string|null $cv
 * @property int|null $do_lectures_another_university
 * @property-read AcademicDegree|null $academicDegree
 * @property-read \Illuminate\Database\Eloquent\Collection|Direction[] $directions
 * @property-read int|null $directions_count
 * @property-read \Illuminate\Database\Eloquent\Collection|ElBook[] $elBooks
 * @property-read int|null $el_books_count
 * @property-read HrAcademicLectureInfo|null $hrAcademicLectureInfo
 * @property-read HrInvitedLectureInfo|null $hrInvitedLectureInfo
 * @property-read \App\Models\Syllabus\LecturerContactTime|null $lecturerContactTimes
 * @property-read User|null $user
 * @method static \Database\Factories\Reestry\Lecturer\LecturerFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereAffiliated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereCardNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereCv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereDoLecturesAnotherUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereIdentityNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereUserId($value)
 * @property int $lmb_id
 * @method static \Illuminate\Database\Eloquent\Builder|Lecturer whereLmbId($value)
 * @mixin \Eloquent
 */
class Lecturer extends Model
{
    protected $fillable = [
        'first_name',
        'last_name',
        'identity_number',
        'card_number',
        'user_id',
        'address',
        'phone',
        'date_of_birth',
        'email',
        'photo',
        'academic_degree_id',
        'affiliated',
        'cv',
        'do_lectures_another_university',
        'lmb_id'
    ];

    use SoftDeletes, HasFactory, HasFilters;

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }

    public function directions(): BelongsToMany
    {
        return $this->belongsToMany(Direction::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    //    public function dateOfBirth(): Attribute
    //    {
    //        return Attribute::make(
    //            get: fn($value) => Carbon::parse($value),
    //            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
    //        );
    //    }

    public function lecturerContactTimes(): HasOne
    {
        return $this->hasOne(LecturerContactTime::class);
    }

    public function elBooks(): HasMany
    {
        return $this->hasMany(ElBook::class);
    }

    public function hrInvitedLectureInfo(): BelongsTo
    {
        return $this->belongsTo(
            HrInvitedLectureInfo::class,
            'user_id',
            'user_id'
        );
    }

    public function hrAcademicLectureInfo(): BelongsTo
    {
        return $this->belongsTo(
            HrAcademicLectureInfo::class,
            'user_id',
            'user_id'
        );
    }

}
