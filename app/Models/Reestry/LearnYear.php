<?php

namespace App\Models\Reestry;

use App\Models\Own;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\Student;
use App\Models\Scopes\AccessibleScope;
use App\Models\Syllabus\Syllabus;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\LearnYear
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear query()
 * @property int $id
 * @property int $program_id
 * @property string $name
 * @property int $price
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Program|null $program
 * @property-read \Illuminate\Database\Eloquent\Collection|Student[] $students
 * @property-read int|null $students_count
 * @method static \Database\Factories\Reestry\LearnYearFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Query\Builder|LearnYear onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|LearnYear withTrashed()
 * @method static \Illuminate\Database\Query\Builder|LearnYear withoutTrashed()
 * @property string|null $hash
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereHash($value)
 * @property string|null $start_year
 * @property string|null $end_year
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Syllabus> $syllabus
 * @property-read int|null $syllabus_count
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LearnYear whereStartYear($value)
 * @mixin \Eloquent
 */
class LearnYear extends Model
{
    use HasFactory, SoftDeletes, HasFilters;

    protected $guarded = [];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class,'program_id','id');
    }

    public function syllabus(): HasMany
    {
        return $this->hasMany(Syllabus::class);
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(
            Student::class,
            'student_learn_year'
        );
    }
}
