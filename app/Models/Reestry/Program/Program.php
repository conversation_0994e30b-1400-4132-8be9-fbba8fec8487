<?php

namespace App\Models\Reestry\Program;

use App\Models\Own;
use App\Models\ProgramAdditional;
use App\Models\ProgramUser;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\School;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\RegisterForms\BachelorRegister;
use App\Models\RegisterForms\DoctorRegister;
use App\Models\RegisterForms\ProffesionRegister;
use App\Models\RegisterForms\TrainingRegister;
use App\Models\Scopes\AccessibleScope;
use App\Models\User\User;
use App\Traits\HasFilters;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Program
 *
 * @property int $id
 * @property string $name
 * @property int $school_id
 * @property int $program_type_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\Student\StudentGroup[] $groups
 * @property-read int|null $groups_count
 * @property-read \App\Models\Reestry\School $school
 * @method static \Illuminate\Database\Eloquent\Builder|Program newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Program newQuery()
 * @method static \Illuminate\Database\Query\Builder|Program onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Program query()
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Program withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Program withoutTrashed()
 * @property string $name_ka
 * @property string $name_en
 * @property int $academic_degree_id
 * @property string|null $color
 * @property-read AcademicDegree|null $academicDegree
 * @property-read BachelorRegister|null $bachelorRegister
 * @property-read DoctorRegister|null $doctorRegister
 * @property-read \Illuminate\Database\Eloquent\Collection|LearnYear[] $learnYears
 * @property-read int|null $learn_years_count
 * @property-read ProffesionRegister|null $professionRegister
 * @property-read \Illuminate\Database\Eloquent\Collection|StudentGroup[] $studentGroups
 * @property-read int|null $student_groups_count
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Database\Factories\Reestry\Program\ProgramFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Program filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Program whereNameKa($value)
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $users
 * @property-read int|null $users_count
 * @mixin \Eloquent
 */
class Program extends Model
{
    use SoftDeletes, HasFactory, HasFilters;

    protected $fillable = [
        'id',
        'name_en',
        'name_ka',
        'color',
        'school_id',
        'academic_degree_id',
        'registration_form'
    ];

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function learnYears(): HasMany
    {
        return $this->hasMany(LearnYear::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }

    public function studentGroups(): HasMany
    {
        return $this->hasMany(StudentGroup::class, 'program_id', 'id');
    }

    public function createdAt(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Carbon::parse($value)->format('Y-m-d H:i')
        );
    }

    public function updatedAt(): Attribute
    {
        return $this->createdAt();
    }

    public function trainingRegister(): HasOne
    {
        return $this->hasOne(TrainingRegister::class);
    }

    public function professionRegister(): HasOne
    {
        return $this->hasOne(ProffesionRegister::class);
    }

    public function doctorRegister(): HasOne
    {
        return $this->hasOne(DoctorRegister::class);
    }

    public function bachelorRegister(): HasOne
    {
        return $this->hasOne(BachelorRegister::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_program')->using(ProgramUser::class);
    }

    public function additional(): BelongsTo
    {
        return $this->belongsTo(ProgramAdditional::class, 'id', 'program_id');
    }
}
