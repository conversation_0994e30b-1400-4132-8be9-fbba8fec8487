<?php

namespace App\Models\Reestry;

use App\Models\Own;
use App\Models\Reestry\Program\Program;
use App\Models\Scopes\AccessibleScope;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Reestry\Flow
 *
 * @property int $id
 * @property string $name
 * @property int $program_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Program|null $program
 * @method static \Illuminate\Database\Eloquent\Builder|Flow filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Flow newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Flow newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Flow query()
 * @method static \Illuminate\Database\Eloquent\Builder|Flow whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Flow whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Flow whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Flow whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Flow whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Flow extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'name',
        'name_en',
        'program_id'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }
}
