<?php

namespace App\Models\Reestry;

use App\Models\Own;
use App\Models\Reestry\Program\Program;
use App\Models\Scopes\AccessibleScope;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Campus
 *
 * @property int $id
 * @property string $name
 * @property string $address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Reestry\School|null $school
 * @method static \Illuminate\Database\Eloquent\Builder|Campus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campus newQuery()
 * @method static \Illuminate\Database\Query\Builder|Campus onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Campus query()
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Campus withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Campus withoutTrashed()
 * @property string $name_ka
 * @property string $name_en
 * @property string $address_ka
 * @property string $address_en
 * @property-read \App\Models\Reestry\Auditorium|null $auditorium
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Reestry\School[] $schools
 * @property-read int|null $schools_count
 * @method static \Database\Factories\Reestry\CampusFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereAddressEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereAddressKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campus whereNameKa($value)
 * @mixin \Eloquent
 */
class Campus extends Model
{
    use SoftDeletes, HasFactory, HasFilters;

    protected $fillable = ['name_en', 'name_ka', 'address_en', 'address_ka', 'deleted_at'];

    public function schools(): HasMany
    {
        return $this->hasMany(School::class, 'campus_id', 'id');
    }

    public function auditorium(): HasOne
    {
        return $this->hasOne(Auditorium::class);
    }

    public function createdAt(): Attribute
    {
        return (new Program())->createdAt();
    }

    public function updatedAt(): Attribute
    {
        return (new Program())->createdAt();

    }
}
