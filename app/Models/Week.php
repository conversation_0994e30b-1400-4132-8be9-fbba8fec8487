<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Week
 *
 * @property int $id
 * @property int $syllabus_id
 * @property int $number
 * @property string $title
 * @property string $main_literature
 * @property string $secondary_literature
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Week newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Week newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Week query()
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereMainLiterature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereSecondaryLiterature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereUpdatedAt($value)
 * @property string|null $title_en
 * @property string|null $main_literature_en
 * @property string|null $secondary_literature_en
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereMainLiteratureEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereSecondaryLiteratureEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Week whereTitleEn($value)
 * @mixin \Eloquent
 */
class Week extends Model
{
    use HasFactory;

    protected $fillable = [
        'number',
        'title',
        'main_literature',
        'secondary_literature',
        'title_en',
        'main_literature_en',
        'secondary_literature_en'
    ];
}
