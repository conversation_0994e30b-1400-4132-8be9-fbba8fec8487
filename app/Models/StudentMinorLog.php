<?php

namespace App\Models;

use App\Models\Reestry\Flow;
use App\Models\Reestry\Student\Student;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentMinorLog extends Model
{
    use HasFactory;

    protected $table = 'student_minor_logs';

    protected $fillable = [
        'student_id',
        'minor_id',
        'flow_id',
    ];


    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function flow(): BelongsTo
    {
        return $this->belongsTo(Flow::class, 'flow_id', 'id');
    }
}
