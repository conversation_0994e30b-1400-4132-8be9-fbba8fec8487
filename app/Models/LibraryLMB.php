<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\LibraryLMB
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LibraryLMB newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LibraryLMB newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LibraryLMB query()
 * @mixin \Eloquent
 */
class LibraryLMB extends Model
{
    protected $table = 'library';

    protected $fillable = ['name', 'autor', 'edition_date', 'sagani', 'lektori', 'link'];
}
