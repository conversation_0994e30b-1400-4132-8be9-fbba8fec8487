<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * App\Models\ProgramUser
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramUser query()
 * @mixin \Eloquent
 */
class ProgramUser extends Pivot
{
    use HasFactory;
}
