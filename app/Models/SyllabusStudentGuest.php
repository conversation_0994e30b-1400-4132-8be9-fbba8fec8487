<?php

namespace App\Models;

use App\Models\Reestry\Student\Student;
use App\Models\Syllabus\Syllabus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\SyllabusStudentGuest
 *
 * @property int $id
 * @property int $student_id
 * @property int $syllabus_id
 * @property int $student_group_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest query()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereStudentGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusStudentGuest whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SyllabusStudentGuest extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'syllabus_id',
        'student_group_id'
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function syllabus(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }
}
