<?php

namespace App\Models;

use App\Models\Reestry\Student\Student;
use App\Models\Syllabus\Syllabus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Predis\Client;

class History extends Model
{
    use HasFactory;

    protected $table = 'histories';

    protected $fillable = [
        'student_id',
        'syllabus_id',
        'is_passed',
        'point',
        'is_closed',
        'credit',
        'created_at',
        'updated_at',
        'lmb'
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function syllabus()
    {
        return $this->belongsTo(Syllabus::class);
    }
}
