<?php

namespace App\Models;

use App\Models\RegisterForms\BachelorRegister;
use App\Models\RegisterForms\DoctorRegister;
use App\Models\RegisterForms\MasterRegister;
use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\EnglishLevel
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read BachelorRegister|null $bachelorRegister
 * @property-read DoctorRegister|null $doctorRegister
 * @property-read MasterRegister|null $masterRegister
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnglishLevel whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EnglishLevel extends Model
{
    use HasFactory;

    const IDS = [
        1 => 'A1',
        2 => 'A2',
        3 => 'B1',
        4 => 'B2',
        5 => 'C1',
        6 => 'C2'
    ];

    protected $fillable = [
        'title'
    ];

    public function trainingRegister(): HasOne
    {
        return $this->hasOne(TrainingRegister::class);
    }

    public function doctorRegister(): HasOne
    {
        return $this->hasOne(DoctorRegister::class);
    }

    public function masterRegister(): HasOne
    {
        return $this->hasOne(MasterRegister::class);
    }

    public function bachelorRegister(): HasOne
    {
        return $this->hasOne(BachelorRegister::class);
    }
}
