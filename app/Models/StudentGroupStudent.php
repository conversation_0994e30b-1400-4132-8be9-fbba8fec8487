<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\StudentGroupStudent
 *
 * @property int $id
 * @property int $student_id
 * @property int $student_group_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent whereStudentGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentGroupStudent whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class StudentGroupStudent extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'student_group_id'
    ];
}
