<?php

namespace App\Models\Syllabus;

use App\Models\Assignment;
use App\Models\Curriculum\Curriculum;
use App\Models\Lectures\Lecture;
use App\Models\Own;
use App\Models\Reestry\AcademicDegree;
use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\StudentGroup;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Scopes\AccessibleScope;
use App\Models\Semester;
use App\Models\Status;
use App\Models\SystemLog;
use App\Models\Week;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Syllabus\Syllabus
 *
 * @property int $id
 * @property string $name
 * @property int|null $learn_year_id
 * @property int $status_id
 * @property int $semester_id
 * @property int $academic_degree_id
 * @property string $code
 * @property int $credits
 * @property int $contact_hours
 * @property int $lecture_hours
 * @property int $seminar_hours
 * @property int $mid_and_final_exam_hours
 * @property int $independent_work_hours
 * @property int $total_hours
 * @property string $goal
 * @property string $assessing_system
 * @property string $final_exam_prerequisite
 * @property string $main_literature
 * @property string $additional_literature
 * @property string $academic_honesty
 * @property string|null $additional_information
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree $academicDegree
 * @property-read \Illuminate\Database\Eloquent\Collection|Assignment[] $assignments
 * @property-read int|null $assignments_count
 * @property-read LearnYear|null $learnYear
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Syllabus\learningOutcome[] $learningOutcomes
 * @property-read int|null $learning_outcomes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Syllabus\LecturerContactTime[] $lecturerContactTimes
 * @property-read int|null $lecturer_contact_times_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Lecturer[] $lecturers
 * @property-read int|null $lecturers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Syllabus\Method[] $methods
 * @property-read int|null $methods_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Syllabus[] $prerequisites
 * @property-read int|null $prerequisites_count
 * @property-read Semester $semester
 * @property-read Status $status
 * @property-read \Illuminate\Database\Eloquent\Collection|StudentSyllabusHistory[] $students
 * @property-read int|null $students_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Week[] $weeks
 * @property-read int|null $weeks_count
 * @method static \Database\Factories\Syllabus\SyllabusFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus newQuery()
 * @method static \Illuminate\Database\Query\Builder|Syllabus onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus query()
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAcademicHonesty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAdditionalInformation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAdditionalLiterature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAssessingSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereContactHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereCredits($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereFinalExamPrerequisite($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereGoal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereIndependentWorkHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearnYearId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLectureHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereMainLiterature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereMidAndFinalExamHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereSemesterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereSeminarHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereTotalHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereIsTraining($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereIsProfession($value)
 * @method static \Illuminate\Database\Query\Builder|Syllabus withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Syllabus withoutTrashed()
 * @property string|null $retake_missed_assignment
 * @property string|null $learning_outcome_knowledge
 * @property string|null $learning_outcome_skill
 * @property string|null $learning_outcome_responsibility
 * @property string|null $exam_rules
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereExamRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeKnowledge($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeResponsibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeSkill($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereRetakeMissedAssignment($value)
 * @property int $is_training
 * @property int $is_profession
 * @property int $exam_percent
 * @property string|null $name_en
 * @property string|null $goal_en
 * @property string|null $assessing_system_en
 * @property string|null $final_exam_prerequisite_en
 * @property string|null $retake_missed_assignment_en
 * @property string|null $main_literature_en
 * @property string|null $additional_literature_en
 * @property string|null $academic_honesty_en
 * @property string|null $additional_information_en
 * @property string|null $learning_outcome_knowledge_en
 * @property string|null $learning_outcome_skill_en
 * @property string|null $learning_outcome_responsibility_en
 * @property string|null $exam_rules_en
 * @property-read Curriculum|null $curriculum
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Lecture> $lectures
 * @property-read int|null $lectures_count
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAcademicHonestyEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAdditionalInformationEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAdditionalLiteratureEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereAssessingSystemEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereExamPercent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereExamRulesEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereFinalExamPrerequisiteEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereGoalEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeKnowledgeEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeResponsibilityEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereLearningOutcomeSkillEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereMainLiteratureEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Syllabus whereRetakeMissedAssignmentEn($value)
 * @mixin \Eloquent
 */
class Syllabus extends Model
{
    use HasFactory, SoftDeletes, HasFilters;


    protected $fillable = [
        'lmb_id',
        'name',
        'learn_year_id',
        'status_id',
        'semester_id',
        'academic_degree_id',
        'code',
        'credits',
        'contact_hours',
        'lecture_hours',
        'seminar_hours',
        'mid_and_final_exam_hours',
        'independent_work_hours',
        'total_hours',
        'goal',
        'assessing_system',
        'final_exam_prerequisite',
        'exam_percent',
        'assessing_components',
        'academic_honesty',
        'additional_information',
        'main_literature',
        'learning_outcome_knowledge',
        'learning_outcome_skill',
        'learning_outcome_responsibility',
        'additional_literature',
        'retake_missed_assignment',
        'exam_rules',
        'name_en',
        'goal_en',
        'assessing_system_en',
        'final_exam_prerequisite_en',
        'retake_missed_assignment_en',
        'main_literature_en',
        'additional_literature_en',
        'academic_honesty_en',
        'additional_information_en',
        'learning_outcome_knowledge_en',
        'learning_outcome_skill_en',
        'learning_outcome_responsibility_en',
        'exam_rules_en',
        'is_training',
        'is_profession',
        'syllabus_type_id',
        'is_external'
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function learnYear(): BelongsTo
    {
        return $this->belongsTo(LearnYear::class);
    }

    public function curriculum(): HasOne
    {
        return $this->hasOne(Curriculum::class);
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    public function lecturers(): BelongsToMany
    {
        return $this->belongsToMany(Lecturer::class, 'lecturer_syllabus', 'syllabus_id', 'lecturer_id');
    }

    public function weeks(): HasMany
    {
        return $this->hasMany(Week::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class)->with('assessmentComponent', 'parentComponent');
    }

    public function lecturerContactTimes(): HasMany
    {
        return $this->hasMany(LecturerContactTime::class);
    }

    public function prerequisites(): BelongsToMany
    {
        return $this->belongsToMany(Syllabus::class, 'prerequisite_syllabus', 'syllabus_id', 'prerequisite_id');
    }

    public function learningOutcomes(): BelongsToMany
    {
        return $this->belongsToMany(learningOutcome::class, 'learning_outcome_syllabus', 'syllabus_id', 'learning_outcome_id');
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }

    public function methods(): BelongsToMany
    {
        return $this->belongsToMany(Method::class, 'method_syllabus', 'syllabus_id', 'method_id');
        // return $this->belongsToMany(Method::class);
    }

    public function students()
    {
        return $this->hasMany(StudentSyllabusHistory::class, 'syllabus_id', 'id');
    }

    public function lectures()
    {
        return $this->hasMany(Lecture::class);
    }

}
