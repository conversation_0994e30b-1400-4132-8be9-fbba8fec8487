<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Syllabus\SyllabusType
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusType query()
 * @mixin \Eloquent
 */
class SyllabusType extends Model
{
    use HasFactory;

    const POINTS = 1;
    const PASS = 2;
}
