<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Syllabus\SyllabusInfo
 *
 * @property int $id
 * @property int $syllabus_id
 * @property int $auditorium_id
 * @property int $syllabus_time_id
 * @property int $student_group_id
 * @property int $lecturer_id
 * @property string $lecturer_cost_per_hour
 * @property string $accounting_id
 * @property string $start_date
 * @property string $end_date
 * @property int $allowed_students_amount
 * @property string $lang
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereAccountingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereAllowedStudentsAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereAuditoriumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereLang($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereLecturerCostPerHour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereLecturerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereStudentGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereSyllabusTimeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusInfo whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SyllabusInfo extends Model
{
    use HasFactory;

    protected $casts = [
        'time' => 'array'
    ];
}
