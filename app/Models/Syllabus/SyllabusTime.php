<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Syllabus\SyllabusTime
 *
 * @property int $id
 * @property int $syllabus_id
 * @property int $weekday
 * @property string $starts_at
 * @property int $duration
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime query()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereStartsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusTime whereWeekday($value)
 * @mixin \Eloquent
 */
class SyllabusTime extends Model
{
    use HasFactory;
}
