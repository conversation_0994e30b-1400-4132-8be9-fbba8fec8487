<?php

namespace App\Models\Syllabus;

use App\Models\Reestry\Lecturer\Lecturer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Syllabus\LecturerContactTime
 *
 * @property int $id
 * @property int $lecturer_id
 * @property int $syllabus_id
 * @property int $week_day
 * @property string $start_time
 * @property string $end_time
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime query()
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereLecturerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LecturerContactTime whereWeekDay($value)
 * @property-read Lecturer $lecturer
 * @property-read \App\Models\Syllabus\Syllabus $syllabus
 * @mixin \Eloquent
 */
class LecturerContactTime extends Model
{
    use HasFactory;

    protected $fillable = [
      'lecturer_id',
      'syllabus_id',
      'week_day',
      'start_time',
      'end_time'
    ];

    public function syllabus(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }

    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(Lecturer::class)->select(['id', 'first_name', 'last_name','phone','email','affiliated']);
    }

}
