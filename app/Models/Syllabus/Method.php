<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Syllabus\Method
 *
 * @property int $id
 * @property string $title
 * @property string $text
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Method newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Method newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Method query()
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereUpdatedAt($value)
 * @property string|null $title_en
 * @property string|null $text_en
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereTextEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Method whereTitleEn($value)
 * @mixin \Eloquent
 */
class Method extends Model
{
    use HasFactory;
}
