<?php

namespace App\Models\Syllabus;

use App\Models\Reestry\AcademicDegree;
use App\Models\Semester;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Syllabus\SyllabusProfession
 *
 * @property int $id
 * @property string|null $name_en
 * @property string $name
 * @property int $semester_id
 * @property int $academic_degree_id
 * @property string $code
 * @property int $credits
 * @property int $contact_hours
 * @property int $lecture_hours
 * @property int $seminar_hours
 * @property int $mid_and_final_exam_hours
 * @property int $independent_work_hours
 * @property int $total_hours
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree $academicDegree
 * @property-read Semester $semester
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession query()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereContactHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereCredits($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereIndependentWorkHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereLectureHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereMidAndFinalExamHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereSemesterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereSeminarHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereTotalHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusProfession whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SyllabusProfession extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_en',
        'name_ka',
        'semester_id',
        'academic_degree_id',
        'code',
        'credits',
        'contact_hours',
        'lecture_hours',
        'seminar_hours',
        'mid_and_final_exam_hours',
        'independent_work_hours',
        'total_hours',
    ];

    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }
}
