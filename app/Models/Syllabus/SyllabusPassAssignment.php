<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Syllabus\SyllabusPassAssignment
 *
 * @property int $id
 * @property int $syllabus_id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Syllabus\Syllabus|null $syllabi
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment query()
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SyllabusPassAssignment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SyllabusPassAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'syllabus_id'
    ];

    public function syllabi(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }
}
