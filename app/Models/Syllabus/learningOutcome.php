<?php

namespace App\Models\Syllabus;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Syllabus\learningOutcome
 *
 * @property int $id
 * @property string $title
 * @property string $text
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome query()
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|learningOutcome whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class learningOutcome extends Model
{
    use HasFactory;
}
