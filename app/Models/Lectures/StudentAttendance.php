<?php

namespace App\Models\Lectures;

use App\Models\Reestry\Student\Student;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Lectures\StudentAttendance
 *
 * @property int $id
 * @property int $student_id
 * @property int $lecture_id
 * @property int $is_present
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Lectures\Lecture $lecture
 * @property-read Student $student
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance query()
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereIsPresent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereLectureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereUpdatedAt($value)
 * @property int $nth_lecture
 * @property int $cron_marker
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereCronMarker($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StudentAttendance whereNthLecture($value)
 * @mixin \Eloquent
 */
class StudentAttendance extends Model
{
    use HasFactory;

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    protected $fillable = [
        'student_id',
        'lecture_id',
        'is_present',
        'nth_lecture',
        'cron_marker',
        'syllabus_id'
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function lecture(): BelongsTo
    {
        return $this->belongsTo(Lecture::class);
    }
}
