<?php

namespace App\Models\Lectures;

use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\LectureStudent;
use App\Models\Reestry\Auditorium;
use App\Models\Reestry\Lecturer\Lecturer;
use App\Models\Reestry\Student\Student;
use App\Models\Syllabus\Syllabus;
use App\Models\SystemLog;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Lectures\Lecture
 *
 * @property int $id
 * @property int $is_current
 * @property int $syllabus_id
 * @property int $lecturer_id
 * @property float $payment_per_hour
 * @property string $start_time
 * @property string|null $end_time
 * @property string $lecture_date
 * @property int $week_day
 * @property int $lecture_number
 * @property int $auditorium_id
 * @property int $is_lecture
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Lectures\StudentAttendance[] $attendances
 * @property-read int|null $attendances_count
 * @property-read Auditorium $auditorium
 * @property-read Lecturer $lecturer
 * @property-read \Illuminate\Database\Eloquent\Collection|Student[] $students
 * @property-read int|null $students_count
 * @property-read Syllabus $syllabus
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture filter(\App\Filters\QueryFilters $filters)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture query()
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereAuditoriumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereIsCurrent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereIsLecture($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereLectureDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereLectureNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereLecturerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture wherePaymentPerHour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereSyllabusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lecture whereWeekDay($value)
 * @property-read Curriculum $curricula
 * @mixin \Eloquent
 */
class Lecture extends Model
{
    use HasFilters;

    protected $fillable = [
        'is_current',
        'syllabus_id',
        'start_time',
        'end_time',
        'week_day',
        'lecture_date',
        'lecture_number',
        'auditorium_id',
        'is_lecture',
        'lecturer_id',
        'payment_per_hour',
    ];

    // Event fired when a new model instance is being created to log data
    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    public function syllabus(): BelongsTo
    {
        return $this->belongsTo(Syllabus::class);
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class);
    }

    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(Lecturer::class);
    }

    public function auditorium(): BelongsTo
    {
        return $this->belongsTo(Auditorium::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(StudentAttendance::class);
    }

    public function curricula()
    {
        return $this->belongsTo(Curriculum::class, 'syllabus_id', 'syllabus_id');
    }
}
