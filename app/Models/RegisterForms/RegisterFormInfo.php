<?php

namespace App\Models\RegisterForms;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Models\RegisterForms\RegisterFormInfo
 *
 * @property int $id
 * @property string $registerable_type
 * @property int $registerable_id
 * @property string $first_name
 * @property string $last_name
 * @property int $identity_number
 * @property string $phone
 * @property string $email
 * @property int $gender 0 - გოგო , 1 - ბიჭი
 * @property \Illuminate\Support\Carbon $date_of_birth
 * @property-read Model|\Eloquent $registerable
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereIdentityNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereRegisterableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormInfo whereRegisterableType($value)
 * @mixin \Eloquent
 */
class RegisterFormInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'identity_number',
        'phone',
        'email',
        'gender',
        'date_of_birth'
    ];

    protected $casts = [
        'date_of_birth' => 'date:d-m-Y'
    ];

    public $timestamps = false;

    public function registerable(): MorphTo
    {
        return $this->morphTo();
    }

    public function dateOfBirth(): Attribute
    {
        return Attribute::make(
            set: fn($value) => Carbon::createFromFormat('d-m-Y', $value)
        );
    }
}
