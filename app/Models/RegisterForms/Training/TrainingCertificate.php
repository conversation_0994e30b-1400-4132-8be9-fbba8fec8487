<?php

namespace App\Models\RegisterForms\Training;

use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingCertificate
 *
 * @property int $id
 * @property int $training_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingCertificate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingCertificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'title'
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class,
            'training_register_id',
            'id');
    }
}
