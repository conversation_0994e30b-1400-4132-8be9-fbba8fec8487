<?php

namespace App\Models\RegisterForms\Training;

use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingRegisterInfo
 *
 * @property int $id
 * @property int $training_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterInfo whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingRegisterInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'title'
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class);
    }
}
