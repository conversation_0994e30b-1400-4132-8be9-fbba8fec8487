<?php

namespace App\Models\RegisterForms\Training;

use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingRegisterAddress
 *
 * @property int $id
 * @property int $training_register_id
 * @property string $city
 * @property string $street
 * @property string $city_of_birth
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereCityOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterAddress whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingRegisterAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'city_of_birth',
        'city',
        'street'
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class);
    }
}
