<?php

namespace App\Models\RegisterForms\Training;

use App\Models\Reestry\AcademicDegree;
use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingRegisterEducation
 *
 * @property int $id
 * @property int $training_register_id
 * @property string $university
 * @property string $faculty
 * @property int $academic_degree_id
 * @property int $start_date
 * @property int $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree|null $academicDegree
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereFaculty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingRegisterEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'university',
        'faculty',
        'academic_degree_id',
        'start_date',
        'end_date'
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }
}
