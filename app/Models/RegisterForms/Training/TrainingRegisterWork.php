<?php

namespace App\Models\RegisterForms\Training;

use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingRegisterWork
 *
 * @property int $id
 * @property int $training_register_id
 * @property string $organization
 * @property string $position
 * @property int $employment_field
 * @property string $start_date
 * @property string|null $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereEmploymentField($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereOrganization($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterWork whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingRegisterWork extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'organization',
        'position',
        'employment_field',
        'start_date',
        'end_date',
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class);
    }
}
