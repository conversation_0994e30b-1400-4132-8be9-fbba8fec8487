<?php

namespace App\Models\RegisterForms\Training;

use App\Models\RegisterForms\TrainingRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Training\TrainingRegisterProgram
 *
 * @property int $id
 * @property int $training_register_id
 * @property int $program_id
 * @property int $level_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegister|null $trainingRegister
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereTrainingRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegisterProgram whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrainingRegisterProgram extends Model
{
    use HasFactory;

    protected $fillable = [
        'training_register_id',
        'program_id',
        'level_id'
    ];

    const PROGRAM_NAMES = [
        1 => 'Word',
        2 => 'Excel',
        3 => 'PowerPoint'
    ];

    const PROGRAM_LEVEL = [
        1 => 'დაბალი',
        2 => 'საშუალო',
        3 => 'მაღალი'
    ];

    public function trainingRegister(): BelongsTo
    {
        return $this->belongsTo(TrainingRegister::class);
    }
}
