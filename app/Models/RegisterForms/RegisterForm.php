<?php

namespace App\Models\RegisterForms;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\RegisterForms\RegisterForm
 *
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterForm newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterForm newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterForm query()
 * @mixin \Eloquent
 */
class RegisterForm extends Model
{
    use HasFactory;

    protected $fillable = [
        'title'
    ];
}
