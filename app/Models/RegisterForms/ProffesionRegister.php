<?php

namespace App\Models\RegisterForms;

use App\Models\Reestry\Program\Program;
use App\Models\RegisterForms\Proffesion\ProffesionRegisterEducation;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Models\RegisterForms\ProffesionRegister
 *
 * @property int $id
 * @property string $first_name_en
 * @property string $last_name_en
 * @property string $photo
 * @property string $identity_number_copy
 * @property string $school
 * @property int $school_finish_date
 * @property int $program_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|ProffesionRegisterEducation[] $educations
 * @property-read int|null $educations_count
 * @property-read Program|null $program
 * @property-read \App\Models\RegisterForms\RegisterFormInfo|null $registerFormInfo
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereFirstNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereIdentityNumberCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereLastNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereSchool($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereSchoolFinishDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister filter(\App\Filters\QueryFilters $filters)
 * @property int $flow_id
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegister whereFlowId($value)
 * @mixin \Eloquent
 */
class ProffesionRegister extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'first_name_en',
        'last_name_en',
        'photo',
        'identity_number_copy',
        'school',
        'school_finish_date',
        'program_id',
        'flow_id'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function registerFormInfo(): MorphOne
    {
        return $this->morphOne(RegisterFormInfo::class, 'registerable');
    }

    public function educations(): HasMany
    {
        return $this->hasMany(
            ProffesionRegisterEducation::class,
            'proffesion_register_id',
            'id'
        );
    }

}
