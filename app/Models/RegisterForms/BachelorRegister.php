<?php

namespace App\Models\RegisterForms;

use App\Models\EnglishLevel;
use App\Models\Reestry\Program\Program;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Models\RegisterForms\BachelorRegister
 *
 * @property int $id
 * @property string $first_name_en
 * @property string $last_name_en
 * @property int $program_id
 * @property string $parent_phone
 * @property string $address
 * @property string $school_document
 * @property string $photo
 * @property string $identity_number_copy
 * @property string $school
 * @property string|null $military_accounting
 * @property string $payment_document
 * @property int $english_level_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read EnglishLevel|null $englishLevel
 * @property-read Program|null $program
 * @property-read \App\Models\RegisterForms\RegisterFormInfo|null $registerFormInfo
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister query()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereEnglishLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereFirstNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereIdentityNumberCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereLastNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereMilitaryAccounting($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereParentPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister wherePaymentDocument($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereSchool($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereSchoolDocument($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister filter(\App\Filters\QueryFilters $filters)
 * @property int $flow_id
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereFlowId($value)
 * @property int $status
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorRegister whereStatus($value)
 * @mixin \Eloquent
 */
class BachelorRegister extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'parent_phone',
        'photo',
        'identity_number_copy',
        'school',
        'program_id',
        'flow_id',
        'english_level_id',
        'address',
        'school_document',
        'military_accounting',
        'payment_document',
        'first_name_en',
        'last_name_en',
        'status'
    ];

    public function registerFormInfo(): MorphOne
    {
        return $this->morphOne(RegisterFormInfo::class, 'registerable');
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function englishLevel(): BelongsTo
    {
        return $this->belongsTo(EnglishLevel::class);
    }

}
