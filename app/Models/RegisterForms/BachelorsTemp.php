<?php

namespace App\Models\RegisterForms;

use App\Models\Reestry\LearnYear;
use App\Models\Reestry\Program\Program;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\BachelorsTemp
 *
 * @property int $id
 * @property int $program_id
 * @property int $flow_id
 * @property string $personal_id
 * @property string $first_name
 * @property string $last_name
 * @property int $is_registered
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read LearnYear|null $flow
 * @property-read Program|null $program
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp query()
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereFlowId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereIsRegistered($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp wherePersonalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BachelorsTemp whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class BachelorsTemp extends Model
{
    use HasFactory;

    protected $table = 'bachelors_temp';

    protected $fillable = [
        'program_id',
        'flow_id',
        'personal_id',
        'first_name',
        'last_name'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function flow(): BelongsTo
    {
        return $this->belongsTo(LearnYear::class, 'flow_id');
    }
}
