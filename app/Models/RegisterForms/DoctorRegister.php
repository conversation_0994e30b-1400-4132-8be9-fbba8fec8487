<?php

namespace App\Models\RegisterForms;

use App\Models\EnglishLevel;
use App\Models\Master\MasterRecommendation;
use App\Models\Reestry\Program\Program;
use App\Models\RegisterForms\Doctor\DoctorCertificate;
use App\Models\RegisterForms\Doctor\DoctorEducation;
use App\Models\RegisterForms\Doctor\DoctorRecommendation;
use App\Models\RegisterForms\Doctor\DoctorRegisterInfo;
use App\Models\RegisterForms\Master\MasterRegisterInfo;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Models\RegisterForms\DoctorRegister
 *
 * @property int $id
 * @property int $english_level_id
 * @property string $photo
 * @property string $identity_number_copy
 * @property string $cv
 * @property string $about_university
 * @property int $program_id
 * @property string $address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|DoctorCertificate[] $certificates
 * @property-read int|null $certificates_count
 * @property-read EnglishLevel|null $englishLevel
 * @property-read Program|null $program
 * @property-read \App\Models\RegisterForms\RegisterFormInfo|null $registerFormInfo
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister query()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereAboutUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereCv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereEnglishLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereIdentityNumberCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister filter(\App\Filters\QueryFilters $filters)
 * @property int $flow_id
 * @property-read \Illuminate\Database\Eloquent\Collection|DoctorRecommendation[] $doctorRecommendations
 * @property-read int|null $doctor_recommendations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|DoctorEducation[] $educations
 * @property-read int|null $educations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|DoctorRegisterInfo[] $infos
 * @property-read int|null $infos_count
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegister whereFlowId($value)
 * @mixin \Eloquent
 */
class DoctorRegister extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'english_level_id',
        'photo',
        'identity_number_copy',
        'cv',
        'about_university',
        'program_id',
        'flow_id',
        'address'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function englishLevel(): BelongsTo
    {
        return $this->belongsTo(EnglishLevel::class);
    }

    public function registerFormInfo(): MorphOne
    {
        return $this->morphOne(RegisterFormInfo::class, 'registerable');
    }

    public function certificates(): HasMany
    {
        return $this->hasMany(DoctorCertificate::class,
            'doctor_register_id',
            'id');
    }

    public function doctorRecommendations(): HasMany
    {
        return $this->hasMany(DoctorRecommendation::class);
    }

    public function infos(): HasMany
    {
        return $this->hasMany(
            DoctorRegisterInfo::class,
            'doctor_register_id',
            'id'
        );
    }

    public function educations(): HasMany
    {
        return $this->hasMany(
            DoctorEducation::class,
            'doctor_register_id',
            'id'
        );
    }
}
