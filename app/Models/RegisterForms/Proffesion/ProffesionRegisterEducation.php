<?php

namespace App\Models\RegisterForms\Proffesion;

use App\Models\Reestry\AcademicDegree;
use App\Models\RegisterForms\ProffesionRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Proffesion\ProffesionRegisterEducation
 *
 * @property int $id
 * @property int $proffesion_register_id
 * @property string $university
 * @property string $faculty
 * @property int $start_date
 * @property int $end_date
 * @property int $academic_degree_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AcademicDegree|null $academicDegree
 * @property-read ProffesionRegister|null $proffesionRegister
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereAcademicDegreeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereFaculty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereProffesionRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProffesionRegisterEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ProffesionRegisterEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'proffesion_register_id',
        'university',
        'faculty',
        'academic_degree_id',
        'start_date',
        'end_date'
    ];

    public function proffesionRegister(): BelongsTo
    {
        return $this->belongsTo(ProffesionRegister::class);
    }

    public function academicDegree(): BelongsTo
    {
        return $this->belongsTo(AcademicDegree::class);
    }
}
