<?php

namespace App\Models\RegisterForms\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Master\MasterRegisterInfo
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterInfo whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterRegisterInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'master_register_id',
        'title'
    ];

    public function masterRegister(): BelongsTo
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
