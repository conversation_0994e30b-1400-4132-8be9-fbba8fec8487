<?php

namespace App\Models\RegisterForms\Master;

use App\Models\RegisterForms\MasterRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Master\MasterRegisterEducation
 *
 * @property int $id
 * @property int $master_register_id
 * @property string $university
 * @property string $faculty
 * @property int $start_date
 * @property int $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read MasterRegister|null $masterRegister
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereFaculty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereMasterRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterRegisterEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterRegisterEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'master_register_id',
        'university',
        'faculty',
        'start_date',
        'end_date'
    ];

    public function masterRegister(): BelongsTo
    {
        return $this->belongsTo(MasterRegister::class);
    }
}
