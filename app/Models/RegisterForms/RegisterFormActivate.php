<?php

namespace App\Models\RegisterForms;

use App\Models\Reestry\LearnYear;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\RegisterForms\RegisterFormActivate
 *
 * @property int $id
 * @property int $learn_year_id
 * @property string $start_date
 * @property string|null $end_date
 * @property string $url
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read LearnYear|null $learnYear
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereLearnYearId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterFormActivate whereUrl($value)
 * @mixin \Eloquent
 */
class RegisterFormActivate extends Model
{
    use HasFactory;

    protected $fillable = [
        'learn_year_id',
        'start_date',
        'end_date',
        'url'
    ];

    public function learnYear(): BelongsTo
    {
        return $this->belongsTo(LearnYear::class);
    }

    public function startDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Carbon::parse($value),
            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
        );
    }

    public function endDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Carbon::parse($value),
            set: fn($value) => Carbon::createFromFormat('d/m/Y', $value)
        );
    }
}
