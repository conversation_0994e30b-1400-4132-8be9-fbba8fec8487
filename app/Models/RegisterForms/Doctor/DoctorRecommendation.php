<?php

namespace App\Models\RegisterForms\Doctor;

use App\Models\RegisterForms\DoctorRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Doctor\DoctorRecommendation
 *
 * @property int $id
 * @property int $doctor_register_id
 * @property string $person
 * @property string $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read DoctorRegister|null $doctorRegister
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation query()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation whereDoctorRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation wherePerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRecommendation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DoctorRecommendation extends Model
{
    use HasFactory;

    protected $fillable = [
        'doctor_register_id',
        'person',
        'phone'
    ];

    public function doctorRegister(): BelongsTo
    {
        return $this->belongsTo(DoctorRegister::class);
    }
}
