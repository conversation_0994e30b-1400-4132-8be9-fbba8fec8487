<?php

namespace App\Models\RegisterForms\Doctor;

use App\Models\RegisterForms\DoctorRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Doctor\DoctorCertificate
 *
 * @property int $id
 * @property int $doctor_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read DoctorRegister|null $doctorRegister
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate query()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate whereDoctorRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorCertificate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DoctorCertificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'doctor_register_id',
        'title'
    ];

    public function doctorRegister(): BelongsTo
    {
        return $this->belongsTo(DoctorRegister::class,
            'doctor_register_id',
            'id');
    }
}
