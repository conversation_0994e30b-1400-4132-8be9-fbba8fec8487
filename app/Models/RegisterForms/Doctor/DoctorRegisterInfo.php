<?php

namespace App\Models\RegisterForms\Doctor;

use App\Models\RegisterForms\DoctorRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Doctor\DoctorRegisterInfo
 *
 * @property int $id
 * @property int $doctor_register_id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read DoctorRegister|null $doctorRegister
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo whereDoctorRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorRegisterInfo whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DoctorRegisterInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'doctor_register_id',
        'title'
    ];

    public function doctorRegister(): BelongsTo
    {
        return $this->belongsTo(DoctorRegister::class);
    }
}
