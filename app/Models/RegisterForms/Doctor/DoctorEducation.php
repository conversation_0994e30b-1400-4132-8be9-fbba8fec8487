<?php

namespace App\Models\RegisterForms\Doctor;

use App\Models\RegisterForms\DoctorRegister;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\RegisterForms\Doctor\DoctorEducation
 *
 * @property int $id
 * @property int $doctor_register_id
 * @property string $university
 * @property string $faculty
 * @property int $start_date
 * @property int $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read DoctorRegister|null $doctorRegister
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation query()
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereDoctorRegisterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereFaculty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereUniversity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DoctorEducation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DoctorEducation extends Model
{
    use HasFactory;

    protected $fillable = [
        'doctor_register_id',
        'university',
        'faculty',
        'start_date',
        'end_date'
    ];

    public function doctorRegister(): BelongsTo
    {
        return $this->belongsTo(DoctorRegister::class);
    }
}
