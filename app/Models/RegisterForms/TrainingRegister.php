<?php

namespace App\Models\RegisterForms;

use App\Models\EnglishLevel;
use App\Models\Reestry\Program\Program;
use App\Models\RegisterForms\Training\TrainingCertificate;
use App\Models\RegisterForms\Training\TrainingRegisterAddress;
use App\Models\RegisterForms\Training\TrainingRegisterEducation;
use App\Models\RegisterForms\Training\TrainingRegisterInfo;
use App\Models\RegisterForms\Training\TrainingRegisterProgram;
use App\Models\RegisterForms\Training\TrainingRegisterWork;
use App\Traits\HasFilters;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Models\RegisterForms\TrainingRegister
 *
 * @property int $id
 * @property string $first_name_en
 * @property string $last_name_en
 * @property int $english_level_id
 * @property string $photo
 * @property string $identity_number_copy
 * @property string $cv
 * @property int $program_id
 * @property int $employment_status
 * @property string|null $comment
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read TrainingRegisterAddress|null $address
 * @property-read \Illuminate\Database\Eloquent\Collection|TrainingCertificate[] $certificates
 * @property-read int|null $certificates_count
 * @property-read \Illuminate\Database\Eloquent\Collection|TrainingRegisterEducation[] $educations
 * @property-read int|null $educations_count
 * @property-read EnglishLevel|null $englishLevel
 * @property-read \Illuminate\Database\Eloquent\Collection|TrainingRegisterInfo[] $infos
 * @property-read int|null $infos_count
 * @property-read Program|null $program
 * @property-read \Illuminate\Database\Eloquent\Collection|TrainingRegisterProgram[] $programs
 * @property-read int|null $programs_count
 * @property-read \App\Models\RegisterForms\RegisterFormInfo|null $registerFormInfo
 * @property-read \Illuminate\Database\Eloquent\Collection|TrainingRegisterWork[] $works
 * @property-read int|null $works_count
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister query()
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereCv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereEmploymentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereEnglishLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereFirstNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereIdentityNumberCopy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereLastNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister wherePhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister filter(\App\Filters\QueryFilters $filters)
 * @property int $flow_id
 * @method static \Illuminate\Database\Eloquent\Builder|TrainingRegister whereFlowId($value)
 * @mixin \Eloquent
 */
class TrainingRegister extends Model
{
    use HasFactory,HasFilters;

    protected $fillable = [
        'first_name_en',
        'last_name_en',
        'english_level_id',
        'identity_number_copy',
        'cv',
        'program_id',
        'flow_id',
        'photo',
        'employment_status'
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function address(): HasOne
    {
        return $this->hasOne(
            TrainingRegisterAddress::class,
            'training_register_id',
            'id'
        );
    }

    public function englishLevel(): BelongsTo
    {
        return $this->belongsTo(EnglishLevel::class);
    }

    public function registerFormInfo(): MorphOne
    {
        return $this->morphOne(
            RegisterFormInfo::class,
            'registerable'
        );
    }

    public function certificates(): HasMany
    {
        return $this->hasMany(
            TrainingCertificate::class,
            'training_register_id',
            'id'
        );
    }

    public function programs(): HasMany
    {
        return $this->hasMany(
            TrainingRegisterProgram::class,
            'training_register_id',
            'id'
        );
    }

    public function educations(): HasMany
    {
        return $this->hasMany(
            TrainingRegisterEducation::class,
            'training_register_id',
            'id'
        );
    }

    public function infos(): HasMany
    {
        return $this->hasMany(
            TrainingRegisterInfo::class,
            'training_register_id',
            'id'
        );
    }

    public function works(): HasMany
    {
        return $this->hasMany(
            TrainingRegisterWork::class,
            'training_register_id',
            'id'
        );
    }

}
