<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AssessmentComponent
 *
 * @property int $id
 * @property string $name_ka
 * @property string $name_en
 * @property int $type_id 1 - standard, 2 - middle, 3 - final
 * @property int $is_parent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent query()
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereIsParent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereNameEn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereNameKa($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AssessmentComponent whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class AssessmentComponent extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ka',
        'name_en',
        'type_id',
        'is_parent',
    ];

    protected $casts = [
        'type_id' => 'integer',
        'is_parent' => 'integer',
    ];
}
