<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemLog extends Model
{
    use HasFactory;

    protected $table = 'system_logs';

    const CREATE_ACTION = 'create';
    const UPDATE_ACTION = 'update';
    const DELETE_ACTION = 'delete';

    protected $fillable = [
        'action_data',
        'model_name',
        'action_type',
        'request_ip',
        'request_device',
    ];

    protected $casts = [
        'action_data' => 'array'
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            $user = User::whereId(auth()->id())->first();
            $model->request_ip = $model->request_ip ?? request()->ip();
            $model->request_device = $model->request_device ?? request()->header('User-Agent');
            if(auth()->id()) {
                $model->user_id = auth()->id() ?? null;
                $model->user_type = $user->userType?->title ?? null;
                $model->full_name = $user->name;
            }
        });
    }
}
