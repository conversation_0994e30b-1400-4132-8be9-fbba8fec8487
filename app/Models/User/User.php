<?php

namespace App\Models\User;

use App\Models\eDoc\eDoc;
use App\Models\HR\Administration\HrAdministrationFile;
use App\Models\Notification;
use App\Models\Own;
use App\Models\ProgramUser;
use App\Models\Reestry\Program\Program;
use App\Models\Reestry\Student\Student;
use App\Models\Role;
use App\Models\RoleUser;
use App\Models\SystemLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * App\Models\User\User
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string|null $email_verified_at
 * @property string $password
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property string|null $remember_token
 * @property int|null $current_team_id
 * @property int $user_type_id
 * @property string|null $profile_photo_path
 * @property int $is_super_admin
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Client[] $clients
 * @property-read int|null $clients_count
 * @property-read \Illuminate\Database\Eloquent\Collection|eDoc[] $edoc
 * @property-read int|null $edoc_count
 * @property-read \Illuminate\Database\Eloquent\Collection|HrAdministrationFile[] $hrAdministrationFiles
 * @property-read int|null $hr_administration_files_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Own[] $owns
 * @property-read int|null $owns_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Program[] $programs
 * @property-read int|null $programs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Role[] $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Token[] $tokens
 * @property-read int|null $tokens_count
 * @property-read \App\Models\User\UserType|null $userType
 * @method static \Database\Factories\User\UserFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCurrentTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsSuperAdmin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereProfilePhotoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserTypeId($value)
 * @property-read Student|null $student
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    protected static function boot(): void
    {
        parent::boot();

        // Creating event
        static::creating(function ($model) {
            self::systemLog($model, SystemLog::CREATE_ACTION);
        });

        // Updating event
        static::updating(function ($model) {
            $originalData = $model->getOriginal();
            $updatedData = array_diff_assoc($model->toArray(), $originalData);
            self::systemLog($updatedData, SystemLog::UPDATE_ACTION);
        });

        // Deleting event
        static::deleting(function ($model) {
            self::systemLog($model, SystemLog::DELETE_ACTION);
        });
    }

    private static function systemLog($model, $actionType): void
    {
        SystemLog::query()->create([
            'action_data' => $model,
            'model_name' => self::class,
            'action_type' => $actionType
        ]);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
        'user_type_id',
        'is_super_admin'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function userType(): BelongsTo
    {
        return $this->belongsTo(UserType::class);
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_user', 'user_id', 'role_id');
    }

    public function permissions()
    {
        return $this->roles->flatMap(function ($role) {
            return $role->permissions;
        })->unique('id');
    }

    public function hasPermission($permission)
    {
        $rolesWithPermissions = $this->roles->load('permissions');
        return $rolesWithPermissions->pluck('permissions')->flatten()->pluck('title')->contains($permission);
    }

    public function programs(): BelongsToMany
    {
        return $this->belongsToMany(Program::class, 'user_program')->using(ProgramUser::class);
    }

    public function owns(): BelongsToMany
    {
        return $this->belongsToMany(Own::class);
    }

    public function hasPrograms()
    {
        return $this->programs()->exists();
    }

    public function hrAdministrationFiles(): HasMany
    {
        return $this->hasMany(
            HrAdministrationFile::class,
            'user_id',
            'id'
        );
    }

    public function edoc(): HasMany
    {
        return $this->hasMany(eDoc::class);
    }

    public function student(): HasOne
    {
        return $this->hasOne(Student::class, 'user_id', 'id');
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'user_id', 'id');
    }
}
