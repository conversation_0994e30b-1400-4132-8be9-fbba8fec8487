<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserFullAccess
 *
 * @property int $id
 * @property int $user_id
 * @property int $model_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserFullAccess whereUserId($value)
 * @mixin \Eloquent
 */
class UserFullAccess extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'model_id'
    ];
}
