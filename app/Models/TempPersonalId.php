<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TempPersonalId
 *
 * @property int $id
 * @property string $personal_id
 * @method static \Illuminate\Database\Eloquent\Builder|TempPersonalId newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempPersonalId newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempPersonalId query()
 * @method static \Illuminate\Database\Eloquent\Builder|TempPersonalId whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempPersonalId wherePersonalId($value)
 * @mixin \Eloquent
 */
class TempPersonalId extends Model
{
    use HasFactory;

    protected $table = 'temp_personal_ids';

    protected $fillable = ['personal_id'];
}
