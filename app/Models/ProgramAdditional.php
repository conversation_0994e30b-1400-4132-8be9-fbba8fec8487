<?php

namespace App\Models;

use App\Models\Reestry\Program\Program;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProgramAdditional extends Model
{
    use HasFactory;

    protected $table = 'program_additionals';

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }
}
