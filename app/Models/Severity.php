<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Severity
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Severity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Severity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Severity query()
 * @method static \Illuminate\Database\Eloquent\Builder|Severity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Severity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Severity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Severity whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Severity extends Model
{
    use HasFactory;

    const LOW = 1;
    const MEDIUM = 2;
    const HIGH = 3;
    const CRITICAL = 4;

    protected $fillable = [
        'name'
    ];
}
