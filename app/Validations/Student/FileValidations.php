<?php

namespace App\Validations\Student;


class FileValidations
{

    public function rules(): array
    {
        return [
            'photo' => 'mimes:jpeg,png,jpg,gif,svg|max:100000', // შესაცვლელია
            'cv_file_name' => 'mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000', // შესაცვლელია
            'diploma_file_name' => 'mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000', // შესაცვლელია
            'transcript_file_name' => 'mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000', // შესაცვლელია
            'motivation_article_file_name' => 'mimes:docx,pdf,pptx,txt,xlsx,rtf|max:100000', // შესაცვლელია
        ];
    }

}
