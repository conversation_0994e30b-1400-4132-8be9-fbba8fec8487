<?php
namespace App\Mail;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class FinanceReportMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(public $filePath, public $dataRange, public $fileName)
    {
        //
    }

    public function envelope(): Envelope
    {
        $start = Carbon::now()->subDays(6)->format('d.m.Y');
        $end = Carbon::now()->format('d.m.Y');

        return new Envelope(
            subject: "Finance Report Mail-$start-$end",
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.finance-report',
            with: ['dataRange' => $this->dataRange]
        );
    }

    public function build()
    {
        return $this->attach(
            $this->filePath,
            [
                'as' => $this->fileName,
                'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ]
        );
    }
}
