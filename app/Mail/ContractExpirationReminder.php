<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ContractExpirationReminder extends Mailable
{
    use Queueable, SerializesModels;

    public $employee;
    public $position;
    public $contractStart;
    public $contractEnd;
    public $contractDuration;
    public $daysRemaining;
    public $adminUrl;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($employee, $position, $contractStart, $contractEnd, $contractDuration, $daysRemaining)
    {
        $this->employee = $employee;
        $this->position = $position;
        $this->contractStart = $contractStart;
        $this->contractEnd = $contractEnd;
        $this->contractDuration = $contractDuration;
        $this->daysRemaining = $daysRemaining;
        $this->adminUrl = config('app.url') . '/admin/academic';
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('კონტრაქტის ვადის შეხსენება - ' . $this->employee->user->name)
                    ->view('emails.contract_expiration_reminder');
    }
}
