<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\InvokableRule;

class Maxsize implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param string $attribute
     * @param mixed $value
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail)
    {
        $sizeInMegabytes = 60;
        $total_size = array_reduce($value, function ($sum, $item) {
            // each item is UploadedFile Object
            $sum += filesize($item->path());
            return $sum;
        });

        if ($total_size > 1000 * 1000 * $sizeInMegabytes) {
            $fail('Total images size should be maximum 60 Megabytes');
        }
    }
}
