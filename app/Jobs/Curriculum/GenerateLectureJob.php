<?php

namespace App\Jobs\Curriculum;

use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumStudentGroup;
use App\Models\Curriculum\LectureStudent;
use App\Models\JournalCreatingLog;
use App\Models\Lectures\Lecture;
use App\Models\PrerequisiteSyllabus;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateLectureJob implements ShouldQueue
{
    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels;

    public array $studentGroups;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Curriculum $curriculum, $studentGroups
    )
    {
        $this->studentGroups = $studentGroups;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $studentList = [];
        $timeIds = [];
        Lecture::where('syllabus_id', $this->curriculum->syllabus_id)
            ->each(function ($lecture) {
                $lecture->students()->detach();
                $lecture->delete();
            });
        foreach ([$this->curriculum->lecture] as $lecture) {
            $hours = [];
            foreach ($lecture->times as $time) {
                if (!isset($hours[$time->is_lecture])) {
                    $hours[$time->is_lecture] = 0;
                }

                $startDate = Carbon::createFromFormat('Y-m-d', $time->lecturer_start_date);
                $endDate = Carbon::createFromFormat('Y-m-d', $time->lecturer_end_date);

                //echo "Lecture: {$lecture->id}, Time: {$time->id}, Start Date: {$startDate}, End Date: {$endDate}\n";

                if ($startDate->dayOfWeek > $time['week_day']) {
                    $startDate = $startDate->addDays(7 + $time['week_day'] - $startDate->dayOfWeek);
                } elseif ($startDate->dayOfWeek < $time['week_day']) {
                    $startDate = $startDate->addDays($time['week_day'] - $startDate->dayOfWeek);
                }

                do {
                    $hours[$time->is_lecture]++;
                    //echo "  Creating lecture for date: {$startDate}\n";
                    $lecture = Lecture::create([
                        'syllabus_id' => $this->curriculum->syllabus_id,
                        'start_time' => $time->start_time,
                        'end_time' => $time->end_time,
                        'week_day' => $time->week_day,
                        'lecture_date' => $startDate,
                        'lecture_number' => $hours[$time->is_lecture],
                        'auditorium_id' => $time->auditorium_id,
                        'is_lecture' => $time->is_lecture,
                        'lecturer_id' => $time->lecturer_id,
                        'payment_per_hour' => $time->payment_per_hour,
                    ]); //syllabus_id , lecture_id , student_id , is_attend , comment - დასწრება როგორ შევინახოთ
                    $prerequisiteSyllabusIds = PrerequisiteSyllabus::query()
                        ->where('syllabus_id', $this->curriculum->syllabus_id)
                        ->pluck('prerequisite_id')
                    ;
                    $history = StudentSyllabusHistory::query()
                        ->whereIn('syllabus_id', $prerequisiteSyllabusIds)
                        ->get()
                    ;

                    $studentIds = Student::whereIn(
                        'group_id',
                        $time->studentGroups->pluck('id')->toArray()
                    )->where('status_id', '=', StudentStatusList::ACTIVE)
                        ->pluck('id')
                        ->filter(function ($row) use ($prerequisiteSyllabusIds, $history){
                            if ($prerequisiteSyllabusIds->isEmpty())
                            {
                                return true;
                            }
                            $totalPassed =
//                                $prerequisiteSyllabusIds->count() == $history->count() &&
                                $history->where('student_id', $row)->where('is_passed', false)->isEmpty();                            if (!$totalPassed)
                            {
                                JournalCreatingLog::query()->updateOrCreate([
                                    'student_id' => $row,
                                    'syllabus_id' => $this->curriculum->syllabus_id
                                ]);
                            }
                            return $totalPassed;
                        })

                        ->toArray();
                    if (!in_array($time->id, $timeIds)) {
                        $timeIds[] = $time->id;
                        array_push($studentList, ...$studentIds);
                    }
                    $lecture->students()->attach($studentIds);
                    $startDate = $startDate->addWeek();
                } while ($startDate <= $endDate);
            }
        }
        $studentList = array_unique($studentList);
        foreach ($studentList as $studentId) {
            StudentSyllabusHistory::create([
                'syllabus_id' => $this->curriculum->syllabus_id,
                'student_id' => $studentId
            ]);
        }
    }
}
