<?php

namespace App\Jobs\Curriculum;

use App\Models\Curriculum\CurriculumLecture;
use App\Models\Lectures\Lecture;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class FillLectureStudentGroupsJob implements ShouldQueue
{
    use Dispatchable,
        InteractsWithQueue,
        Queueable,
        SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private CurriculumLecture $lecture,
    ) {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $syllabus = $this->lecture->curriculum->syllabus;

        if ($this->lecture->curriculum->start_date >= Carbon::now()) {
            foreach ($this->lecture->times as $time) {
                $lecture = Lecture::where('syllabus_id', $syllabus->id)
                    ->where('start_time', $time->start_time)
                    ->where('end_time', $time->end_time)
                    ->where('week_day', $time->week_day)
                    ->where('lecture_date', $time->lecture_date)
                    ->where('lecture_number', $time->lecture_number)
                    ->where('auditorium_id', $time->auditorium_id)
                    ->where('is_lecture', $time->is_lecture)
                    ->where('lecturer_id', $time->lecturer_id)
                    ->where('payment_per_hour', $time->payment_per_hour)
                    ->first();

                if ($lecture) {
                    foreach ($this->lecture->studentGroups as $groups) {
                        foreach ($groups->students as $student) {
                            if ($student->syllabusHistory->contains($syllabus)) {
                                continue;
                            }

                            $prerequisites = $syllabus->prerequisites->filter(function ($prerequisite) use ($student) {
                                return !$student->syllabusHistory->contains($prerequisite);
                            });

                            if ($prerequisites->count() > 0) {
                                continue;
                            }

                            $lecture->studentGroups()->attach($student->id);
                            $student->syllabusHistory()->attach($syllabus->id);
                        }
                    }
                } else {
                    Log::alert('Lecture not found', [
                        'lecture' => $this->lecture->id,
                        'time'    => $time->id,
                    ]);
                }
            }
        }
    }
}
