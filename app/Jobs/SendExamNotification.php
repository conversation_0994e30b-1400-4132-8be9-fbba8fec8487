<?php

namespace App\Jobs;

use App\Models\Assignment;
use App\Models\Message\Message;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Syllabus\Syllabus;
use App\Services\MessageService;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;

class SendExamNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected MessageService $messageService;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Assignment $assignment,
        private            $date,
    )
    {
        $this->messageService = new MessageService();
        $this->date = $date;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        (new NotificationService())->sendExamNotification($this->assignment, $this->date);
    }
}
