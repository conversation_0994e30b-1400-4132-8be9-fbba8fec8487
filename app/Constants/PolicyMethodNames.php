<?php
namespace App\Constants;

enum PolicyMethodNames{
    case INDEX;
    case CREATE;
    case UPDATE;
    case DELETE;
    case EXPORT;

    public static function methodName($name)
    {
        return match($name){
            self::INDEX => 'index',
            self::CREATE => 'store',
            self::UPDATE => 'update',
            self::DELETE => 'destroy',
            self::EXPORT => 'export'
        };
    }

    public static function errorMessage($name)
    {
        return match($name){
            self::INDEX => "You aren't allowed to view data",
            self::CREATE => "You aren't allowed to create data",
            self::UPDATE => "You aren't allowed to update data",
            self::DELETE => "You aren't allowed to delete data",
            self::EXPORT => "You aren't allowed to export data",
        };
    }
}


