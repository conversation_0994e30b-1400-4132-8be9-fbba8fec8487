<?php

namespace App\Console\Commands;

use App\Models\Finance\Finance;
use App\Models\Finance\FinanceScheduler;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Severity;
use App\Services\FinanceService;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class BlockFinanceStudent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:block-quarter-student';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $currentQuarter = (new FinanceService())->getQuarter(now()->year, now()->month, now()->day);
        $blockedStudents = Student::select('id', 'user_id', 'personal_id')
            ->whereStatusId(StudentStatusList::ACTIVE)
            ->get();
        foreach ($blockedStudents as $blockedStudent) {
            $financeScheduler = FinanceScheduler::where('is_active', '=', 1)
                ->where('user_id', $blockedStudents->user_id)->exists();
            if ($financeScheduler) {
                continue;
            } else {
                $totalDebt = Finance::where('piradi_nom', $blockedStudent->personal_id)
                    ->where('Qrter', $currentQuarter)
                    ->first()->kvartlis_jami;
                if ($totalDebt > 0) {
                    $student = Student::where('user_id', $blockedStudent->user_id)
                        ->first();
                    $student->update([
                        'status_id' => StudentStatusList::STOPPED
                    ]);
                    $notificationService = (new NotificationService());
                    $notification = $notificationService->sendNotification(
                        userId: $blockedStudent->user_id,
                        severityId: Severity::CRITICAL,
                        text: "სტუდენტური სტატუსი შეგიჩერდათ , გადაიხადეთ დავალიანება აღსადგენად!"
                    );
                    $notificationService->publish($notification);
                }
            }

        }
    }
}
