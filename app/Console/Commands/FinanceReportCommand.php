<?php
namespace App\Console\Commands;

use App\Exports\Student\FinanceStatusLogExport;
use App\Mail\FinanceReportMail;
use App\Models\FinanceStudentStatusLog;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Mail;
use PhpOffice\PhpSpreadsheet\Exception;

class FinanceReportCommand extends Command
{
    protected $signature = 'send:finance-report';
    protected $description = 'Finance report for admin';

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function handle(): void
    {
        $dateRange = collect([
            (string) Carbon::now()->hour(16)->minute(14),
            (string) Carbon::now()->startOfWeek(CarbonInterface::SATURDAY)->hour(0)->minute(1),
        ])->sortKeysDesc()->values()->toArray();

        $logs = FinanceStudentStatusLog::query()
            ->with(['student.program','student' => function($student){
                $student->select('id', 'name', 'surname', 'personal_id', 'status_id', 'user_id', 'program_id');
            }])
            ->whereBetween('created_at', $dateRange)
            ->orderByDesc('id')
            ->get()
            ->unique('student_id');

        // Store the file temporarily
        $start = Carbon::now()->subDays(6)->format('d.m.Y');
        $end = Carbon::now()->format('d.m.Y');
        $fileName = "$start-$end-finance-status-update.xlsx";
        $filePath = storage_path("app/$fileName");

        Excel::store(new FinanceStatusLogExport($logs), $fileName);
        Mail::to('<EMAIL>')->send(new FinanceReportMail($filePath, $dateRange,$fileName));
        Mail::to('<EMAIL>')->send(new FinanceReportMail($filePath, $dateRange,$fileName));
        Mail::to('<EMAIL>')->send(new FinanceReportMail($filePath, $dateRange,$fileName));

        // Clean up the temporary file
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }
}
