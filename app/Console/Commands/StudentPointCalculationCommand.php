<?php

namespace App\Console\Commands;

use App\Models\Curriculum\Curriculum;
use App\Models\Reestry\Student\StudentAssignment;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use Illuminate\Console\Command;

class StudentPointCalculationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:student-point';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Student syllabus assignment point calculation';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        $syllabusIds = Curriculum::query()
            ->select(['id','syllabus_id','end_date'])
            ->whereHas('syllabus')
            //->whereIn('id', [5,14])
                ->where('flow_id', '=', Setting::where('key', '=', 'first_semester')
                ->first()->value)
            ->where('end_date', '<', today())
            ->pluck('syllabus_id')
        ;

        StudentAssignment::query()
            ->with('assignment.assessmentComponent', 'student')
            ->whereIn('syllabus_id', $syllabusIds)
            ->whereHas('student')
            ->get()
            ->groupBy(function($item){
                return $item['syllabus_id']."-".$item['student_id'];
            })
            ->map(function ($assignment){
                $point = round($assignment->sum('point'));
                $isPassed = $point >= 51;

                foreach ($assignment as $item)
                {
                    $isPassed = $item?->assignment?->assessmentComponent?->type_id  == 3
                        ? round($item->point) >= $item->assignment->min_score and $item->point > 0 and $point >= 51
                        : $isPassed
                    ;
                }

                StudentSyllabusHistory::query()
                    ->updateOrCreate([
                        'syllabus_id' => $assignment->first()->syllabus_id,
                        'student_id' => $assignment->first()->student_id,
                    ],[
                        'point' => $point,
                        'is_passed' => $isPassed,
                        'is_closed' => 1,
                    ]);
            })
        ;

        return self::SUCCESS;
    }
}
