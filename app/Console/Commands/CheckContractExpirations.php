<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\HR\Lecturer\Academic\HrAcademicLectureInfo;
use App\Models\HR\Lecturer\Academic\HrAcademicLecturePosition;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContractExpirationReminder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CheckContractExpirations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contracts:check-expirations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for contracts expiring in 45 days and send reminders';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $expirationDate = Carbon::now()->addDays(45)->format('Y-m-d');

        $expiringContracts = HrAcademicLecturePosition::with(['hrAcademicLectureInfo.user'])
            ->whereDate('contract_end', $expirationDate)
            ->get();

        $count = 0;

        foreach ($expiringContracts as $contract) {
            try {
                // Get admin email addresses from configuration
                $adminEmails = config('mail.admin_emails', ['<EMAIL>']);

                // Get position name
                $position = $contract->lecturerPosition->title ?? 'Unknown';

                // Calculate contract duration
                $contractDuration = $this->calculateContractDuration(
                    $contract->contract_start,
                    $contract->contract_end
                );

                // Send email to administrators
                Mail::to($adminEmails)->send(new ContractExpirationReminder(
                    $contract->hrAcademicLectureInfo,
                    $position,
                    Carbon::parse($contract->contract_start)->format('Y-m-d'),
                    Carbon::parse($contract->contract_end)->format('Y-m-d'),
                    $contractDuration,
                    45
                ));

                $count++;

                Log::info("Contract expiration reminder sent for {$contract->hrAcademicLectureInfo->user->name}");
            } catch (\Exception $e) {
                Log::error("Failed to send contract expiration reminder: " . $e->getMessage());
            }
        }

        $this->info("Sent {$count} contract expiration reminders");

        $this->info("Done");
    }

    /**
     * Calculate contract duration in years
     *
     * @param string $startDate Contract start date (Y-m-d)
     * @param string $endDate Contract end date (Y-m-d)
     * @return string Duration in years (with decimal for months)
     */
    private function calculateContractDuration($startDate, $endDate)
    {
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $interval = $start->diff($end);

        $years = $interval->y;
        $months = $interval->m;

        if ($months > 0) {
            $decimalYears = $years + ($months / 12);
            return number_format($decimalYears, 1);
        }

        return (string)$years;
    }
}
