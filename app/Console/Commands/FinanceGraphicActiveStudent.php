<?php

namespace App\Console\Commands;

use App\Models\Finance\Finance;
use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\FinanceLog;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Severity;
use App\Services\FinanceService;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class FinanceGraphicActiveStudent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:graphic-active-student';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $blockedStudents = Student::select('id', 'user_id', 'piradi_nom', 'status_id')
            ->where('status_id', StudentStatusList::STOPPED)
            ->get();
        foreach ($blockedStudents as $blockedStudent) {
            $financeScheduler = FinanceScheduler::whereUserId($blockedStudent->user_id)
                ->where('is_active', 1)
                ->first();
            if (!is_null($financeScheduler)) {
                $payableMoney = FinanceCalendar::whereFinanceSchedulerId($financeScheduler->id)
                    ->whereDate('end_date', '<', now()->format('Y-m-d'))
                    ->sum('amount');
                $financeCalendarStartDate = FinanceCalendar::where(
                    'finance_scheduler_id',
                    '=',
                    $financeScheduler->id
                )
                    ->orderBy('start_date', 'ASC')
                    ->first()->start_date;
                $currentPayedMoney = FinanceLog::whereDate(
                    'tarigi',
                    '>=',
                    $financeCalendarStartDate
                )
                    ->where(
                        'piradi_nom',
                        '=',
                        (new FinanceService(userId: $blockedStudent->user_id))->getStudentIdentityNumber()
                    )
                    ->sum('tanxa');
                if ($currentPayedMoney >= $payableMoney) {
                    $blockedStudent->update([
                        'status_id' => StudentStatusList::ACTIVE
                    ]);
                    $notificationService = new NotificationService();
                    $notification = $notificationService->sendNotification(
                        userId: $blockedStudent->user_id,
                        severityId: Severity::MEDIUM,
                        text: "სტუდენტური სტატუსი აღდგენილია!"
                    );
                    $notificationService->publish($notification);
                }
            } else {
                $currentQuarter = (new FinanceService())->getQuarter(now()->year, now()->month, now()->day);
                $studentFinance = Finance::where('piradi_nom', '=', $blockedStudent->piradi_nom)
                    ->where('Qrter', '=', $currentQuarter)
                    ->where('kvartalis_jami', '<=', 0);
                if ($studentFinance->exists()) {
                    $blockedStudent->update([
                        'status_id' => StudentStatusList::ACTIVE
                    ]);
                    $notificationService = new NotificationService();
                    $notification = $notificationService->sendNotification(
                        userId: $blockedStudent->user_id,
                        severityId: Severity::MEDIUM,
                        text: "სტუდენტური სტატუსი აღდგენილია!"
                    );
                    $notificationService->publish($notification);
                }
            }
        }
    }
}
