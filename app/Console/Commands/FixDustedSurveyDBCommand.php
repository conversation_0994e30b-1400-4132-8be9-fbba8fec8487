<?php

namespace App\Console\Commands;

use App\Models\Reestry\Student\Student;
use App\Models\Survey\SurveyActivation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDustedSurveyDBCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean:dusted-survey-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
//        SurveyActivation::query()
//            ->get()
//            ->map(function ($data) {
//                $student = Student::query()
//                    ->select(['id', 'user_id'])
//                    ->find($data?->user_id);
//                if ($student) {
//                    $data->update(['user_id' => $student->user_id]);
//                }
//            });

        $deletedRows = DB::delete("
            DELETE sa1 FROM survey_answers sa1
            INNER JOIN survey_answers sa2
            ON sa1 . survey_activation_id = sa2 . survey_activation_id
            and sa1 . survey_question_id = sa2 . survey_question_id
            and sa1 . id > sa2 . id
            WHERE sa1.created_at > '2025-02-18'
        ");

        $this->info("წაიშალა $deletedRows ჩანაწერი");

    }
}
