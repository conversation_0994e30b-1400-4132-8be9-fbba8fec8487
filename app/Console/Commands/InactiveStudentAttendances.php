<?php

namespace App\Console\Commands;

use App\Models\Lectures\StudentAttendance;
use App\Models\Reestry\Student\Student;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class InactiveStudentAttendances extends Command
{
    const STUDENT_FINANCE_DISABLE_STATUS = 9;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendances:inactive-student';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Inactive Student Attendances';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = Carbon::now()->format('Y-m-d');
        $students = Student::with(['lectures' => function($lectures) use($today){
                $lectures->where('lectures.lecture_date', $today);
            }])
            ->where('status_id', self::STUDENT_FINANCE_DISABLE_STATUS)
            ->whereRelation('lectures', 'lectures.lecture_date', '=', $today)
            ->where('program_id', '!=', 1052)
            ->get()
        ;

        foreach ($students as $student)
        {
            if ($student->lectures->isNotEmpty())
            {
                foreach ($student->lectures as $lecture)
                {
                    if (!$lecture->attendances->where('student_id', $student->id)->isNotEmpty())
                    {
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', '1970-01-01 ' . $lecture->start_time);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', '1970-01-01 ' . $lecture->end_time);
                        $attendanceCount = (int) ceil($startDate->diffInMinutes($endDate) / 60);
                        foreach (range(1, $attendanceCount) as $value)
                        {
                            StudentAttendance::query()->create([
                                'student_id'  => $student->id,
                                'lecture_id'  => $lecture->id,
                                'nth_lecture' => $value,
                                'cron_marker' => true,
                                'syllabus_id' => $lecture?->syllabus?->id
                            ]);
                        }
                    }
                }
            }
        }

        return CommandAlias::SUCCESS;
    }
}
