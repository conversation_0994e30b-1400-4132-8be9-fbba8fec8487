<?php

namespace App\Console\Commands;

use App\Models\Curriculum\Curriculum;
use App\Models\Curriculum\CurriculumLecture;
use App\Models\Curriculum\CurriculumLectureTime;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use App\Models\Setting;
use App\Models\Survey\SurveyActivation;
use App\Models\Syllabus\Syllabus;
use App\Models\SyllabusStudentGuest;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SurveyActivationCommand extends Command
{
    protected $signature = 'activation:survey';

    protected $description = 'Command description';

    public function handle()
    {
        $flowId = Setting::query()->where('key', 'current_semester')->first()->value;

        $syllabusIds = Curriculum::query()
            ->where('end_date', '<=', Carbon::now()->addDays(7))
            ->where('flow_id', $flowId)
            ->pluck('syllabus_id')
        ;

        $syllabusIds = Syllabus::query()
            ->whereIn('id', $syllabusIds)
            ->whereHas('learnYear.program', function ($query){
                $query->whereIn('academic_degree_id', [1,2]);
            })
            ->pluck('id')
        ;

        $this->activate($syllabusIds, 1);

        $syllabusIds = Syllabus::query()
            ->whereIn('id', $syllabusIds)
            ->whereHas('learnYear.program', function ($query){
                $query->whereIn('academic_degree_id', [4]);
            })
            ->pluck('id')
        ;

        $this->activate($syllabusIds, 2);
    }


    public function activate($syllabusIds, $surveyId): void
    {
        SurveyActivation::query()->whereIn('syllabus_id', $syllabusIds)->update(['status' => 1]);

        foreach ($syllabusIds as $syllabusId) {
            $curriculaIds = Curriculum::query()
                ->where('syllabus_id', $syllabusId)
                ->pluck('id');

            $curriculumLecturerIds = CurriculumLecture::query()
                ->whereIn('curriculum_id', $curriculaIds)
                ->pluck('id');

            $curriculumLecturerTimes = CurriculumLectureTime::query()
                ->select(['id', 'lecturer_id','curriculum_lecture_id'])
                ->with('groups')
                ->whereIn('curriculum_lecture_id', $curriculumLecturerIds)
                ->get();

            $groupIds = $curriculumLecturerTimes->pluck('groups.*.student_group_id')->flatten()->all();

            $students = Student::query()
                ->select(['id', 'group_id','name','surname'])
                ->orderByDesc('surname')
                ->whereIn('group_id', $groupIds)
                ->whereIn('status_id', [1,9])
                ->get('id');

            $guests = SyllabusStudentGuest::query()
                ->select(['id', 'syllabus_id', 'student_group_id', 'student_id'])
                ->where('syllabus_id', $syllabusId)
                ->whereIn('student_group_id', $groupIds)
                ->get('student_id');

            $curriculumLecturerTimes = $curriculumLecturerTimes->groupBy('lecturer_id');

            foreach ($curriculumLecturerTimes as $lecturerId => $times) {
                $insertData = [];
                $userIds = [];
                foreach ($times as $item) {
                    $studentIdsHistory = StudentSyllabusHistory::query()
                        ->select(['id', 'syllabus_id', 'student_id'])
                        ->where('syllabus_id', $syllabusId)
                        ->pluck('student_id');

                    if ($item->groups->isEmpty()) {
                        $studentIds = $studentIdsHistory;

                    } else {
                        $groupIds = $item->groups->pluck('student_group_id');
                        $studentIdsFromStudents = $students->whereIn('group_id', $groupIds)->pluck('id');
                        $studentIdsFromGuest = $guests->whereIn('student_group_id', $groupIds)->pluck('student_id');
                        $studentIds = collect([...$studentIdsFromStudents, ...$studentIdsFromGuest])->unique()->values();
                        $studentIds = $studentIds->intersect($studentIdsHistory);
                    }

                    $newUserIds = Student::query()
                        ->select(['id', 'user_id'])
                        ->whereIn('id', $studentIds)
                        ->pluck('user_id');

                    $userIds = collect([...$userIds, ...$newUserIds])->unique()->values();

                }
                foreach ($userIds as $userId) {
                    $insertData[] = [
                        'user_id' => $userId,
                        'survey_id' => $surveyId,
                        'syllabus_id' => $syllabusId,
                        'lecturer_id' => $lecturerId,
                        'status' => 1,
                        'learn_semester' => Setting::where('key', 'current_semester')->pluck('value')->first(),
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
                SurveyActivation::query()->insert($insertData);
            }
        }
    }
}
