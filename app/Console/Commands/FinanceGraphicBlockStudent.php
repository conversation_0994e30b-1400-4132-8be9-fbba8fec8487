<?php

namespace App\Console\Commands;

use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\FinanceLog;
use App\Models\Reestry\Student\Student;
use App\Models\Reestry\Student\StudentStatusList;
use App\Models\Severity;
use App\Services\FinanceService;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class FinanceGraphicBlockStudent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:graphic-block-student';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $financeScheduler = FinanceScheduler::where('is_active', '=', 1);
        if ($financeScheduler->exists()) {
            $deadlineStudents = $financeScheduler->whereHas('calendars', function ($query) {
                return $query->where('end_date', '=', now()->subDays(2)->format('Y/m/d'));
            })->get();
            foreach ($deadlineStudents as $deadlineStudent) {
                $financeCalendarStartDate = FinanceCalendar::where(
                    'finance_scheduler_id',
                    '=',
                    $deadlineStudent->id
                )
                    ->orderBy('start_date', 'ASC')
                    ->first()->start_date;
                $currentPayableMoney = FinanceCalendar::where(
                    'end_date',
                    '<=',
                    now()->subDays(2)->format('Y/m/d')
                )
                    ->where('finance_scheduler_id', '=', $deadlineStudent->id)
                    ->sum('amount');
                $currentPayedMoney = FinanceLog::whereDate(
                    'tarigi',
                    '>=',
                    $financeCalendarStartDate
                )
                    ->where(
                        'piradi_nom',
                        '=',
                        (new FinanceService(userId: $deadlineStudent->user_id))->getStudentIdentityNumber()
                    )
                    ->sum('tanxa');
                $student = Student::whereUserId($deadlineStudent->user_id)->first();
                if ($currentPayedMoney < $currentPayableMoney) {
                    $student->update([
                        'status_id' => StudentStatusList::STOPPED
                    ]);
                    $notificationService = new NotificationService();
                    $notification = $notificationService->sendNotification(
                        userId: $deadlineStudent->user_id,
                        severityId: Severity::CRITICAL,
                        text: "სტუდენტური სტატუსი შეგიჩერდათ , გადაიხადეთ დავალიანება აღსადგენად!"
                    );
                    $notificationService->publish($notification);
                }

            }
        }
    }
}
