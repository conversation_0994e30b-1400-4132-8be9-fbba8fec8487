<?php

namespace App\Console\Commands;

use App\Jobs\Curriculum\FillLectureStudentGroupsJob;
use App\Models\Curriculum\Curriculum;
use Carbon\Carbon;
use Illuminate\Console\Command;

class FillLectureGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lecture-groups:fill-students';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill lecture groups with students';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $curriculums = Curriculum::where('start_date', '>=', Carbon::now()->subDays(10))->get();
//თუ დაწყების დღემდე დარჩენილია 10 დღე, იმ დატას წამოიოღებს, მაგალითად start_date თუ არის 12.05.2023 მაშინ დატა რომ წამოვიდეს მეორე თარიღში უნდა იყოს 02.05.2023, 10 დღით უკან რიცხვი. ანუ ეს ნიშნავს 10 დღით ადრე ჩაიტვირთოს ეს ბრძანება სანამ საგანი დაიწყება
        foreach ($curriculums as $curriculum) {
            foreach ($curriculum->lectures as $lecture) {
                FillLectureStudentGroupsJob::dispatch($lecture);
            }
        }

        return Command::SUCCESS;
    }
}
