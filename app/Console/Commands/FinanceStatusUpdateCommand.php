<?php

namespace App\Console\Commands;

use App\Models\Finance\Finance;
use App\Models\Finance\FinanceCalendar;
use App\Models\Finance\FinanceScheduler;
use App\Models\FinanceLog;
use App\Models\FinanceStudentStatusLog;
use App\Models\Reestry\Student\Student;
use App\Models\Setting;
use App\Models\TempPersonalId;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class FinanceStatusUpdateCommand extends Command
{
    const STUDENT_ACTIVE_STATUS = 1;
    const STUDENT_FINANCE_DISABLE_STATUS = 9;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:status-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Student finance status update';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // break if new data count too low
        $firstContidion = Finance::count() < 149000;
        $secondContidion = FinanceLog::count() < 40000;
        if ($firstContidion or $secondContidion)
        {
            $mailContent1 = false ? 'finance_new ჩანაწერი არ არის სრულად' : '';
            $mailContent2 = true ? 'finance_log ჩანაწერი არ არის სრულად' : '';
            $destinationEmail = '<EMAIL>';
            $mailContent = PHP_EOL . $mailContent1 . PHP_EOL . $mailContent2;
            Mail::raw($mailContent, function ($message) use($destinationEmail){
                $message->to($destinationEmail)
                    ->subject($this->description);
            });

            return 0;
        }

        $tempPersonalIds = TempPersonalId::all()->pluck('personal_id');
        $students = Student::query()
            ->whereIn('status_id', [self::STUDENT_ACTIVE_STATUS, self::STUDENT_FINANCE_DISABLE_STATUS])
            ->when($tempPersonalIds->isNotEmpty(), function ($query) use ($tempPersonalIds){
                return $query->whereNotIn('personal_id', $tempPersonalIds);
            })
            ->whereNotIn('program_id', [1052, 1056, 1057])
            ->get();

        $startLearnYear = Setting::where('key', 'finance_start_learn_year')->pluck('value')->first();
        $learnYear = Setting::where('key', 'learn_year')->pluck('value')->first();
        $this->startLearnYear = $startLearnYear;
        $this->learnYear = $learnYear;
        $nextYear = $learnYear + 1;

        foreach ($students as $student) {
            $financeScheduler = FinanceScheduler::whereUserId($student->user_id)
                ->where('is_active', 1)
                ->first();

            if ($financeScheduler) {
                $mappedSchedulerData = FinanceCalendar::select('id', 'finance_scheduler_id', 'start_date', 'end_date', 'amount')
                    ->whereFinanceSchedulerId($financeScheduler->id)
                    ->where('end_date', '<=', Carbon::now())
                    ->get();

                $this->changeStatus($mappedSchedulerData, $student, false);
                continue;
            }
            $finances = Finance::where('piradi_nom', $student->personal_id)
                ->where('ProgramID', $student->program_id)
                ->where('saswavlo_weli', $learnYear)
                ->get();

            if ($finances) {
                $finances = $finances->map(function ($finance) use ($learnYear, $nextYear) {
                    if ($finance->Qrter == 1) {
                        $startDate = "01/09/{$learnYear}";
                        $endDate = "04/10/{$learnYear}";
                    } elseif ($finance->Qrter == 2) {
                        $startDate = "02/10/{$learnYear}";
                        $endDate = "16/12/{$learnYear}"; // შესაჩერებელი თარიღი
                    } elseif ($finance->Qrter == 3) {
                        $startDate = "02/12/{$learnYear}";
                        $endDate = "24/03/{$nextYear}";
                    } elseif ($finance->Qrter == 4) {
                        $startDate = "02/03/{$nextYear}";
                        $endDate = "04/06/{$nextYear}";
                    }
                    return [
                        'start_date' => $startDate ?? null,
                        'end_date' => $endDate ?? null,
                        'amountToPay' => $finance->kvartlis_jami ?? 0,
                        'paidMoney' => 0,
                    ];
                });

                $this->changeStatus($finances, $student, true);
            }
        } // end for
        return 0;
    }

    public function changeStatus($schedule, $student, $standard)
    {
        $totalPaymentMoney = 0;

        if ($standard)
        {
            foreach ($schedule as $item) {
                $endDate = Carbon::createFromFormat('d/m/Y', $item['end_date']);

                if (Carbon::now() > $endDate) {
                    $totalPaymentMoney = $item['amountToPay'];
                } else {
                    break;
                }
            }
        }
        else
        {
            foreach ($schedule as $item) {
                $endDate = Carbon::createFromFormat('d/m/Y', $item['end_date']);
                if (Carbon::now() > $endDate) {
                    $totalPaymentMoney += $item->amount;
                }
            }
            $totalPaymentMoney -= FinanceLog::wherePiradiNom($student->personal_id)
                ->whereBetween('tarigi', [Carbon::createFromFormat('d/m/Y', $this->startLearnYear), now()])
                ->sum('tanxa') ?? 0
            ;
        }

        if ($totalPaymentMoney <= 0) {
            if ($student->status_id == self::STUDENT_FINANCE_DISABLE_STATUS) {
                $student->update(['status_id' => self::STUDENT_ACTIVE_STATUS]);
                FinanceStudentStatusLog::query()->create([
                    'student_id' => $student->id,
                    'status_id' => self::STUDENT_ACTIVE_STATUS
                ]);
            }
        } else {
            if ($student->status_id == self::STUDENT_ACTIVE_STATUS) {
                $student->update(['status_id' => self::STUDENT_FINANCE_DISABLE_STATUS]);
                FinanceStudentStatusLog::query()->create([
                    'amount' => $totalPaymentMoney,
                    'student_id' => $student->id,
                    'status_id' => self::STUDENT_FINANCE_DISABLE_STATUS
                ]);
            }
            elseif ($student->status_id == self::STUDENT_FINANCE_DISABLE_STATUS){
                FinanceStudentStatusLog::query()
                    ->where('student_id', $student->id)
                    ->where('status_id', self::STUDENT_FINANCE_DISABLE_STATUS)
                    ->orderBy('id','desc')
                    ->take(1)
                    ->update(['amount' => $totalPaymentMoney])
                ;
            }
        }
    }
}
