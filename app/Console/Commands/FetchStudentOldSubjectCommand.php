<?php

namespace App\Console\Commands;

use App\Models\Reestry\Student\StudentOldMark;
use App\Models\Reestry\Student\StudentSyllabusHistory;
use Illuminate\Console\Command;

class FetchStudentOldSubjectCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:student-old-subjects';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $studentOldMarks = StudentOldMark::select('id', 'sagnis_id', 'student_id', 'nishani')->get();
        foreach ($studentOldMarks as $studentOldMark) {
            StudentSyllabusHistory::create([
                'syllabus_id' => $studentOldMark->sagnis_id,
                'is_passed' => $studentOldMark->nishani >= 51,
                'student_id' => $studentOldMark->student_id,
                'point' => $studentOldMark->nishani
            ]);
        }
    }
}
