<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class InsertAssignmentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:import-assignment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $file = public_path('imports/export-active-grades.xlsx');
        ini_set('memory_limit', '512M');

        Excel::import(
            new \App\Imports\OldDatabase\AssignmentImport,
            $file
        );

        return self::SUCCESS;
    }
}
