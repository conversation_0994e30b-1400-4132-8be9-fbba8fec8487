<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Predis\Client;

class ClearRedisData extends Command
{
    protected $signature = 'redis:clear';
    protected $description = 'Clear all data from Redis';

    public function handle()
    {
        $client = new Client();

        // Flush all data from Redis
        $client->flushall();

        $this->info('Redis data cleared successfully.');
    }
}
