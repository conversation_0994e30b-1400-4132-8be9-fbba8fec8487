<?php

namespace App\Console\Commands;

use App\Models\FinanceStudentStatusLog;
use App\Models\Reestry\Student\Student;
use Illuminate\Console\Command;

class BackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:name';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
//        6675
//        $studentId = FinanceStudentStatusLog::query()->where('id', '>', '6675')->pluck('student_id');
//        Student::query()->whereIn('id', $studentId)->update(['status_id' => 9]);
//
//        $this->info('done.');
    }
}
