<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssignmentDuplicateClearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignment:clean-duplicates {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'სტუდენტების დუბლირებული ნიშნების გასუფთავება student_assignments ცხრილში';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('დუბლირებული ნიშნების გასუფთავების დაწყება...');

        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->warn('DRY RUN რეჟიმი - არაფერი არ წაიშლება');
        }

        // ჯერ ვნახავთ რამდენი დუბლირებული ჩანაწერია
        $duplicatesCount = DB::select("
            SELECT COUNT(*) as count FROM (
                SELECT sa1.id
                FROM student_assignments sa1
                INNER JOIN student_assignments sa2
                ON sa1.student_id = sa2.student_id
                AND sa1.assignment_id = sa2.assignment_id
                AND sa1.syllabus_id = sa2.syllabus_id
                AND sa1.id > sa2.id
            ) as duplicates
        ")[0]->count;

        if ($duplicatesCount == 0) {
            $this->info('დუბლირებული ჩანაწერები არ მოიძებნა.');
            return Command::SUCCESS;
        }

        $this->info("მოიძებნა {$duplicatesCount} დუბლირებული ჩანაწერი");

        if ($isDryRun) {
            // Dry run - მხოლოდ ვაჩვენებთ რა წაიშლება
            $duplicates = DB::select("
                SELECT sa1.id, sa1.student_id, sa1.assignment_id, sa1.syllabus_id, sa1.created_at
                FROM student_assignments sa1
                INNER JOIN student_assignments sa2
                ON sa1.student_id = sa2.student_id
                AND sa1.assignment_id = sa2.assignment_id
                AND sa1.syllabus_id = sa2.syllabus_id
                AND sa1.id > sa2.id
                ORDER BY sa1.student_id, sa1.assignment_id, sa1.syllabus_id
                LIMIT 10
            ");

            $this->table(
                ['ID', 'Student ID', 'Assignment ID', 'Syllabus ID', 'Created At'],
                array_map(function($row) {
                    return [$row->id, $row->student_id, $row->assignment_id, $row->syllabus_id, $row->created_at];
                }, $duplicates)
            );

            if (count($duplicates) == 10 && $duplicatesCount > 10) {
                $this->info("... და კიდევ " . ($duplicatesCount - 10) . " ჩანაწერი");
            }

            $this->info("რეალური წაშლისთვის გაუშვით: php artisan assignment:clean-duplicates");
            return Command::SUCCESS;
        }

        $deletedRows = DB::delete("
            DELETE sa1 FROM student_assignments sa1
            INNER JOIN student_assignments sa2
            ON sa1.student_id = sa2.student_id
            AND sa1.assignment_id = sa2.assignment_id
            AND sa1.syllabus_id = sa2.syllabus_id
            AND sa1.id > sa2.id
        ");

        $this->info("წარმატებით წაიშალა {$deletedRows} დუბლირებული ჩანაწერი");

        // ლოგში ჩაწერა
        Log::info("Student assignments duplicates cleaned", [
            'deleted_count' => $deletedRows,
            'executed_at' => now(),
            'command' => 'assignment:clean-duplicates'
        ]);

        return Command::SUCCESS;
    }
}
