<?php

use App\Http\Controllers\API\V1\AssessmentComponentController;
use App\Http\Controllers\API\V1\AuthController;
use App\Http\Controllers\API\V1\CommonController;
use App\Http\Controllers\API\V1\Curriculum\CurriculumController;
use App\Http\Controllers\API\V1\Curriculum\JournalController;
use App\Http\Controllers\API\V1\Curriculum\StudentRegisterController;
use App\Http\Controllers\API\V1\eDoc\DocController;
use App\Http\Controllers\API\V1\eDoc\DocTemplateController;
use App\Http\Controllers\API\V1\ElBookController;
use App\Http\Controllers\API\V1\EventController;
use App\Http\Controllers\API\V1\FinanceController;
use App\Http\Controllers\API\V1\HR\HrAcademicLecturerController;
use App\Http\Controllers\API\V1\HR\HrAdministrationController;
use App\Http\Controllers\API\V1\HR\HrInvitedLecturerController;
use App\Http\Controllers\API\V1\HR\HrStatisticsController;
use App\Http\Controllers\API\V1\Imports\ExcelImportController;
use App\Http\Controllers\API\V1\LibraryLMBController;
use App\Http\Controllers\API\V1\Messages\MessageController;
use App\Http\Controllers\API\V1\News\NewsController;
use App\Http\Controllers\API\V1\PermissionController;
use App\Http\Controllers\API\V1\Reestry\Administration\AdministrationCalendarController;
use App\Http\Controllers\API\V1\Reestry\Administration\AdministrationController;
use App\Http\Controllers\API\V1\Reestry\Administration\AdministrationItemController;
use App\Http\Controllers\API\V1\Reestry\Administration\AdministrationPositionController;
use App\Http\Controllers\API\V1\Reestry\ApplicantsController;
use App\Http\Controllers\API\V1\Reestry\AuditoriumController;
use App\Http\Controllers\API\V1\Reestry\CampusController;
use App\Http\Controllers\API\V1\Reestry\DirectionController;
use App\Http\Controllers\API\V1\Reestry\FlowController;
use App\Http\Controllers\API\V1\Reestry\LearnYearController;
use App\Http\Controllers\API\V1\Reestry\LecturerController;
use App\Http\Controllers\API\V1\Reestry\ProgramController;
use App\Http\Controllers\API\V1\Reestry\SchoolController;
use App\Http\Controllers\API\V1\Reestry\Student\StudentController;
use App\Http\Controllers\API\V1\Reestry\Student\StudentGroupController;
use App\Http\Controllers\API\V1\RegisterForms\BachelorRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\DoctorRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\MasterRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\ProffesionRegisterController;
use App\Http\Controllers\API\V1\RegisterForms\RegisterFormActivateController;
use App\Http\Controllers\API\V1\RegisterForms\TrainingRegisterController;
use App\Http\Controllers\API\V1\RoleController;
use App\Http\Controllers\API\V1\StudentPage\ProfileController;
use App\Http\Controllers\API\V1\LecturerPage\LecturerProfileController;
use App\Http\Controllers\API\V1\Survey\SurveyController;
use App\Http\Controllers\API\V1\SyllabusController;
use App\Http\Controllers\API\V1\SyllabusTrainingController;
use App\Http\Controllers\API\V1\TopicController;
use App\Http\Controllers\API\V1\UserController;
use App\Http\Controllers\API\V1\UserPermissionController;
use App\Http\Controllers\LMBOldDbController;
use App\Http\Controllers\SyllabusProfessionController;
use App\Models\Reestry\Student\Student;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */
//Route::get('/phpinfo', function () {
//    return response()->make(phpinfo(), 200, ['Content-Type' => 'text/html']);
//});

Route::middleware('auth.administrator')
    ->post('/admin/student-auth', [AuthController::class, 'generateStudentToken'])
;

Route::get('semesters', function (){
return \App\Models\Reestry\Flow::query()->orderBy('id','desc')->get();
})->middleware('auth.administrator');
Route::get('lectureIds', function (\Illuminate\Http\Request $request){
    $validated = $request->validate([
        'student_id' => 'required',
        'syllabus_id' => 'required'
    ]);

    $student = \App\Models\Reestry\Student\Student::query()
        ->with('lectures.syllabus')
        ->whereHas('lectures.syllabus', function ($syllabus)use ($validated){
            $syllabus->where('id', $validated['syllabus_id']);
        })
        ->where('id', $validated['student_id'])
        ->get()
        ->pluck('lectures.*.id')
        ->flatten()
        ->toArray();

    dd(implode(',', $student));
})
    ->middleware('auth.administrator')
;


Route::get('/', function () {
    return response()->json(['message' => 'Welcome to GIPA']);
});

//Route::get('/change-password', function () {
//    \App\Models\User\User::find(14)->update(['password' => bcrypt('password')]);
//});
Route::middleware('auth')
    ->get('/check-token-validity', [PermissionController::class, 'checkTokenValidity']);

Route::post('/update-password', [UserController::class, 'updatePassword'])->middleware('auth');

Route::prefix('auth')->controller(AuthController::class)->group(function () {
    Route::post('/login', 'login');
    Route::get('/google', 'redirectToGoogle');
    Route::get('/google/callback', 'handleGoogleCallback');
    Route::get('/logout', 'logout')->middleware('auth');
    Route::get('/forgot-password', 'forgotPassword');
    Route::post('/reset-password', 'resetPassword');
    Route::get('/check', 'check');
});

/**
 * <AUTHOR>
 * Administration Protected Routes
 */
Route::prefix('administration')
    ->middleware(['auth.administrator'])
    ->group(function () {
        Route::post('/student-gpa-calculate', [StudentController::class, 'calculateGpa']);
        Route::post('/student-diploma-download/{lang}', [StudentController::class, 'downloadDiploma']);
        Route::get('/search-students', [StudentController::class, 'searchStudents']); //student search function for journal-add new students in journal
        Route::post('/set-students-in-syllabus', [StudentController::class, 'setStudentsInSyllabus']);
        Route::get('/minor/logs', [StudentController::class, 'minorLogs']);
        Route::get('/minor/export', [StudentController::class, 'minorExport']);

        Route::get('/user/permissions', [UserPermissionController::class, 'index']);
        Route::get('/user/programs', [PermissionController::class, 'programs']);
        Route::get('/user/models', [PermissionController::class, 'modelPermissions']);
        Route::post('/set-permissions', [AuthController::class, 'setPermissions'])->middleware(['auth.super_admin']);
        Route::post('/set-model-full-access', [PermissionController::class, 'setModelFullAccess'])
            ->middleware('auth.super_admin');
        //Route::post('/set-program-access', [PermissionController::class, 'setProgramPermissions'])->middleware('auth.super_admin');
        Route::get('/personal-info', [UserController::class, 'personalInformation']);
        Route::put(
            '/update-personal-info/{id}',
            [UserController::class, 'updatePersonalInformation']
        );
        Route::get('/calendar', [AdministrationCalendarController::class, 'index']);
        Route::put('/calendar/{lecture}', [AdministrationCalendarController::class, 'updateLecture']);
        //Syllabus check for syllabus code
        Route::get('/syllabus-code/{flow}/{semester}', [SyllabusController::class, 'checkSequence']);
        Route::get('/syllabus-checkPrerequisites/{flow}/{keyword?}', [SyllabusController::class, 'checkPrerequisites']);
        Route::get('/syllabus-lecturer/{keyword?}', [SyllabusController::class, 'findLecturer']);
        Route::get('/syllabus/export/{syllabus}/{lang}', [SyllabusController::class, 'exportAsPdf']);
//
        //applicants filter
        Route::get('/applicants', [ApplicantsController::class, 'checkRouteForFilter']);
        //copy applicant to students registry
        Route::post('/copy-applicant-to-students', [ApplicantsController::class, 'copyToStudentsTable']);
        //import bachelors in temp table
        Route::post('/bachelor-import', [ExcelImportController::class, 'importBachelors']);
        Route::post('/students-import', [ExcelImportController::class, 'importStudents']);
        Route::post('/lecturers-import', [ExcelImportController::class, 'importLecturers']);
        Route::post('/temp-users-import', [ExcelImportController::class, 'importTempUsers']);
        Route::post('/administrations-import', [ExcelImportController::class, 'importAdministration']);
        //Start journal
//        Route::get('/journal/filters', [JournalController::class, 'filterDataList']);
//        Route::get('/journal', [JournalController::class, 'index']);
//        Route::get('/journal/{id}', [JournalController::class, 'show']);
        Route::get('/temp-users-export', [UserController::class, 'tempUsersExport']);
        Route::post('/update-password', [UserController::class, 'updateUserPassword']);
        Route::get('/student-current-semester/{id}', [StudentController::class, 'viewStudentSubjects']); //student current semester subjects
        Route::get('/student-current-missed-hours/{id}/{syllabusId}', [StudentController::class, 'viewStudentMissedLectures']); // student current semester missed lectures
        Route::get('/student-finances/{id}', [FinanceController::class, 'studentFinances']);
        Route::post('/student-diploma/{id}', [FinanceController::class, 'studentDiploma']);
    });


/**
 * <AUTHOR> Demetrashvili , David Lekveishvili
 * @description Syllabus dashboard some endpoints.
 */

Route::prefix('syllabus')
    ->middleware('auth.adminorlecturer') //TODO:საჭიროა ადმინისტრატორზე და ლექტორზე გაიწეროს, ლექტორზე არ მუშაობდა წინა ვერსია
    ->controller(SyllabusController::class)
    ->group(function () {
        Route::get('/export/{syllabus}/{lang}', 'exportAsPdf');
        Route::get('/show/{syllabus}/{lang}', 'showPDF');
        Route::get('/students/{syllabus}', 'syllabusStudents');
        Route::get('/lectures/{syllabus}', 'syllabusLectureDates');
        Route::post('/set-student-mark/{syllabus}', 'setStudentMark');
        Route::post('/set-profession-student-mark/{syllabus}', 'setProfessionStudentMark');
        Route::get('/assessments/{syllabus}', 'syllabusAssessments');
        Route::post('/lecture/set-student-attendance/{lecture}', 'setNotPresentedStudents');
        Route::post('lecture/delete', 'lectureDelete');
    });


Route::prefix('syllabus')
    ->middleware('auth.administrator') //TODO:საჭიროა ადმინისტრატორზე და ლექტორზე გაიწეროს, ლექტორზე არ მუშაობდა წინა ვერსია
    ->controller(SyllabusController::class)
    ->group(function () {
        Route::post('/syllabusHistory/edit', 'editSyllabusHistory'); //TODO::განვლილი საგნის რედაქტირება
        Route::post('/syllabusHistory/create', 'createSyllabusHistory');
        Route::post('/syllabusHistory/delete', 'deleteSyllabusHistory');
        Route::post('/externalSyllabus/create', 'createExternalSyllabus');
        Route::post('/externalSyllabus/delete', 'deleteExternalSyllabus');
        Route::get('/syllabusHistories', 'syllabusHistories'); //TODO::აქ მოდის სტუდენტის ყველა განვლილი საგანი
        Route::get('/syllabus/list', 'listSyllabus'); //სტუდენტის ლისტი ნაკადების მიხედვით
        Route::post('/assignment/set-dates', 'setExamDates'); //TODO::სწორად არ მუშაობს, დეითებს არასწორად სვამს
        Route::post('/assignment/createOrUpdate', 'createOrUpdateAssignment'); //TODO::სილაბუსის კომპონენტის რედაქტირება ისე რომ დაწერილი ნიშნები არ წაიშალოს
        Route::delete('/assignment/delete/{id}', 'deleteAssignment'); //TODO::სილაბუსის კომპონენტის წაშლა
        Route::post('/copy', 'syllabusCopy'); // ერთი სილაბუსის კოპირება, შესაძლებელია სახელის ცვლილება და ნაკადის ცვლილება
        Route::post('/copyByLearnYear', 'syllabusCopyByLearnYear'); //ნაკადის მიხედვით კოპირება ყველა სილაბუსის
        Route::post('deleteLecturerSyllabus', [StudentRegisterController::class, 'deleteLecturerSyllabus']);
    });


//fetch created syllabuses for journal
Route::get('/journal', [SyllabusController::class, 'subjectList'])->middleware('auth.administrator');

Route::prefix('/curriculum')
    ->middleware('auth.administrator')
    ->controller(CurriculumController::class)
    ->group(function () {
        Route::post('/', 'save');
        Route::get('/free-times', 'getFreeTimes');
        Route::get('/{syllabus}', 'index');
        Route::get('/copy/{target}/{source}', 'copyCurriculum');
        Route::post('/remove-students', 'removeStudents');
        Route::post('/update-lectures', 'updateCurriculumLectures');
        Route::post('/delete/{curriculum}', 'deleteCurriculum');
        Route::get('/lecture-times/{syllabusId}', 'curriculumLectureTimes');
    });

Route::prefix('notification')->middleware('auth')->controller(NotificationController::class)->group(function () {
    Route::get('/', 'getNotifications');
    Route::get('/header', 'getNotificationsHeader');
    Route::post('/seen', 'seen');
    Route::get('/notifications/{userId}', 'inbox');
});

/**
 * <AUTHOR> Lekveishvili
 * @description Lecturer dashboard endpoints.
 */

Route::prefix('lecturer')
    ->middleware('auth.lecturer')
    ->controller(LecturerController::class)->group(function () {
        Route::get('/subjects', 'lecturerSubjects');
        Route::get('/lectures/today', 'todayLectures'); //მიმდინარე დღის ცხრილი
        Route::get('/lectures/week', 'weekLectures'); //მიმდინარე კვირის ცხრილი
        Route::get('/lectures/semester', 'semesterLectures'); //მიმდინარე სემესტრის ცხრილი
        //Route::get('/semester/lectures', 'lecturerLectures');
        Route::get('/calendar', [AdministrationCalendarController::class, 'index']);
    });
Route::get('lecturer/syllabus-lectures/{syllabusId}', [LecturerController::class, 'syllabusLectures'])->middleware('auth.administrator');
Route::post('lecturer/syllabus-lectures/{syllabusId}', [LecturerController::class, 'updateSyllabusGlobalParameters'])->middleware('auth.administrator');
Route::prefix('lecturer')
    ->name('lecturer')
    ->middleware('auth.lecturer')
    ->group(function () {
        Route::get('/profile', [LecturerProfileController::class, 'index']);
        Route::put('/profile', [LecturerProfileController::class, 'update']);
        Route::get('/syllabus/{syllabus}', [StudentController::class, 'showSyllabus']);
    });

/**
 * <AUTHOR> Lekveishvili
 * @description Student dashboard endpoints.
 */
Route::get('/library-lmb', [LibraryLMBController::class,'index'])->middleware('auth');
Route::prefix('student')
    ->controller(StudentController::class)
    ->middleware('auth.student')
    ->group(function () {
        Route::get('/subjects', 'studentSubjects');
        Route::get('/passed-subjects', 'passedSubjects');
        Route::get('/lectures/today', 'todayLectures');
        Route::get('/missed-lectures/{syllabusId}', 'missedLectures');
        Route::get('/syllabus/{syllabus}', 'showSyllabus');
        Route::get('/lectures/week', 'weekSchedule');
        Route::get('/missed-lecture/{syllabusId}', 'studentSyllabusMissedLectures');
        Route::get('/syllabi', 'syllabi');
        Route::get('/lecturer/{id}', 'viewLecturer');
        Route::get('/syllabusHistories', 'syllabusHistories');
        Route::post('/vote-minor', 'voteMinor');

        //Route::get('/lectures', 'studentLectures');
    });

//Route::post('/stress-test', [StudentRegisterController::class, 'register']);

Route::prefix('student')
    ->middleware('auth.student')
    ->name('student')
    ->group(function () {
        Route::get('/profile', [ProfileController::class, 'index']);
        Route::put('/profile', [ProfileController::class, 'update']);
//        Route::get('/open-registrations', [StudentRegisterController::class, 'index']);
        Route::get('/curriculum/selection', [StudentRegisterController::class, 'curriculumSelection']);
        Route::post('/register-on-lecture', [StudentRegisterController::class, 'register']);
        Route::post('/delete-registration', [StudentRegisterController::class, 'deleteRegistration']);
        Route::get('/calendar/ka', [AdministrationCalendarController::class, 'index']);
        Route::get('/calendar/en', [AdministrationCalendarController::class, 'indexEn']);
        Route::get('/finances', [FinanceController::class, 'studentFinances']);
        Route::get('/edoc-templates', [DocTemplateController::class, 'listForStudents']);
        Route::post('/edoc/create', [DocController::class,'store']);
        Route::get('/edoc', [DocController::class,'student']);
        Route::get('/edoc/export/{id}', [DocController::class,'exportPDF']);
        //Route::get('/calendar', [StudentLectureController::class, 'index']); //old response
    });

Route::prefix('syllabus-profession')
    ->middleware('auth.administrator')
    ->controller(SyllabusProfessionController::class)
    ->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/{id}', 'edit');
        Route::post('/update/{syllabusProfessionId}', 'update');
        Route::delete('/{syllabusProfessionId}', 'delete');
    });

Route::prefix('syllabus-tcc')
    ->middleware('auth.administrator')
    ->controller(SyllabusTrainingController::class)
    ->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/{id}', 'edit');
        Route::post('/{id}', 'update');
        Route::delete('/{id}', 'delete');
    });

Route::middleware('auth')
    ->get('/student/filtered-data', [StudentController::class, 'getFilteredData']);

/**
 * <AUTHOR> Lekveishvili
 * @description Finance endpoints
 */

Route::prefix('finances')
    ->middleware(['auth.administrator'])
    ->controller(FinanceController::class)
    ->group(function () {
        Route::get('/', 'index');
        Route::get('/total-price', 'totalPrice');
        Route::get('/periods', 'periods');
        Route::get('/additional-data', 'additionalData');
        Route::prefix('calendar')->group(function () {
            Route::get('/', 'calendar');
            Route::post('/request', 'requestScheduler');
            Route::post('/status-update', 'schedulerStatusUpdate');
            Route::delete('/delete', 'delete');
        });
        Route::get('/check-finance-log', 'checkFinanceLog');
        Route::get('/student', 'studentFinances');
    });

Route::get('/finances/calendar', [FinanceController::class, 'calendar'])->middleware('auth');
Route::post('/finances/calendar/request', [FinanceController::class, 'requestScheduler'])->middleware('auth');
//Check students statuses
Route::get('allStudentFinanceStatusLog', [StudentController::class, 'allStudentFinanceStatusLog'])->middleware('auth.administrator');
//http://localhost:8000/api/allStudentFinanceStatusLog?export=1&period_from=2023-11-01&period_to=2023-11-08&status_id=9
//შეჩერებულები
//
//http://localhost:8000/api/allStudentFinanceStatusLog?export=1&period_from=2023-11-01&period_to=2023-11-08&status_id=1
//აქტიურები

Route::get('/finance-statement/{id?}', [DocController::class,'studentSchedulers'])->middleware('auth.administrator');
//check register link is valid or not
Route::get('/checkUrl', [ApplicantsController::class, 'checkUrl']);
//
Route::get('/assessment-component-list', [AssessmentComponentController::class, 'list'])->middleware('auth');
//News
Route::get('/news-main', [NewsController::class, 'fetchNewsForMain'])->middleware('auth');//news for main page [student,lecturer]
Route::get('/news-page', [NewsController::class, 'fetchNews'])->middleware('auth');//all news page
Route::get('/news/{id}', [NewsController::class, 'show'])->middleware('auth');//show news
Route::post('/check-bachelor', [BachelorRegisterController::class, 'checkBachelor']);

Route::post(
    '/student-group/attach-students',
    [StudentGroupController::class, 'attachStudents']
)->middleware(['auth.administrator']);

Route::get(
    '/programs-by-academic-degree/{academicDegree}',
    [
        RegisterFormActivateController::class,
        'programByAcademicDegree',
    ]
);

Route::prefix('hr')->middleware('auth.administrator')->group(function () {
    Route::prefix('administration')->group(function () {
        Route::get('/', [HrAdministrationController::class, 'show']);
        Route::post('/', [HrAdministrationController::class, 'store']);
        Route::get('/related-models', [HrAdministrationController::class, 'index']);
        Route::post('/delete-file', [HrAdministrationController::class, 'removeImage']);
        Route::get('/export', [HrAdministrationController::class, 'export']);
    });

    Route::prefix('academic-lecturer')->group(function () {
        Route::get('/', [HrAcademicLecturerController::class, 'show']);
        Route::post('/', [HrAcademicLecturerController::class, 'store']);
        Route::get('/related-models', [HrAcademicLecturerController::class, 'index']);
        Route::post('/delete-file', [HrAcademicLecturerController::class, 'removeImage']);
        Route::get('/export', [HrAcademicLecturerController::class, 'export']);
    });

    Route::prefix('invited-lecturer')->group(function () {
        Route::get('/', [HrInvitedLecturerController::class, 'show']);
        Route::post('/', [HrInvitedLecturerController::class, 'store']);
        Route::get('/related-models', [HrInvitedLecturerController::class, 'index']);
        Route::post('/delete-file', [HrInvitedLecturerController::class, 'removeImage']);
        Route::get('/export', [HrInvitedLecturerController::class, 'export']);
    });

    // HR Statistics endpoint
    Route::get('/statistics', [HrStatisticsController::class, 'index']);
});


Route::prefix('edoc')->middleware('auth.administrator')->controller(DocController::class)->group(function () {
    Route::get('/inbox/{id?}', 'adminInbox');
    Route::delete('/inbox/{id?}', 'adminInbox');
    Route::get('/sent/{id?}', 'adminSent');
    Route::delete('/sent/{id?}', 'adminSent');
    Route::get('/open-time/{id}', 'openTime');
    Route::post('/create', 'store'); //for everyone, include user_id when creating administrator
    Route::put('/update/{id}', 'update'); //for admin
    Route::get('/view/{id}', 'view');
    Route::get('/export/{id}', 'exportPDF');
    Route::get('/edoc/delete/{id}', 'delete');
    Route::post('/student/edit/scheduler', 'updateStudentScheduler'); //სტუდენტის ფინანსური განცხადების სტატუსის განახლება
});
Route::get('/surveys/analysis', [SurveyController::class, 'analysis']);
Route::prefix('surveys')
    ->middleware('auth')
    ->controller(SurveyController::class)
    ->group(function () {
        Route::post('/activation/set', 'setActivation');
        Route::get('/survey/status','surveysForSubjects');
        //Route::get('/analysis', 'analysis');
        Route::get('/', 'index');
        Route::post('/', 'store');
        Route::get('/{survey}', 'show');
        Route::get('/{survey}/edit', 'edit');
        Route::put('/{survey}', 'update');
        Route::delete('/{survey}', 'destroy');
        Route::post('/insert', 'insert');
    });

/**
 * <AUTHOR> Lekveishvili
 * @description Message endpoints
 */
Route::prefix('/messages')
    ->middleware('auth')
    ->controller(MessageController::class)
    ->group(function () {
        Route::get('/lecture-groups', 'lectureGroups');
        Route::get('/send-message-by-lecturer-lecture-group', 'sendMessageByLecturerLectureGroup');
        Route::get('/sidebarLinks', 'messageCount');
        Route::get('/unreadCount', 'unreadCount');
        Route::get('/inbox', 'incomingMessages');
        Route::get('/favorites', 'favoriteMessages');
        Route::get('/trash', 'deletedMessages');
        Route::get('/sent', 'sentMessages');
        Route::post('/send', 'sendMessage');
        Route::get('/{message:id}', 'getMessages');
        Route::post('/status/change', 'changeMessageStatus');
        Route::get('/filter/programs', 'filterByPrograms');
        Route::get('/filter/schools', 'filterBySchools');
        Route::get('/search/receiver', 'search');
        Route::delete('/delete/{message_id}', 'deleteMessage');
        Route::post('/empty-trash', 'emptyTrash');
        Route::get('/message-addresses/{id}', 'messageAddresses');
    });

Route::middleware('auth.adminorlecturer')
    ->get('excel/syllabus/student-marks/{syllabus}', [SyllabusController::class, 'exportStudentMarks']);

Route::middleware('auth.adminorlecturer')
    ->post('excel/syllabus/import/student-marks', [SyllabusController::class, 'importStudentMarks']);
Route::middleware('auth.adminorlecturer')
    ->post('administration/import', [SyllabusController::class, 'importStudentMarks']);
Route::prefix('excel')
    ->middleware('auth.administrator')
    ->group(function () {
        Route::get('/tcc', [TrainingRegisterController::class, 'tccExport']);
        Route::get('/compareSyllabiHours', [SyllabusController::class, 'compareSyllabiHours']);
        Route::get('/program-with-gpa', [SyllabusController::class, 'exportProgramWithGpa']);
        Route::get('/master', [MasterRegisterController::class, 'export']);
        Route::post('/syllabusLiteratureExport', [SyllabusController::class, 'syllabusLiteratureExport'])->name('syllabusLiteratureExport'); //export syllabus literature
        Route::post('/import-sutdent-template', [StudentController::class, 'importStudentTemplate'])->name('importStudentTemplate'); //import student enrollment dates and orders, we can use this function for bulk updates
        Route::get('/administration-positions', [AdministrationPositionController::class, 'exportExcel']);
        Route::get('/administrations', [AdministrationController::class, 'exportExcel']);
        Route::get('/students', [StudentController::class, 'exportExcel']);
        Route::get('/students/{programID}', [StudentController::class, 'exportExcelByProgram']); //export students by program
        Route::get('/student-groups', [StudentGroupController::class, 'exportExcel']);
        Route::get('/auditoriums', [AuditoriumController::class, 'exportExcel']);
        Route::get('/campuses', [CampusController::class, 'exportExcel']);
        Route::get('/directions', [DirectionController::class, 'exportExcel']);
        Route::get('/lecturers', [LecturerController::class, 'exportExcel']);
        Route::get('/lecturerFinance', [LecturerController::class, 'lecturerFinanceExport']);
        Route::get('/programs', [ProgramController::class, 'exportExcel']);
        Route::get('/schools', [SchoolController::class, 'exportExcel']);
        Route::get('/learn-years', [LearnYearController::class, 'exportExcel']);
        Route::get('/administration-items', [AdministrationItemController::class, 'exportExcel']);
        Route::get('/bachelor-registers', [BachelorRegisterController::class, 'exportExcel']);
        Route::get('/bachelor-emails', [BachelorRegisterController::class, 'exportDataForEmails']);
        Route::get('/finances', [FinanceController::class, 'exportExcel']); //http://localhost:8000/api/excel/finances?periods=01/09/2023,08/11/2023&school_id=4&program_id=1&status=1
        Route::get('/studentSyllabusPoints', [StudentController::class, 'studentSyllabusPoints']); //fetch student points, depends on syllabus and learn year. http://localhost:8000/api/excel/studentSyllabusPoints?learn_year_id=1&flow_id=1
        Route::get('/gradeAnalyzeExport', [SyllabusController::class, 'gradeAnalyzeExport']); //fetch student points, depends on syllabus and learn year. http://localhost:8000/api/excel/studentSyllabusPoints?learn_year_id=1&flow_id=1
        //TODO::გასაგკეთებლია ფრონტზე
    });
//import syllabuses and assignments from old database LMB
Route::middleware('auth.administrator')->prefix('/oldDb')->group(function (){
    Route::post('/syllabiImport', [LMBOldDbController::class, 'syllabiImport']);
    Route::post('/assignmentImport', [LMBOldDbController::class, 'assignmentImport']);
});


Route::get('/academic-degrees', fn() => response(\App\Models\Reestry\AcademicDegree::all()));

Route::get('/download-excel', [CommonController::class, 'downloadFile']);
Route::get('/download-pdf', [CommonController::class, 'downloadPDF']);

Route::resource('syllabi', SyllabusController::class)->middleware('auth.administrator');
//start applicant registration form
Route::post('/master-registers/store', [MasterRegisterController::class, 'store']);
Route::post('/bachelor-registers/store', [BachelorRegisterController::class, 'store']);
Route::post('/training-registers/store', [TrainingRegisterController::class, 'store']);
//end
Route::get('student/minors', [StudentController::class, 'minors'])->name('students.minors');
Route::middleware(['auth.administrator'])->group(function () {
    Route::resources([
        'assessments' => AssessmentComponentController::class,
        'auditoriums' => AuditoriumController::class,
        'campuses' => CampusController::class,
        'schools' => SchoolController::class,
        'programs' => ProgramController::class,
        'directions' => DirectionController::class,
        'student-groups' => StudentGroupController::class,
        'administrations' => AdministrationController::class,
        'administration-positions' => AdministrationPositionController::class,
        'administration-items' => AdministrationItemController::class,
        'lecturers' => LecturerController::class,
        'students' => StudentController::class,
        'flows' => LearnYearController::class, // ნაკადები პირიქით არის.
        'learn-years' => FlowController::class, // სასწავლო წლები
        //'syllabi' => SyllabusController::class,
        'topics' => TopicController::class,
        'permissions' => PermissionController::class,
        'roles' => RoleController::class,
        'el-books' => ElBookController::class, //TODO::ახალი ენდფოინთია გასაკეთებელი ლექტორისთვის და სტუდენტისთვის
        'bachelor-registers' => BachelorRegisterController::class,
        'master-registers' => MasterRegisterController::class,
        'doctor-registers' => DoctorRegisterController::class,
        'proffession-registers' => ProffesionRegisterController::class,
        'training-registers' => TrainingRegisterController::class,
        'tcc' => TrainingRegisterController::class,
        'tcc-registers' => TrainingRegisterController::class,
        'register-form-activate' => RegisterFormActivateController::class,
        'events' => EventController::class,
        'edoc-templates' => DocTemplateController::class,
        'news' => NewsController::class,
    ]);
});
